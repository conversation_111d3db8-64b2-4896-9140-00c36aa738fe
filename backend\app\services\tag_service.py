"""
标签业务逻辑服务
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from ..models.tag import Tag
from ..schemas.tag import TagResponse


class TagService:
    """标签业务逻辑服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_tags(self, category_filter: Optional[str] = None) -> List[TagResponse]:
        """
        获取标签列表，支持按分类筛选
        """
        query = self.db.query(Tag).filter(Tag.status == "active")
        
        if category_filter:
            query = query.filter(Tag.category == category_filter)
        
        tags = query.all()
        
        return [
            TagResponse(
                id=tag.id,
                name=tag.name,
                category=tag.category,
                status=tag.status
            )
            for tag in tags
        ]
