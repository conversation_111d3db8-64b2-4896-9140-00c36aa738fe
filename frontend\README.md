# 台账管理系统 - 前端

基于Vue3 + Vite + Element Plus开发的台账管理系统前端应用。

**当前状态**: 核心功能已完成，可投入使用
**完成模块**: 台账查看页面、台账录入页面
**访问端口**: 5174

## 技术栈

- **框架**: Vue 3.4 + TypeScript
- **构建工具**: Vite 6.0
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios

## 功能模块

### ✅ 已完成功能

#### 台账查看模块 (`/ledgers`)
- 台账列表展示和分页
- 高级筛选功能 (年度、状态、部门、标签、关键词)
- Excel导出功能
- 台账详情查看弹窗
- 响应式设计

#### 台账录入模块 (`/ledgers/create`, `/ledgers/edit/:id`)
- 分组表单录入界面
- 序号自动获取和验证
- 表单验证和错误提示
- 草稿保存功能
- 智能填充功能

### ❌ 待开发功能
- 独立的筛选导出页面
- 权限管理界面
- 年度管理界面
- 部门管理界面

## 快速开始

### 1. 环境准备
确保已安装Node.js (版本 >= 16.0)

### 2. 安装依赖
```bash
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```
访问: http://localhost:5174

### 4. 构建生产版本
```bash
npm run build
```

## 项目结构

```
frontend/
├── src/
│   ├── views/              # 页面组件
│   │   ├── LedgerListView.vue    # 台账查看页面
│   │   ├── LedgerInputView.vue   # 台账录入页面
│   │   └── HomeView.vue          # 首页
│   ├── components/         # 通用组件
│   │   ├── LedgerDetail.vue      # 台账详情组件
│   │   └── LedgerDialog.vue      # 台账弹窗组件
│   ├── api/               # API调用
│   │   ├── ledger.ts            # 台账API
│   │   ├── department.ts        # 部门API
│   │   └── tag.ts              # 标签API
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   └── utils/             # 工具函数
├── public/                # 静态资源
└── package.json          # 依赖配置
```

## 开发指南

### 代码规范
- 使用TypeScript进行类型检查
- 遵循Vue3 Composition API规范
- 使用Element Plus组件库
- 统一的代码格式化配置

### API集成
所有API调用统一在 `src/api/` 目录下管理，后端API地址配置在各API文件中。

### 路由配置
- `/` - 首页
- `/ledgers` - 台账查看页面
- `/ledgers/create` - 台账创建页面
- `/ledgers/edit/:id` - 台账编辑页面

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
# 构建
npm run build

# 预览构建结果
npm run preview
```

## 故障排除

### 常见问题
1. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **端口冲突**
   - 修改 `vite.config.ts` 中的端口配置

3. **API连接失败**
   - 确认后端服务已启动 (端口8002)
   - 检查API地址配置

### 开发工具推荐
- **IDE**: VSCode + Volar插件
- **浏览器**: Chrome DevTools
- **调试**: Vue DevTools扩展
