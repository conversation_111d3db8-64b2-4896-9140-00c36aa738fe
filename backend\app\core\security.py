"""
安全工具模块
"""
import hashlib
import secrets
import re
from typing import Optional
from datetime import datetime, timedelta
from passlib.context import CryptContext

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def generate_secure_token(length: int = 32) -> str:
    """生成安全的随机令牌"""
    return secrets.token_urlsafe(length)


def generate_salt(length: int = 16) -> str:
    """生成盐值"""
    return secrets.token_hex(length)


def hash_string(text: str, salt: Optional[str] = None) -> str:
    """哈希字符串"""
    if salt:
        text = text + salt
    return hashlib.sha256(text.encode()).hexdigest()


def validate_password_strength(password: str) -> tuple[bool, list[str]]:
    """验证密码强度"""
    errors = []
    
    # 最小长度检查
    if len(password) < 8:
        errors.append("密码长度至少8位")
    
    # 最大长度检查
    if len(password) > 128:
        errors.append("密码长度不能超过128位")
    
    # 包含数字
    if not re.search(r"\d", password):
        errors.append("密码必须包含至少一个数字")
    
    # 包含字母
    if not re.search(r"[a-zA-Z]", password):
        errors.append("密码必须包含至少一个字母")
    
    # 包含特殊字符（可选，根据需求调整）
    # if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", password):
    #     errors.append("密码必须包含至少一个特殊字符")
    
    return len(errors) == 0, errors


def validate_username(username: str) -> tuple[bool, list[str]]:
    """验证用户名格式"""
    errors = []
    
    # 长度检查
    if len(username) < 3:
        errors.append("用户名长度至少3位")
    
    if len(username) > 50:
        errors.append("用户名长度不能超过50位")
    
    # 格式检查：只允许字母、数字、下划线
    if not re.match(r"^[a-zA-Z0-9_]+$", username):
        errors.append("用户名只能包含字母、数字和下划线")
    
    # 不能以数字开头
    if username[0].isdigit():
        errors.append("用户名不能以数字开头")
    
    return len(errors) == 0, errors


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return True  # 邮箱是可选的
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone(phone: str) -> bool:
    """验证手机号格式"""
    if not phone:
        return True  # 手机号是可选的
    
    # 简单的中国手机号验证
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None


def sanitize_input(text: str) -> str:
    """清理输入文本"""
    if not text:
        return ""
    
    # 移除前后空白
    text = text.strip()
    
    # 移除潜在的危险字符
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        text = text.replace(char, '')
    
    return text


def is_safe_redirect_url(url: str, allowed_hosts: list[str]) -> bool:
    """检查重定向URL是否安全"""
    if not url:
        return False
    
    # 检查是否为相对URL
    if url.startswith('/') and not url.startswith('//'):
        return True
    
    # 检查是否为允许的主机
    for host in allowed_hosts:
        if url.startswith(f'http://{host}') or url.startswith(f'https://{host}'):
            return True
    
    return False


def generate_csrf_token() -> str:
    """生成CSRF令牌"""
    return secrets.token_urlsafe(32)


def rate_limit_key(identifier: str, action: str) -> str:
    """生成速率限制键"""
    return f"rate_limit:{action}:{identifier}"


class SecurityConfig:
    """安全配置类"""
    
    # 密码配置
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    PASSWORD_REQUIRE_DIGIT = True
    PASSWORD_REQUIRE_LETTER = True
    PASSWORD_REQUIRE_SPECIAL = False
    
    # 用户名配置
    USERNAME_MIN_LENGTH = 3
    USERNAME_MAX_LENGTH = 50
    USERNAME_PATTERN = r"^[a-zA-Z0-9_]+$"
    
    # 会话配置
    SESSION_TIMEOUT_MINUTES = 30
    MAX_SESSIONS_PER_USER = 5
    
    # 速率限制配置
    LOGIN_RATE_LIMIT = 5  # 每分钟最多5次登录尝试
    API_RATE_LIMIT = 100  # 每分钟最多100次API调用
    
    # 安全头配置
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
    }


def get_client_ip(request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 返回直接连接的IP
    return request.client.host if hasattr(request, 'client') else "unknown"


def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """掩码敏感数据"""
    if not data or len(data) <= visible_chars:
        return mask_char * len(data) if data else ""
    
    return data[:visible_chars] + mask_char * (len(data) - visible_chars)


def log_security_event(event_type: str, user_id: Optional[int], details: dict, ip_address: str):
    """记录安全事件"""
    # 这里可以集成到日志系统或安全监控系统
    security_log = {
        "timestamp": datetime.utcnow().isoformat(),
        "event_type": event_type,
        "user_id": user_id,
        "ip_address": ip_address,
        "details": details
    }
    
    # 实际实现中应该写入日志文件或发送到监控系统
    print(f"SECURITY_EVENT: {security_log}")


# 常用的安全事件类型
class SecurityEventType:
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    PERMISSION_DENIED = "permission_denied"
    INVALID_TOKEN = "invalid_token"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
