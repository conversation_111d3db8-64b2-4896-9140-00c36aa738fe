/**
 * 台账管理状态存储
 */
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ledgerApi, type Ledger, type LedgerFilters } from '../api/ledger'
import { departmentApi, type Department } from '../api/department'
import { tagApi, type Tag } from '../api/tag'

export const useLedgerStore = defineStore('ledger', () => {
  // 状态
  const ledgers = ref<Ledger[]>([])
  const currentLedger = ref<Ledger | null>(null)
  const departments = ref<Department[]>([])
  const tags = ref<Tag[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 分页状态
  const pagination = ref({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0
  })

  // 筛选状态
  const filters = ref<LedgerFilters>({
    year: new Date().getFullYear()
  })

  // 计算属性
  const hasLedgers = computed(() => ledgers.value.length > 0)
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => error.value !== null)

  // 按状态分组的台账
  const ledgersByStatus = computed(() => {
    const groups: Record<string, Ledger[]> = {}
    ledgers.value.forEach(ledger => {
      if (!groups[ledger.status]) {
        groups[ledger.status] = []
      }
      groups[ledger.status].push(ledger)
    })
    return groups
  })

  // 方法
  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // 获取台账列表
  const fetchLedgers = async (newFilters?: Partial<LedgerFilters>) => {
    try {
      setLoading(true)
      clearError()

      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters }
      }

      console.log('发送API请求，筛选条件:', filters.value)
      const response = await ledgerApi.getList(filters.value)
      console.log('fetchLedgers API原始响应:', response)

      // 修复数据解析逻辑 - 检查响应结构
      if (response && response.data && Array.isArray(response.data)) {
        // 标准响应格式：{data: [], total: number, page: number, ...}
        ledgers.value = response.data
        pagination.value = {
          total: response.total || response.data.length,
          page: response.page || 1,
          limit: response.limit || 20,
          pages: response.pages || Math.ceil((response.total || response.data.length) / (response.limit || 20))
        }
      } else if (Array.isArray(response)) {
        // 直接返回数组的情况
        ledgers.value = response
        pagination.value = {
          total: response.length,
          page: 1,
          limit: 20,
          pages: Math.ceil(response.length / 20)
        }
      } else {
        // 其他情况，设置为空数组
        console.warn('意外的API响应格式:', response)
        ledgers.value = []
        pagination.value = {
          total: 0,
          page: 1,
          limit: 20,
          pages: 0
        }
      }

      console.log('解析后的数据:', {
        ledgersCount: ledgers.value.length,
        pagination: pagination.value,
        firstLedger: ledgers.value[0] || 'No data'
      })
    } catch (err: any) {
      console.error('fetchLedgers错误:', err)
      setError(err.message || '获取台账列表失败')
      // 确保在错误情况下也清空数据
      ledgers.value = []
      pagination.value = {
        total: 0,
        page: 1,
        limit: 20,
        pages: 0
      }
    } finally {
      setLoading(false)
    }
  }

  // 获取所有台账数据（用于导出）
  const fetchAllLedgers = async (exportFilters?: any) => {
    try {
      // 使用当前筛选条件，但设置大的页面大小来获取所有数据
      const allFilters = {
        ...filters.value,
        ...exportFilters,
        page: 1,
        size: 10000 // 设置一个很大的数字来获取所有数据
      }

      console.log('获取所有数据的筛选条件:', allFilters)
      const response = await ledgerApi.getList(allFilters)
      console.log('API响应:', response)

      // 根据实际API响应结构返回数据
      if (response.items) {
        return response.items
      } else if (response.data) {
        return response.data
      } else if (Array.isArray(response)) {
        return response
      } else {
        return []
      }
    } catch (err: any) {
      console.error('获取所有台账数据失败:', err)
      throw new Error(err.message || '获取数据失败')
    }
  }

  // 获取单个台账
  const fetchLedgerById = async (id: number) => {
    try {
      setLoading(true)
      clearError()

      console.log('Store: 开始调用API获取台账，ID:', id)
      const ledger = await ledgerApi.getById(id)
      console.log('Store: API返回的台账数据:', ledger)
      currentLedger.value = ledger
      return ledger
    } catch (err: any) {
      console.error('Store: 获取台账失败:', err)
      setError(err.message || '获取台账详情失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 创建台账
  const createLedger = async (data: any) => {
    try {
      setLoading(true)
      clearError()

      const newLedger = await ledgerApi.create(data)
      ledgers.value.unshift(newLedger)
      pagination.value.total += 1
      return newLedger
    } catch (err: any) {
      setError(err.message || '创建台账失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 更新台账
  const updateLedger = async (id: number, data: any) => {
    try {
      setLoading(true)
      clearError()

      const updatedLedger = await ledgerApi.update(id, data)
      const index = ledgers.value.findIndex(l => l.id === id)
      if (index !== -1) {
        ledgers.value[index] = updatedLedger
      }
      if (currentLedger.value?.id === id) {
        currentLedger.value = updatedLedger
      }
      return updatedLedger
    } catch (err: any) {
      setError(err.message || '更新台账失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 删除台账
  const deleteLedger = async (id: number) => {
    try {
      setLoading(true)
      clearError()

      await ledgerApi.delete(id)
      ledgers.value = ledgers.value.filter(l => l.id !== id)
      pagination.value.total -= 1
      if (currentLedger.value?.id === id) {
        currentLedger.value = null
      }
    } catch (err: any) {
      setError(err.message || '删除台账失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  // 获取部门列表
  const fetchDepartments = async () => {
    try {
      console.log('开始获取部门列表...')
      const response = await departmentApi.getList()
      console.log('部门API原始响应:', response)

      // 修复部门数据解析 - 检查响应结构
      if (response && response.data && Array.isArray(response.data)) {
        departments.value = response.data
      } else if (Array.isArray(response)) {
        departments.value = response
      } else {
        console.warn('意外的部门API响应格式:', response)
        departments.value = []
      }

      console.log('解析后的部门数据:', {
        total: departments.value.length,
        reporting: departments.value.filter(d => d.type === 'reporting').length,
        local: departments.value.filter(d => d.type === 'local').length,
        provincial: departments.value.filter(d => d.type === 'provincial').length
      })
    } catch (err: any) {
      console.error('获取部门列表失败:', err)
      setError(err.message || '获取部门列表失败')
      departments.value = []
    }
  }

  // 获取标签列表
  const fetchTags = async () => {
    try {
      console.log('开始获取标签列表...')
      const response = await tagApi.getList()
      console.log('标签API原始响应:', response)

      // 修复标签数据解析 - 检查响应结构
      if (response && response.data && Array.isArray(response.data)) {
        tags.value = response.data
      } else if (Array.isArray(response)) {
        tags.value = response
      } else {
        console.warn('意外的标签API响应格式:', response)
        tags.value = []
      }

      console.log('解析后的标签数据:', {
        total: tags.value.length,
        work_type: tags.value.filter(t => t.category === 'work_type').length,
        time_cycle: tags.value.filter(t => t.category === 'time_cycle').length,
        priority: tags.value.filter(t => t.category === 'priority').length
      })
    } catch (err: any) {
      console.error('获取标签列表失败:', err)
      setError(err.message || '获取标签列表失败')
      tags.value = []
    }
  }

  // 初始化数据
  const initialize = async () => {
    await Promise.all([
      fetchLedgers(),
      fetchDepartments(),
      fetchTags()
    ])
  }

  return {
    // 状态
    ledgers,
    currentLedger,
    departments,
    tags,
    loading,
    error,
    pagination,
    filters,

    // 计算属性
    hasLedgers,
    isLoading,
    hasError,
    ledgersByStatus,

    // 方法
    setLoading,
    setError,
    clearError,
    fetchLedgers,
    fetchAllLedgers,
    fetchLedgerById,
    createLedger,
    updateLedger,
    deleteLedger,
    fetchDepartments,
    fetchTags,
    initialize
  }
})

// 兼容性导出，确保所有组件都能正确引用
export { useLedgerStore as useCounterStore }
