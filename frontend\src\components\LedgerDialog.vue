<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑台账' : '新建台账'"
    width="900px"
    :close-on-click-modal="false"
    :z-index="3000"
    append-to-body
    @close="handleClose"
    data-testid="ledger-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="序号" prop="sequence_number">
            <el-input
              v-model="form.sequence_number"
              :disabled="!canEditSequence"
              placeholder="请输入序号"
              maxlength="10"
            />
            <div class="form-tip" v-if="!canEditSequence">
              只有主管可以编辑序号
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="责任单位" prop="responsible_dept">
            <el-select
              v-model="form.responsible_dept"
              placeholder="请选择责任单位"
              style="width: 100%"
              filterable
              clearable
              data-testid="dialog-responsible-dept-select"
            >
              <el-option
                v-for="dept in reportingDepartments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.name"
                :data-testid="`dialog-dept-option-${dept.name}`"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="年度" prop="year">
            <el-select
              v-model="form.year"
              placeholder="请选择年度"
              style="width: 100%"
            >
              <el-option label="2025年" :value="2025" />
              <el-option label="2024年" :value="2024" />
              <el-option label="2023年" :value="2023" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="工作事项" prop="work_item">
        <el-input
          v-model="form.work_item"
          type="textarea"
          :rows="3"
          placeholder="请输入工作事项名称"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="具体措施" prop="measures">
        <el-input
          v-model="form.measures"
          type="textarea"
          :rows="4"
          placeholder="请输入具体措施"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="进展情况" prop="progress">
        <el-input
          v-model="form.progress"
          type="textarea"
          :rows="4"
          placeholder="请输入进展情况"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="成效" prop="effectiveness">
        <el-input
          v-model="form.effectiveness"
          type="textarea"
          :rows="3"
          placeholder="请输入成效描述（可选）"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="对口部门" prop="counterpart_depts">
            <el-select
              v-model="form.counterpart_depts"
              multiple
              placeholder="请选择对口部门"
              style="width: 100%"
            >
              <el-option-group label="本单位">
                <el-option
                  v-for="dept in localDepartments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.name"
                />
              </el-option-group>
              <el-option-group label="省分公司">
                <el-option
                  v-for="dept in provincialDepartments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.name"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="关键词标签" prop="tags">
            <el-select
              v-model="form.tags"
              multiple
              placeholder="请选择关键词标签"
              style="width: 100%"
            >
              <el-option-group label="工作类型">
                <el-option
                  v-for="tag in workTypeTags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.name"
                />
              </el-option-group>
              <el-option-group label="时间周期">
                <el-option
                  v-for="tag in timeCycleTags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.name"
                />
              </el-option-group>
              <el-option-group label="优先级">
                <el-option
                  v-for="tag in priorityTags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.name"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option label="草稿" value="draft" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已完成并持续推进" value="completed_ongoing" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="form.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleSaveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { useLedgerStore } from '../stores/counter'
import { useAuthStore } from '../stores/auth'
import type { Ledger, LedgerCreate, LedgerUpdate } from '../api/ledger'

// Props
interface Props {
  modelValue: boolean
  ledger?: Ledger | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  ledger: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [ledger: Ledger]
}>()

// Store
const store = useLedgerStore()
const authStore = useAuthStore()

// Refs
const formRef = ref<FormInstance>()
const saving = ref(false)

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.ledger)

const canEditSequence = computed(() => {
  return authStore.canEditSequence
})

const reportingDepartments = computed(() =>
  store.departments.filter((dept: any) => dept.type === 'reporting')
)

const localDepartments = computed(() =>
  store.departments.filter((dept: any) => dept.type === 'local')
)

const provincialDepartments = computed(() =>
  store.departments.filter((dept: any) => dept.type === 'provincial')
)

const workTypeTags = computed(() =>
  store.tags.filter((tag: any) => tag.category === 'work_type')
)

const timeCycleTags = computed(() =>
  store.tags.filter((tag: any) => tag.category === 'time_cycle')
)

const priorityTags = computed(() =>
  store.tags.filter((tag: any) => tag.category === 'priority')
)

// Form data
const defaultForm = {
  sequence_number: '',
  work_item: '',
  measures: '',
  progress: '',
  effectiveness: '',
  responsible_dept: '',
  counterpart_depts: [] as string[],
  tags: [] as string[],
  remarks: '',
  status: 'draft',
  year: new Date().getFullYear()
}

const form = ref<LedgerCreate & { id?: number }>({ ...defaultForm })

// Form rules
const rules: FormRules = {
  sequence_number: [
    { required: true, message: '请输入序号', trigger: 'blur' },
    { pattern: /^\d{3}$/, message: '序号必须是3位数字', trigger: 'blur' }
  ],
  work_item: [
    { required: true, message: '请输入工作事项', trigger: 'blur' },
    { min: 1, max: 500, message: '工作事项长度在1-500个字符', trigger: 'blur' }
  ],
  measures: [
    { required: true, message: '请输入具体措施', trigger: 'blur' },
    { min: 1, max: 2000, message: '具体措施长度在1-2000个字符', trigger: 'blur' }
  ],
  progress: [
    { required: true, message: '请输入进展情况', trigger: 'blur' },
    { min: 1, max: 2000, message: '进展情况长度在1-2000个字符', trigger: 'blur' }
  ],
  responsible_dept: [
    { required: true, message: '请选择责任单位', trigger: 'change' }
  ],
  counterpart_depts: [
    { required: true, message: '请选择至少一个对口部门', trigger: 'change' }
  ],
  tags: [
    { required: true, message: '请选择至少一个关键词标签', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  year: [
    { required: true, message: '请选择年度', trigger: 'change' }
  ]
}

// Methods
const resetForm = () => {
  form.value = { ...defaultForm }
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const loadLedgerData = () => {
  if (props.ledger) {
    form.value = {
      id: props.ledger.id,
      sequence_number: props.ledger.sequence_number,
      work_item: props.ledger.work_item,
      measures: props.ledger.measures,
      progress: props.ledger.progress,
      effectiveness: props.ledger.effectiveness || '',
      responsible_dept: props.ledger.responsible_dept,
      counterpart_depts: [...props.ledger.counterpart_depts],
      tags: [...props.ledger.tags],
      remarks: props.ledger.remarks || '',
      status: props.ledger.status,
      year: props.ledger.year
    }
  } else {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleSaveDraft = async () => {
  try {
    saving.value = true

    const draftData = { ...form.value, status: 'draft' }

    if (isEdit.value && form.value.id) {
      await store.updateLedger(form.value.id, draftData)
      ElMessage.success('草稿保存成功')
    } else {
      const newLedger = await store.createLedger(draftData)
      emit('success', newLedger)
      ElMessage.success('草稿保存成功')
    }

    visible.value = false
    resetForm()
  } catch (error: any) {
    ElMessage.error(error.message || '保存草稿失败')
  } finally {
    saving.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    saving.value = true

    if (isEdit.value && form.value.id) {
      const updatedLedger = await store.updateLedger(form.value.id, form.value)
      emit('success', updatedLedger)
      ElMessage.success('台账更新成功')
    } else {
      const newLedger = await store.createLedger(form.value)
      emit('success', newLedger)
      ElMessage.success('台账创建成功')
    }

    visible.value = false
    resetForm()
  } catch (error: any) {
    ElMessage.error(error.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    saving.value = false
  }
}

// Watch
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadLedgerData()
  }
})

watch(() => props.ledger, () => {
  if (visible.value) {
    loadLedgerData()
  }
})
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

:deep(.el-select .el-input) {
  width: 100%;
}
</style>
