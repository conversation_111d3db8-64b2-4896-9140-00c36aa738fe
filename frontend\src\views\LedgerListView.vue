<template>
  <div class="ledger-view">

    <!-- 页面标题 -->
    <div class="page-header">
      <h1>台账查看</h1>
      <div class="header-actions">
        <el-button type="success" @click="handleCreateLedger" :disabled="!canEdit">
          <el-icon><Plus /></el-icon>
          台账录入
        </el-button>
        <el-button
          type="primary"
          @click="showCreateDialog = true"
          :disabled="!canEdit"
        >
          <el-icon><Edit /></el-icon>
          快速编辑
        </el-button>
        <el-button
          type="warning"
          @click="handleExport"
          :disabled="!canExport"
        >
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域：左侧筛选 + 右侧表格 -->
    <div class="main-content">
      <!-- 左侧筛选栏 -->
      <div class="filter-sidebar">
        <div class="filter-title">🔍 筛选条件</div>

        <!-- 年度筛选 -->
        <div class="filter-section">
          <div class="filter-label">📅 年度选择</div>
          <el-select v-model="currentFilters.year" @change="handleFilterChange" placeholder="选择年度" style="width: 100%">
            <el-option label="2025年" :value="2025" />
            <el-option label="2024年" :value="2024" />
            <el-option label="2023年" :value="2023" />
          </el-select>
        </div>

        <!-- 责任单位筛选 -->
        <div class="filter-section">
          <div class="filter-label">🏢 责任单位</div>
          <div class="checkbox-group">
            <div class="checkbox-item" v-for="dept in reportingDepartments" :key="dept.id">
              <input
                type="checkbox"
                :id="`dept-${dept.id}`"
                :value="dept.name"
                v-model="selectedDepartments"
                @change="handleDepartmentChange"
              >
              <label :for="`dept-${dept.id}`">{{ dept.name }}</label>
            </div>
          </div>
        </div>

        <!-- 对口部门筛选 -->
        <div class="filter-section">
          <div class="filter-label">🏛️ 对口部门</div>
          <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">本单位：</div>
          <div class="checkbox-group" style="margin-bottom: 15px;">
            <div class="checkbox-item" v-for="dept in localDepartments" :key="dept.id">
              <input
                type="checkbox"
                :id="`local-${dept.id}`"
                :value="dept.name"
                v-model="selectedCounterpartDepts"
                @change="handleCounterpartChange"
              >
              <label :for="`local-${dept.id}`">{{ dept.name }}</label>
            </div>
          </div>
          <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">省分公司：</div>
          <div class="checkbox-group">
            <div class="checkbox-item" v-for="dept in provincialDepartments" :key="dept.id">
              <input
                type="checkbox"
                :id="`prov-${dept.id}`"
                :value="dept.name"
                v-model="selectedCounterpartDepts"
                @change="handleCounterpartChange"
              >
              <label :for="`prov-${dept.id}`">{{ dept.name }}</label>
            </div>
          </div>
        </div>

        <!-- 关键词标签筛选 -->
        <div class="filter-section">
          <div class="filter-label">🏷️ 关键词标签</div>
          <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">工作类型：</div>
          <div class="tag-group" style="margin-bottom: 15px;">
            <div class="tag-item" v-for="tag in workTypeTags" :key="tag.id">
              <input
                type="checkbox"
                :id="`work-${tag.id}`"
                :value="tag.name"
                v-model="selectedTags"
                @change="handleTagChange"
              >
              <label :for="`work-${tag.id}`">{{ tag.name }}</label>
            </div>
          </div>
          <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">时间周期：</div>
          <div class="tag-group">
            <div class="tag-item" v-for="tag in timeCycleTags" :key="tag.id">
              <input
                type="checkbox"
                :id="`time-${tag.id}`"
                :value="tag.name"
                v-model="selectedTags"
                @change="handleTagChange"
              >
              <label :for="`time-${tag.id}`">{{ tag.name }}</label>
            </div>
          </div>
        </div>

        <!-- 填报状态筛选 -->
        <div class="filter-section">
          <div class="filter-label">📊 填报状态</div>
          <div class="checkbox-group">
            <div class="checkbox-item">
              <input type="checkbox" id="status-completed" value="completed" v-model="selectedStatuses" @change="handleStatusChange">
              <label for="status-completed">已完成</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="status-completed-ongoing" value="completed_ongoing" v-model="selectedStatuses" @change="handleStatusChange">
              <label for="status-completed-ongoing">已完成并持续推进</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="status-progress" value="in_progress" v-model="selectedStatuses" @change="handleStatusChange">
              <label for="status-progress">进行中</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="status-draft" value="draft" v-model="selectedStatuses" @change="handleStatusChange">
              <label for="status-draft">草稿</label>
            </div>
          </div>
        </div>

        <!-- 重置按钮 -->
        <div class="filter-actions">
          <el-button @click="resetFilters" style="width: 100%">重置筛选</el-button>
        </div>
      </div>

      <!-- 右侧表格区域 -->
      <div class="table-area">
        <div class="table-header">
          <div class="table-title">台账列表</div>
          <div class="table-actions">
            <el-button
              v-if="selectedRows.length > 0"
              type="danger"
              size="small"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedRows.length }})
            </el-button>
            <el-button
              v-if="selectedRows.length > 0"
              type="primary"
              size="small"
              @click="handleBatchExport"
            >
              导出选中 ({{ selectedRows.length }})
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleExportAll"
            >
              导出全部
            </el-button>
          </div>
          <div class="result-count">筛选结果：共 <span>{{ actualTotal }}</span> 条记录</div>
        </div>

        <div class="table-container">
          <el-table
            :data="store.ledgers"
            :loading="store.isLoading"
            stripe
            style="width: 100%"
            @row-click="handleRowClick"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="sequence_number" label="序号" width="80" />
            <el-table-column prop="work_item" label="重点工作事项" min-width="200" show-overflow-tooltip />
            <el-table-column prop="work_objectives" label="工作目标" min-width="150" show-overflow-tooltip />
            <el-table-column prop="measures" label="工作措施或举措" min-width="150" show-overflow-tooltip />
            <el-table-column prop="progress" label="工作进展" min-width="150" show-overflow-tooltip />
            <el-table-column prop="responsible_dept" label="责任单位" width="120" />
            <el-table-column prop="reporting_unit" label="填报单位或部门" width="120" />
            <el-table-column prop="counterpart_depts" label="对口部门" width="120">
              <template #default="{ row }">
                <div class="dept-display">
                  <span v-if="row.counterpart_depts && row.counterpart_depts.length > 0">
                    {{ row.counterpart_depts.slice(0, 1).join(', ') }}
                    <span v-if="row.counterpart_depts.length > 1" class="more-depts">+{{ row.counterpart_depts.length - 1 }}</span>
                  </span>
                  <span v-else>-</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="accountability_status" label="问责情况" width="100" show-overflow-tooltip />
            <el-table-column prop="difficulties" label="工作中遇到的问题或困难" min-width="150" show-overflow-tooltip />
            <el-table-column prop="next_steps" label="下一步计划或措施" min-width="150" show-overflow-tooltip />
            <el-table-column prop="required_submit_date" label="要求填报日期" width="120">
              <template #default="{ row }">
                <span v-if="row.required_submit_date">
                  {{ formatDate(row.required_submit_date) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="updated_at" label="填报（更新）日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.updated_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="tags" label="关键词勾选" width="150">
              <template #default="{ row }">
                <div class="tag-display">
                  <span v-for="tag in row.tags.slice(0, 2)" :key="tag" class="tag-badge">{{ tag }}</span>
                  <span v-if="row.tags.length > 2" class="more-tags">+{{ row.tags.length - 2 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="remarks" label="备注/对口部门批注" width="150" show-overflow-tooltip />
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click.stop="viewLedger(row)">查看</el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click.stop="editLedgerInForm(row)"
                  :disabled="!canEdit"
                >
                  编辑
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click.stop="deleteLedger(row)"
                  :disabled="!canDelete"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            :current-page="store.pagination.page"
            :page-size="store.pagination.limit"
            :total="store.pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <LedgerDialog
      v-model="showCreateDialog"
      :ledger="editingLedger"
      @success="handleDialogSuccess"
    />

    <!-- 台账详情抽屉 -->
    <!-- <LedgerDetail
      v-model="showDetailDrawer"
      :ledger="viewingLedger"
      @edit="handleDetailEdit"
      @delete="handleDetailDelete"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useLedgerStore } from '../stores/counter'
import { exportToExcel, LEDGER_EXPORT_COLUMNS } from '../utils/export'
import type { Ledger } from '../api/ledger'
import { useAuthStore } from '../stores/auth'
import LedgerDialog from '../components/LedgerDialog.vue'
// import LedgerDetail from '../components/LedgerDetail.vue'

const store = useLedgerStore()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const showCreateDialog = ref(false)
const showDetailDrawer = ref(false)
const editingLedger = ref<any>(null)
const viewingLedger = ref<any>(null)
const currentFilters = ref<any>({
  year: new Date().getFullYear(),
  page: 1,
  size: 20
})

// 筛选状态
const selectedDepartments = ref<string[]>([])
const selectedCounterpartDepts = ref<string[]>([])
const selectedTags = ref<string[]>([])
const selectedStatuses = ref<string[]>([])
const selectedRows = ref<any[]>([])

// 计算属性
const reportingDepartments = computed(() =>
  store.departments?.filter((dept: any) => dept.type === 'reporting') || []
)

const localDepartments = computed(() =>
  store.departments?.filter((dept: any) => dept.type === 'local') || []
)

const provincialDepartments = computed(() =>
  store.departments?.filter((dept: any) => dept.type === 'provincial') || []
)

const workTypeTags = computed(() =>
  store.tags?.filter((tag: any) => tag.category === 'work_type') || []
)

const timeCycleTags = computed(() =>
  store.tags?.filter((tag: any) => tag.category === 'time_cycle') || []
)

// 权限相关计算属性 - 与后端权限API保持一致
const canEditSequence = computed(() => authStore.canEditSequence)
const canEdit = computed(() => {
  // 根据后端权限矩阵，所有角色都可以录入台账
  return authStore.isLoggedIn && (authStore.isAdmin || authStore.isManager || authStore.isUser)
})
const canDelete = computed(() => {
  // 只有管理员可以删除台账
  return authStore.isAdmin || authStore.hasPermission('delete_ledgers')
})
const canExport = computed(() => authStore.canExportData)

// 添加权限状态调试
watch(canEdit, (newVal) => {
  console.log('canEdit权限状态:', newVal, {
    isLoggedIn: authStore.isLoggedIn,
    isAdmin: authStore.isAdmin,
    isManager: authStore.isManager,
    isUser: authStore.isUser,
    userRole: authStore.user?.role
  })
}, { immediate: true })

// 添加状态变化调试
watch(showDetailDrawer, (newVal) => {
  console.log('showDetailDrawer状态变化:', newVal, 'viewingLedger:', viewingLedger.value)
})

watch(showCreateDialog, (newVal) => {
  console.log('showCreateDialog状态变化:', newVal, 'editingLedger:', editingLedger.value)
})

// 添加计算属性确保数据同步
const actualTotal = computed(() => {
  // 优先使用store中的total，如果为0则使用实际数据长度
  return store.pagination.total || store.ledgers.length
})
const canAddComments = computed(() => {
  // 管理员和管理部门可以添加批注
  return authStore.isAdmin || authStore.isManager || authStore.hasPermission('add_comments')
})

// 日期格式化函数
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 状态映射
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': 'info',
    'in_progress': 'warning',
    'completed': 'success',
    'completed_ongoing': 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'in_progress': '进行中',
    'completed': '已完成',
    'completed_ongoing': '已完成并持续推进'
  }
  return statusMap[status] || status
}

// 筛选事件处理
const handleFilterChange = () => {
  updateFilters()
}

const handleDepartmentChange = () => {
  // 支持多选责任单位
  currentFilters.value.responsible_dept = selectedDepartments.value.length > 0 ? selectedDepartments.value : undefined
  updateFilters()
}

const handleCounterpartChange = () => {
  // 对口部门筛选
  currentFilters.value.counterpart_depts = selectedCounterpartDepts.value.length > 0 ? selectedCounterpartDepts.value : undefined
  updateFilters()
}

const handleTagChange = () => {
  // 关键词标签筛选
  currentFilters.value.tags = selectedTags.value.length > 0 ? selectedTags.value : undefined
  updateFilters()
}

const handleStatusChange = () => {
  // 填报状态筛选
  currentFilters.value.status = selectedStatuses.value.length > 0 ? selectedStatuses.value : undefined
  updateFilters()
}

const updateFilters = () => {
  currentFilters.value.page = 1
  store.fetchLedgers(currentFilters.value)
}

const resetFilters = () => {
  selectedDepartments.value = []
  selectedCounterpartDepts.value = []
  selectedTags.value = []
  selectedStatuses.value = []

  currentFilters.value = {
    year: new Date().getFullYear(),
    page: 1,
    size: 20
  }
  store.fetchLedgers(currentFilters.value)
}

const handleCreateLedger = () => {
  router.push('/ledgers/create')
}

const handleRowClick = (row: any) => {
  viewingLedger.value = row
  showDetailDrawer.value = true
}

const editLedger = (ledger: any) => {
  editingLedger.value = ledger
  showCreateDialog.value = true
}

const editLedgerInForm = (ledger: any) => {
  console.log('editLedgerInForm被调用:', ledger)
  try {
    router.push(`/ledgers/edit/${ledger.id}`)
  } catch (error) {
    console.error('editLedgerInForm执行错误:', error)
    ElMessage.error('跳转编辑页面失败')
  }
}

const deleteLedger = async (ledger: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除台账"${ledger.work_item}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await store.deleteLedger(ledger.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  currentFilters.value.size = size
  currentFilters.value.page = 1
  store.fetchLedgers(currentFilters.value)
}

const handleCurrentChange = (page: number) => {
  currentFilters.value.page = page
  store.fetchLedgers(currentFilters.value)
}

const handleDialogSuccess = () => {
  showCreateDialog.value = false
  editingLedger.value = null
  store.fetchLedgers(currentFilters.value)
}

const handleDetailEdit = (ledger: any) => {
  editingLedger.value = ledger
  showDetailDrawer.value = false
  showCreateDialog.value = true
}

// 查看台账详情
const viewLedger = (ledger: any) => {
  console.log('viewLedger被调用:', ledger)
  try {
    viewingLedger.value = ledger
    showDetailDrawer.value = true
    console.log('详情抽屉应该打开:', showDetailDrawer.value)
  } catch (error) {
    console.error('viewLedger执行错误:', error)
    ElMessage.error('打开详情失败')
  }
}

// 导出数据
const handleExport = async () => {
  if (!canExport.value) {
    ElMessage.warning('您没有导出权限')
    return
  }

  if (!store.ledgers || store.ledgers.length === 0) {
    ElMessage.warning('没有数据可以导出')
    return
  }

  try {
    console.log('开始导出，数据条数:', store.ledgers.length)
    const data = store.ledgers.map(formatLedgerForExport)
    console.log('格式化后的导出数据:', data.slice(0, 2)) // 只打印前2条用于调试

    const success = exportToExcel(data, LEDGER_EXPORT_COLUMNS, '台账数据')

    if (success) {
      ElMessage.success(`导出成功！共导出 ${data.length} 条记录`)
    }
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error(error.message || '导出失败')
  }
}



const handleDetailDelete = async (ledger: any) => {
  try {
    await store.deleteLedger(ledger.id)
    showDetailDrawer.value = false
    ElMessage.success('删除成功')
  } catch (error: any) {
    ElMessage.error(error.message || '删除失败')
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条台账记录吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 批量删除
    const deletePromises = selectedRows.value.map(row => store.deleteLedger(row.id))
    await Promise.all(deletePromises)

    selectedRows.value = []
    ElMessage.success('批量删除成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

const handleBatchExport = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的记录')
    return
  }

  try {
    // 格式化选中的数据
    const exportData = selectedRows.value.map(formatLedgerForExport)

    // 导出Excel文件
    exportToExcel(
      exportData,
      LEDGER_EXPORT_COLUMNS,
      `台账数据_选中${selectedRows.value.length}条`
    )

    ElMessage.success(`成功导出 ${selectedRows.value.length} 条记录`)
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  }
}

const handleExportAll = async () => {
  try {
    // 使用当前页面的数据导出（临时方案）
    if (!store.ledgers || store.ledgers.length === 0) {
      ElMessage.warning('没有数据可以导出')
      return
    }

    // 格式化当前页面数据
    const exportData = store.ledgers.map(formatLedgerForExport)

    // 导出Excel文件
    exportToExcel(
      exportData,
      LEDGER_EXPORT_COLUMNS,
      `台账数据_当前页${exportData.length}条`
    )

    ElMessage.success(`成功导出当前页 ${exportData.length} 条记录`)
    ElMessage.info('当前导出的是当前页数据，如需导出全部数据，请调整分页大小或筛选条件')
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error(error.message || '导出失败')
  }
}

// 格式化台账数据用于导出
const formatLedgerForExport = (ledger: any) => {
  return {
    sequence_number: ledger.sequence_number,
    work_item: ledger.work_item,
    work_objectives: ledger.work_objectives || '',
    measures: ledger.measures,
    progress: ledger.progress,
    effectiveness: ledger.effectiveness || '',
    responsible_dept: ledger.responsible_dept,
    counterpart_depts: Array.isArray(ledger.counterpart_depts) ? ledger.counterpart_depts : [],
    tags: Array.isArray(ledger.tags) ? ledger.tags : [],
    status: ledger.status,
    year: ledger.year,
    remarks: ledger.remarks || '',
    created_at: formatDateTime(ledger.created_at),
    updated_at: formatDateTime(ledger.updated_at)
  }
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期
onMounted(async () => {
  await store.initialize()
  await store.fetchLedgers(currentFilters.value)
})
</script>

<style scoped>


.ledger-view {
  width: 100%;
  height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.page-header h1 {
  margin: 0;
  color: #1976d2;
  font-size: 24px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 主要内容区域：左右布局 */
.main-content {
  display: flex;
  flex: 1;
  gap: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 左侧筛选栏 - 参考原型图设计 */
.filter-sidebar {
  width: 300px;
  min-width: 300px;
  max-width: 300px;
  background: white;
  padding: 20px;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 4px rgba(0,0,0,0.1);
  overflow-y: auto;
  flex-shrink: 0;
  box-sizing: border-box;
}

.filter-title {
  font-size: 18px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.filter-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-section:last-child {
  border-bottom: none;
}

.filter-label {
  font-weight: bold;
  margin-bottom: 10px;
  color: #555;
  display: flex;
  align-items: center;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.1);
}

.checkbox-item label {
  cursor: pointer;
  font-size: 14px;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  display: flex;
  align-items: center;
  background: #f0f8ff;
  border: 1px solid #1976d2;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.tag-item:hover {
  background: #e3f2fd;
}

.tag-item.checked {
  background: #1976d2;
  color: white;
}

.tag-item input[type="checkbox"] {
  margin-right: 4px;
  transform: scale(0.9);
}

.filter-actions {
  margin-top: 20px;
}

/* 右侧表格区域 */
.table-area {
  flex: 1;
  min-width: 0;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  flex-wrap: wrap;
  gap: 16px;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-title {
  font-size: 20px;
  font-weight: bold;
  color: #1976d2;
}

.result-count {
  color: #666;
  font-size: 14px;
}

.result-count span {
  font-weight: bold;
  color: #1976d2;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 0 20px;
}

.tag-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-badge {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  white-space: nowrap;
}

.more-tags {
  color: #909399;
  font-size: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .ledger-view {
    height: auto;
    min-height: calc(100vh - 60px);
  }

  .main-content {
    flex-direction: column;
    height: auto;
  }

  .filter-sidebar {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    max-height: 400px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .table-area {
    height: auto;
    min-height: 500px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .filter-sidebar {
    padding: 15px;
  }

  .table-container {
    padding: 0 15px;
  }
}
</style>
