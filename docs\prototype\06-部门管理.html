<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .management-sections {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .section-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-header {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-actions {
            display: flex;
            gap: 10px;
        }

        .section-content {
            padding: 20px;
        }

        .dept-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .dept-table th,
        .dept-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .dept-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #555;
        }

        .dept-table tr:hover {
            background: #f8f9fa;
        }

        .dept-name {
            font-weight: 500;
            color: #333;
        }

        .user-count {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .dept-actions {
            display: flex;
            gap: 5px;
        }

        .add-dept-form {
            display: flex;
            gap: 15px;
            align-items: end;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }

        .form-group-inline {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-group-inline label {
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }

        .form-group-inline input,
        .form-group-inline select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .dept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .dept-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
            transition: all 0.3s;
        }

        .dept-item:hover {
            background: #f0f8ff;
            border-color: #1976d2;
        }

        .dept-item-name {
            font-weight: 500;
            color: #333;
        }

        .dept-item-actions {
            display: flex;
            gap: 5px;
        }

        .warning-notice {
            background: #fff3e0;
            border: 1px solid #ffb74d;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .warning-title {
            font-weight: bold;
            color: #f57c00;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-content {
            color: #f57c00;
            font-size: 14px;
            line-height: 1.5;
        }

        .change-log {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .log-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .log-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-action {
            color: #666;
        }

        .log-time {
            color: #999;
            font-size: 12px;
        }

        .import-export-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .import-export-actions {
            display: flex;
            gap: 10px;
        }

        .stats-info {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>

    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab active">部门管理</a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-container">
        <h2 class="page-title">部门管理页面 (主管专用)</h2>

        <!-- 警告提示 -->
        <div class="warning-notice">
            <div class="warning-title">
                ⚠️ 重要提示
            </div>
            <div class="warning-content">
                部门管理功能仅限主管使用。部门名称变更将影响现有台账数据，请谨慎操作。建议在企业改革期间及时更新部门信息以保持数据一致性。
            </div>
        </div>

        <div class="management-sections">
            <!-- 填报单位管理 -->
            <div class="section-card">
                <div class="section-header">
                    <div class="section-title">
                        🏢 填报单位管理
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportDepts('reporting')">导出列表</button>
                        <button class="btn btn-secondary" onclick="importDepts('reporting')">批量导入</button>
                    </div>
                </div>

                <div class="import-export-bar">
                    <div class="stats-info">
                        当前共有 <strong>12</strong> 个填报单位，关联用户 <strong>28</strong> 人
                    </div>
                    <div class="import-export-actions">
                        <input type="file" id="import-file" style="display: none;" accept=".xlsx,.csv">
                        <button class="btn btn-small btn-secondary" onclick="document.getElementById('import-file').click()">选择文件</button>
                    </div>
                </div>

                <div class="section-content">
                    <table class="dept-table">
                        <thead>
                            <tr>
                                <th style="width: 60px;">序号</th>
                                <th>部门名称</th>
                                <th style="width: 100px;">状态</th>
                                <th style="width: 100px;">关联用户数</th>
                                <th style="width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td class="dept-name">综合办</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">5</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('综合办')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('综合办')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td class="dept-name">人力</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">3</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('人力')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('人力')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td class="dept-name">财务</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">2</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('财务')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('财务')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td class="dept-name">党建部</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">2</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('党建部')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('党建部')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td class="dept-name">纪委办</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">1</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('纪委办')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('纪委办')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td class="dept-name">工会</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">1</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('工会')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('工会')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td class="dept-name">管控部</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">3</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('管控部')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('管控部')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td class="dept-name">生产中心A</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">4</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('生产中心A')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('生产中心A')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td class="dept-name">生产中心B</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">4</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('生产中心B')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('生产中心B')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td class="dept-name">运输中心</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">3</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('运输中心')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('运输中心')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td class="dept-name">运维中心</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">2</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('运维中心')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('运维中心')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td class="dept-name">团委</td>
                                <td><span class="status-active">启用</span></td>
                                <td><span class="user-count">1</span></td>
                                <td>
                                    <div class="dept-actions">
                                        <button class="btn btn-small btn-primary" onclick="editDept('团委')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteDept('团委')">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="add-dept-form">
                    <div class="form-group-inline">
                        <label>部门名称</label>
                        <input type="text" placeholder="输入新部门名称" style="width: 200px;">
                    </div>
                    <div class="form-group-inline">
                        <label>部门状态</label>
                        <select style="width: 120px;">
                            <option value="启用">启用</option>
                            <option value="停用">停用</option>
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>&nbsp;</label>
                        <button class="btn btn-primary" onclick="addDept('reporting')">添加填报单位</button>
                    </div>
                </div>
            </div>

            <!-- 对口部门管理 -->
            <div class="section-card">
                <div class="section-header">
                    <div class="section-title">
                        🏛️ 对口部门管理
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-secondary" onclick="exportDepts('counterpart')">导出配置</button>
                    </div>
                </div>

                <div class="section-content">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                        <!-- 本单位对口部门 -->
                        <div>
                            <h4 style="color: #1976d2; margin-bottom: 15px;">本单位对口部门 (8个)</h4>
                            <div class="dept-grid">
                                <div class="dept-item">
                                    <span class="dept-item-name">综合办</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('本单位', '综合办')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('本单位', '综合办')">删除</button>
                                    </div>
                                </div>
                                <div class="dept-item">
                                    <span class="dept-item-name">人力</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('本单位', '人力')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('本单位', '人力')">删除</button>
                                    </div>
                                </div>
                                <div class="dept-item">
                                    <span class="dept-item-name">财务</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('本单位', '财务')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('本单位', '财务')">删除</button>
                                    </div>
                                </div>
                                <div class="dept-item">
                                    <span class="dept-item-name">党建部</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('本单位', '党建部')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('本单位', '党建部')">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 省分公司对口部门 -->
                        <div>
                            <h4 style="color: #1976d2; margin-bottom: 15px;">省分公司对口部门 (9个)</h4>
                            <div class="dept-grid">
                                <div class="dept-item">
                                    <span class="dept-item-name">综合办(党委办)</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('省分公司', '综合办(党委办)')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('省分公司', '综合办(党委办)')">删除</button>
                                    </div>
                                </div>
                                <div class="dept-item">
                                    <span class="dept-item-name">人力</span>
                                    <div class="dept-item-actions">
                                        <button class="btn btn-small btn-secondary" onclick="editCounterpart('省分公司', '人力')">编辑</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteCounterpart('省分公司', '人力')">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        function editDept(deptName) {
            const newName = prompt(`请输入新的部门名称：`, deptName);
            if (newName && newName.trim() && newName !== deptName) {
                if (confirm(`确定要将"${deptName}"修改为"${newName}"吗？`)) {
                    showMessage(`部门名称已从"${deptName}"修改为"${newName}"`, 'success');
                }
            }
        }

        function deleteDept(deptName) {
            if (confirm(`确定要删除部门"${deptName}"吗？`)) {
                showMessage(`部门"${deptName}"已删除`, 'success');
            }
        }

        function addDept(type) {
            const input = event.target.parentElement.parentElement.querySelector('input');
            const deptName = input.value.trim();

            if (!deptName) {
                showMessage('请输入部门名称', 'warning');
                return;
            }

            showMessage(`填报单位"${deptName}"添加成功！`, 'success');
            input.value = '';
        }

        function editCounterpart(category, deptName) {
            const newName = prompt(`请输入新的${category}对口部门名称：`, deptName);
            if (newName && newName.trim() && newName !== deptName) {
                showMessage(`${category}对口部门已从"${deptName}"修改为"${newName}"`, 'success');
            }
        }

        function deleteCounterpart(category, deptName) {
            if (confirm(`确定要删除${category}对口部门"${deptName}"吗？`)) {
                showMessage(`${category}对口部门"${deptName}"已删除`, 'success');
            }
        }

        function exportDepts(type) {
            const typeName = type === 'reporting' ? '填报单位' : '对口部门';
            showMessage(`正在导出${typeName}列表...`, 'info');
            setTimeout(() => {
                showMessage(`${typeName}列表导出成功！`, 'success');
            }, 1500);
        }
    </script>
</body>
</html>
