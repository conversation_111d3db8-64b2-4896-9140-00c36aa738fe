"""
部门变更管理服务
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_

from ..models.department import Department
from ..models.user import User
from ..models.department_change import DepartmentChange
from ..schemas.department_management import (
    DepartmentMergeRequest, DepartmentSplitRequest, DepartmentCreate,
    BatchOperationResult
)


class DepartmentChangeService:
    """部门变更管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def get_change_history(
        self,
        department_id: Optional[int] = None,
        change_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 50
    ) -> Dict[str, Any]:
        """获取部门变更历史"""
        query = self.db.query(DepartmentChange)
        
        # 筛选条件
        if department_id:
            query = query.filter(DepartmentChange.department_id == department_id)
        
        if change_type:
            query = query.filter(DepartmentChange.change_type == change_type)
        
        # 排序
        query = query.order_by(desc(DepartmentChange.created_at))
        
        # 总数
        total = query.count()
        
        # 分页
        changes = query.offset(skip).limit(limit).all()
        
        # 添加创建者信息
        for change in changes:
            if change.created_by:
                creator = self.db.query(User).filter(User.id == change.created_by).first()
                change.creator_name = creator.full_name if creator else "未知用户"
        
        return {
            "data": changes,
            "total": total,
            "page": (skip // limit) + 1 if limit > 0 else 1,
            "limit": limit
        }

    def merge_departments(self, request: DepartmentMergeRequest, user_id: int) -> Department:
        """合并部门"""
        try:
            # 验证源部门
            source_departments = []
            for dept_id in request.source_department_ids:
                dept = self.db.query(Department).filter(Department.id == dept_id).first()
                if not dept:
                    raise ValueError(f"源部门 ID {dept_id} 不存在")
                source_departments.append(dept)
            
            # 检查目标名称唯一性
            existing = self.db.query(Department).filter(Department.name == request.target_name).first()
            if existing:
                raise ValueError(f"目标部门名称 '{request.target_name}' 已存在")
            
            # 创建目标部门
            target_department = Department(
                name=request.target_name,
                type=request.target_type,
                status="active",
                updated_by=user_id
            )
            
            self.db.add(target_department)
            self.db.flush()  # 获取ID
            
            # 统计影响
            total_users = 0
            total_ledgers = 0
            
            # 迁移用户和数据
            for source_dept in source_departments:
                # 迁移用户
                users = self.db.query(User).filter(User.department_id == source_dept.id).all()
                for user in users:
                    user.department_id = target_department.id
                    total_users += 1
                
                # 统计台账（这里只统计，实际台账数据可能需要手动处理）
                from ..models.ledger import Ledger
                ledger_count = self.db.query(Ledger).filter(Ledger.responsible_dept == source_dept.name).count()
                total_ledgers += ledger_count
                
                # 记录源部门的变更日志
                self._log_department_change(
                    department_id=source_dept.id,
                    change_type="merge",
                    old_name=source_dept.name,
                    new_name=request.target_name,
                    old_type=source_dept.type,
                    new_type=request.target_type,
                    reason=request.merge_reason or f"合并到部门: {request.target_name}",
                    affected_users=len(users),
                    affected_ledgers=ledger_count,
                    user_id=user_id
                )
                
                # 删除源部门
                self.db.delete(source_dept)
            
            # 记录目标部门的创建日志
            self._log_department_change(
                department_id=target_department.id,
                change_type="create",
                new_name=request.target_name,
                new_type=request.target_type,
                reason=f"通过合并创建: 合并了 {len(source_departments)} 个部门",
                affected_users=total_users,
                affected_ledgers=total_ledgers,
                user_id=user_id
            )
            
            self.db.commit()
            return target_department
            
        except Exception as e:
            self.db.rollback()
            raise ValueError(f"部门合并失败: {str(e)}")

    def _log_department_change(
        self,
        department_id: Optional[int],
        change_type: str,
        old_name: Optional[str] = None,
        new_name: Optional[str] = None,
        old_type: Optional[str] = None,
        new_type: Optional[str] = None,
        reason: Optional[str] = None,
        affected_ledgers: int = 0,
        affected_users: int = 0,
        user_id: int = None
    ):
        """记录部门变更日志"""
        change_log = DepartmentChange(
            department_id=department_id,
            change_type=change_type,
            old_name=old_name,
            new_name=new_name,
            old_type=old_type,
            new_type=new_type,
            reason=reason,
            affected_ledgers=affected_ledgers,
            affected_users=affected_users,
            status="completed",
            created_by=user_id
        )
        
        self.db.add(change_log)
        # 不在这里commit，由调用方决定
