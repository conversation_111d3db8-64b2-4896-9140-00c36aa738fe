/**
 * 导出工具类
 * 支持导出Excel文件
 */

export interface ExportColumn {
  key: string
  title: string
  width?: number
}

export interface ExportData {
  [key: string]: any
}

/**
 * 导出Excel文件（使用CSV格式）
 * @param data 数据数组
 * @param columns 列配置
 * @param filename 文件名
 */
export function exportToExcel(data: ExportData[], columns: ExportColumn[], filename: string = '导出数据') {
  try {
    // 生成CSV内容
    const csvContent = generateCSV(data, columns)

    // 下载文件
    downloadCSV(csvContent, filename)

    return true
  } catch (error) {
    console.error('导出文件失败:', error)
    throw new Error('导出失败，请重试')
  }
}

/**
 * 生成CSV内容
 */
function generateCSV(data: ExportData[], columns: ExportColumn[]): string {
  // 创建表头
  const headers = columns.map(col => col.title)

  // 创建数据行
  const rows = data.map(item =>
    columns.map(col => {
      const value = item[col.key]
      // 处理数组类型的数据（如标签、对口部门）
      if (Array.isArray(value)) {
        return value.join(', ')
      }
      // 处理状态字段
      if (col.key === 'status') {
        return getStatusText(value)
      }
      // 处理空值
      if (value === null || value === undefined) {
        return ''
      }
      // 转换为字符串并处理特殊字符
      const stringValue = String(value)
      // 如果包含逗号、引号或换行符，需要用引号包围并转义引号
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`
      }
      return stringValue
    })
  )

  // 合并表头和数据
  const allRows = [headers, ...rows]

  // 转换为CSV字符串
  return allRows.map(row => row.join(',')).join('\n')
}

/**
 * 下载CSV文件
 */
function downloadCSV(csvContent: string, filename: string) {
  try {
    // 添加BOM以支持中文
    const BOM = '\uFEFF'
    const csvWithBOM = BOM + csvContent

    // 创建Blob对象
    const blob = new Blob([csvWithBOM], {
      type: 'text/csv;charset=utf-8'
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${filename}_${formatDate(new Date())}.csv`

    // 添加下载验证
    link.addEventListener('click', () => {
      console.log('文件下载已触发:', link.download)
    })

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 延迟清理，确保下载完成
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      console.log('下载资源已清理')
    }, 1000)

    return true
  } catch (error) {
    console.error('下载文件失败:', error)
    throw new Error('文件下载失败')
  }
}



/**
 * 状态文本映射
 */
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'in_progress': '进行中',
    'completed': '已完成',
    'completed_ongoing': '已完成并持续推进'
  }
  return statusMap[status] || status
}

/**
 * 格式化日期
 */
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}${month}${day}_${hours}${minutes}`
}

/**
 * 台账数据导出列配置
 */
export const LEDGER_EXPORT_COLUMNS: ExportColumn[] = [
  { key: 'sequence_number', title: '序号', width: 10 },
  { key: 'work_item', title: '工作事项', width: 30 },
  { key: 'work_objectives', title: '工作目标', width: 30 },
  { key: 'measures', title: '具体措施', width: 40 },
  { key: 'progress', title: '进展情况', width: 40 },
  { key: 'effectiveness', title: '成效', width: 30 },
  { key: 'responsible_dept', title: '责任单位', width: 20 },
  { key: 'counterpart_depts', title: '对口部门', width: 30 },
  { key: 'tags', title: '关键词标签', width: 25 },
  { key: 'status', title: '状态', width: 12 },
  { key: 'year', title: '年度', width: 10 },
  { key: 'remarks', title: '备注', width: 30 },
  { key: 'created_at', title: '创建时间', width: 20 },
  { key: 'updated_at', title: '更新时间', width: 20 }
]
