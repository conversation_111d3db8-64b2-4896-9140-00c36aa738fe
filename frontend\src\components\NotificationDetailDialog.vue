<template>
  <el-dialog
    v-model="visible"
    title="通知详情"
    width="600px"
    :before-close="handleClose"
  >
    <div v-if="notification" class="notification-detail">
      <!-- 通知头部 -->
      <div class="notification-header">
        <div class="header-left">
          <el-icon :size="24" :color="getNotificationColor(notification)">
            <component :is="getNotificationIcon(notification)" />
          </el-icon>
          <div class="header-info">
            <h3 class="notification-title">{{ notification.title }}</h3>
            <div class="notification-meta">
              <span class="type-badge" :class="notification.notification_type">
                {{ getTypeLabel(notification.notification_type) }}
              </span>
              <span class="time">{{ formatDateTime(notification.created_at) }}</span>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-tag v-if="notification.is_pinned" type="warning" size="small">
            <el-icon><Star /></el-icon>
            置顶
          </el-tag>
          <el-tag v-if="!notification.is_read" type="primary" size="small">
            未读
          </el-tag>
        </div>
      </div>
      
      <!-- 通知内容 -->
      <div class="notification-content">
        <div class="content-text">
          {{ notification.content }}
        </div>
        
        <!-- 关联信息 -->
        <div v-if="notification.reminder_id" class="related-info">
          <h4>相关提醒信息</h4>
          <div class="reminder-info" v-loading="reminderLoading">
            <div v-if="reminderDetail">
              <p><strong>提醒类型：</strong>{{ getReminderTypeLabel(reminderDetail.reminder_type) }}</p>
              <p><strong>优先级：</strong>{{ getPriorityLabel(reminderDetail.priority) }}</p>
              <p><strong>计划时间：</strong>{{ formatDateTime(reminderDetail.scheduled_time) }}</p>
              <p v-if="reminderDetail.ledger_title">
                <strong>相关台账：</strong>{{ reminderDetail.ledger_title }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="notification.action_url" class="action-section">
          <el-button 
            type="primary" 
            @click="handleAction"
            :loading="actionLoading"
          >
            {{ notification.action_text || '查看详情' }}
          </el-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <el-button 
            v-if="!notification?.is_read" 
            @click="markAsRead"
            :loading="markReadLoading"
          >
            标记已读
          </el-button>
          <el-button 
            @click="togglePin"
            :loading="pinLoading"
          >
            {{ notification?.is_pinned ? '取消置顶' : '置顶' }}
          </el-button>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">关闭</el-button>
          <el-button 
            type="danger" 
            @click="deleteNotification"
            :loading="deleteLoading"
          >
            删除
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Star, Bell, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  notification: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits(['update:modelValue', 'updated'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const reminderDetail = ref(null)
const reminderLoading = ref(false)
const actionLoading = ref(false)
const markReadLoading = ref(false)
const pinLoading = ref(false)
const deleteLoading = ref(false)

// 监听通知变化
watch(() => props.notification, (newNotification) => {
  if (newNotification && newNotification.reminder_id) {
    loadReminderDetail(newNotification.reminder_id)
  }
}, { immediate: true })

// 方法
const loadReminderDetail = async (reminderId: number) => {
  try {
    reminderLoading.value = true
    const response = await fetch(`/api/v1/reminders/${reminderId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      reminderDetail.value = await response.json()
    }
  } catch (error) {
    console.error('加载提醒详情失败:', error)
  } finally {
    reminderLoading.value = false
  }
}

const handleAction = async () => {
  if (!props.notification?.action_url) return
  
  try {
    actionLoading.value = true
    
    // 如果是内部链接，使用路由跳转
    if (props.notification.action_url.startsWith('/')) {
      window.location.href = props.notification.action_url
    } else {
      // 外部链接，新窗口打开
      window.open(props.notification.action_url, '_blank')
    }
    
    // 标记为已读
    if (!props.notification.is_read) {
      await markAsRead()
    }
    
    // 关闭对话框
    handleClose()
  } catch (error) {
    console.error('执行操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    actionLoading.value = false
  }
}

const markAsRead = async () => {
  if (!props.notification) return
  
  try {
    markReadLoading.value = true
    const response = await fetch(`/api/v1/reminders/notifications/${props.notification.id}/read`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('已标记为已读')
      emit('updated')
      handleClose()
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  } finally {
    markReadLoading.value = false
  }
}

const togglePin = async () => {
  if (!props.notification) return
  
  try {
    pinLoading.value = true
    const response = await fetch(`/api/v1/reminders/notifications/${props.notification.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({ 
        is_pinned: !props.notification.is_pinned 
      })
    })
    
    if (response.ok) {
      ElMessage.success(props.notification.is_pinned ? '已取消置顶' : '已置顶')
      emit('updated')
      handleClose()
    }
  } catch (error) {
    console.error('置顶操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    pinLoading.value = false
  }
}

const deleteNotification = async () => {
  if (!props.notification) return
  
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    deleteLoading.value = true
    const response = await fetch(`/api/v1/reminders/notifications/${props.notification.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({ is_deleted: true })
    })
    
    if (response.ok) {
      ElMessage.success('通知已删除')
      emit('updated')
      handleClose()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
    }
  } finally {
    deleteLoading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

// 工具方法
const getNotificationIcon = (notification: any) => {
  const iconMap = {
    'reminder': Bell,
    'warning': Warning,
    'info': InfoFilled,
    'success': SuccessFilled,
    'system': InfoFilled
  }
  return iconMap[notification.notification_type] || Bell
}

const getNotificationColor = (notification: any) => {
  const colorMap = {
    'primary': '#409eff',
    'success': '#67c23a',
    'warning': '#e6a23c',
    'danger': '#f56c6c',
    'info': '#909399'
  }
  return colorMap[notification.color] || '#409eff'
}

const getTypeLabel = (type: string) => {
  const typeMap = {
    'reminder': '提醒',
    'warning': '警告',
    'info': '信息',
    'success': '成功',
    'system': '系统'
  }
  return typeMap[type] || '通知'
}

const getReminderTypeLabel = (type: string) => {
  const typeMap = {
    'deadline': '截止日期',
    'escalation': '升级提醒',
    'custom': '自定义'
  }
  return typeMap[type] || type
}

const getPriorityLabel = (priority: string) => {
  const priorityMap = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  }
  return priorityMap[priority] || priority
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}
</script>

<style scoped>
.notification-detail {
  padding: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.header-info {
  flex: 1;
}

.notification-title {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
  font-weight: 600;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.reminder {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

.type-badge.warning {
  background-color: var(--el-color-warning-light-8);
  color: var(--el-color-warning);
}

.type-badge.info {
  background-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

.type-badge.success {
  background-color: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.time {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.notification-content {
  line-height: 1.6;
}

.content-text {
  color: var(--el-text-color-primary);
  margin-bottom: 20px;
  white-space: pre-wrap;
}

.related-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 6px;
}

.related-info h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.reminder-info p {
  margin: 8px 0;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.action-section {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-lighter);
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 8px;
}
</style>
