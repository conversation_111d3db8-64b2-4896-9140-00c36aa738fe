# 环境变量配置文档

## 概述

台账管理系统现在使用环境变量统一管理所有配置，包括端口、数据库连接、安全密钥等。这样可以：

- 避免硬编码配置
- 支持不同环境的配置
- 提高安全性
- 简化部署流程

## 环境变量文件结构

```
项目根目录/
├── .env                    # 全局环境变量
├── backend/.env           # 后端专用环境变量
├── frontend/.env          # 前端专用环境变量
└── scripts/
    ├── start-dev.py       # 开发环境启动脚本
    └── start-dev.bat      # Windows启动脚本
```

## 主要环境变量说明

### 服务端口配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `BACKEND_PORT` | 8004 | 后端API服务端口 |
| `FRONTEND_PORT` / `VITE_PORT` | 5174 | 前端开发服务器端口 |

### 后端配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `APP_NAME` | 台账管理系统 | 应用名称 |
| `APP_VERSION` | 1.0.0 | 应用版本 |
| `DEBUG` | true | 调试模式 |
| `DATABASE_URL` | sqlite:///./ledger.db | 数据库连接字符串 |
| `SECRET_KEY` | your-secret-key... | JWT密钥 |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | 120 | Token过期时间(分钟) |
| `LOG_LEVEL` | debug | 日志级别 |

### 前端配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VITE_API_TARGET` | http://localhost:8004 | 后端API目标地址 |
| `VITE_HOST` | 0.0.0.0 | 开发服务器主机 |
| `VITE_APP_TITLE` | 台账管理系统 | 应用标题 |

## 使用方法

### 1. 开发环境启动

**方法一：使用Python脚本（推荐）**
```bash
python scripts/start-dev.py
```

**方法二：使用Windows批处理**
```cmd
scripts/start-dev.bat
```

**方法三：手动启动**
```bash
# 启动后端
cd backend
python start.py

# 启动前端（新终端）
cd frontend
npm run dev
```

### 2. 修改端口配置

编辑根目录的 `.env` 文件：
```env
# 修改后端端口为8005
BACKEND_PORT=8005

# 修改前端端口为3000
FRONTEND_PORT=3000
VITE_PORT=3000

# 更新API目标
VITE_API_TARGET=http://localhost:8005
```

### 3. 生产环境配置

创建 `.env.production` 文件：
```env
DEBUG=false
SECRET_KEY=your-very-secure-production-key
LOG_LEVEL=info
ALLOWED_ORIGINS=https://yourdomain.com
```

## 配置优先级

1. 系统环境变量（最高优先级）
2. `.env.local` 文件
3. `.env.production` / `.env.development` 文件
4. `.env` 文件
5. 代码中的默认值（最低优先级）

## 安全注意事项

### 敏感信息保护

- 生产环境的 `SECRET_KEY` 必须使用强密钥
- 不要将包含敏感信息的 `.env` 文件提交到版本控制
- 使用 `.gitignore` 排除敏感配置文件

### .gitignore 配置

```gitignore
# 环境变量文件
.env.local
.env.production
.env.*.local

# 但保留示例文件
!.env.example
```

## 故障排除

### 端口冲突

如果遇到端口被占用：
1. 修改 `.env` 文件中的端口号
2. 重启服务

### 环境变量不生效

1. 检查文件名是否正确（`.env`）
2. 检查变量名是否拼写正确
3. 重启开发服务器
4. 检查是否有语法错误（如多余的空格）

### 前端代理失败

1. 确认 `VITE_API_TARGET` 指向正确的后端地址
2. 确认后端服务已启动
3. 检查防火墙设置

## 最佳实践

1. **使用示例文件**：创建 `.env.example` 文件作为模板
2. **文档化配置**：为每个环境变量添加注释说明
3. **分环境配置**：开发、测试、生产使用不同的配置文件
4. **定期更新密钥**：生产环境定期更换安全密钥
5. **监控配置**：记录配置变更，便于问题排查

## 环境变量验证

系统启动时会自动验证关键环境变量：
- 端口号是否为有效数字
- 必需的配置是否存在
- 数据库连接是否可用

如果验证失败，系统会显示详细的错误信息并拒绝启动。
