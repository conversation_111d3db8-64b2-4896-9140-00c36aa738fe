# 部门管理功能修复 - 部署说明

## 📋 部署概述

**部署类型**: 增量部署（只更新修改的文件）  
**风险等级**: 低  
**预计停机时间**: 2-3分钟  

## 🔧 修改的文件列表

### 后端文件 (2个)
1. `backend/app/api/v1/department_management.py` - 添加对口部门PUT/DELETE API
2. `backend/app/models/department.py` - 添加user_count属性

### 前端文件 (3个)
1. `frontend/src/components/department/CounterpartDepartments.vue` - 修复API路径
2. `frontend/src/components/department/DepartmentFormDialog.vue` - 修复API路径
3. `frontend/src/components/department/ReportingDepartments.vue` - 修复API路径

## 🚀 部署步骤

### 步骤1: 备份现有文件
```bash
cd /opt/хП░ш┤жчобчРЖ2
cp -r backend backend_backup_$(date +%Y%m%d_%H%M%S)
cp -r frontend frontend_backup_$(date +%Y%m%d_%H%M%S)
```

### 步骤2: 停止服务
```bash
# 停止后端服务
pkill -f "python3 start.py"

# 停止Nginx (可选，如果需要)
systemctl stop nginx
```

### 步骤3: 更新后端文件

#### 3.1 更新 department_management.py
将本地的 `backend/app/api/v1/department_management.py` 文件内容复制到服务器对应位置。

**关键修改**: 添加了以下API端点：
- `PUT /api/v1/department-management/counterpart/{dept_id}` - 更新对口部门
- `DELETE /api/v1/department-management/counterpart/{dept_id}` - 删除对口部门

#### 3.2 更新 department.py
将本地的 `backend/app/models/department.py` 文件内容复制到服务器对应位置。

**关键修改**: 添加了以下属性：
```python
@property
def user_count(self) -> int:
    """用户数量"""
    return len([user for user in self.users if user.is_active])

@property
def has_children(self) -> bool:
    """是否有下级部门"""
    return len(self.children) > 0
```

### 步骤4: 更新前端文件

#### 4.1 更新 CounterpartDepartments.vue
**修改位置**: 第179行
```javascript
// 修改前
const response = await fetch(`/api/v1/departments/counterpart?${params}`, {

// 修改后
const response = await fetch(`/api/v1/department-management/counterpart?${params}`, {
```

#### 4.2 更新 ReportingDepartments.vue
**修改位置**: 第178行
```javascript
// 修改前
const response = await fetch(`/api/v1/departments/reporting?${params}`, {

// 修改后
const response = await fetch(`/api/v1/department-management/reporting?${params}`, {
```

#### 4.3 DepartmentFormDialog.vue
这个文件之前已经修复过，确认API路径为：
```javascript
const url = isEdit.value 
  ? `/api/v1/department-management/${props.departmentType}/${props.department.id}`
  : `/api/v1/department-management/${props.departmentType}`
```

### 步骤5: 重新构建前端
```bash
cd /opt/хП░ш┤жчобчРЖ2/frontend
npm run build
```

### 步骤6: 部署前端构建文件
```bash
cp -r /opt/хП░ш┤жчобчРЖ2/frontend/dist/* /var/www/html/
```

### 步骤7: 启动服务
```bash
# 启动后端服务
cd /opt/хП░ш┤жчобчРЖ2/backend
nohup python3 start.py > /var/log/backend.log 2>&1 &

# 启动Nginx (如果之前停止了)
systemctl start nginx
```

### 步骤8: 验证部署
```bash
# 检查后端服务
curl http://localhost:8004/health

# 检查前端服务
curl -I http://localhost/

# 检查API端点
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8004/api/v1/department-management/counterpart
```

## 🧪 功能测试

部署完成后，请测试以下功能：

### 1. 登录测试
- 访问 http://************
- 使用 admin/admin123 登录

### 2. 对口部门功能测试
- 进入部门管理页面
- 切换到对口部门标签
- 测试创建对口部门
- 测试编辑对口部门 ✅ **应该正常工作**
- 测试删除对口部门 ✅ **应该正常工作**

### 3. 填报部门功能测试
- 切换到填报部门标签
- 测试创建填报部门
- 测试编辑填报部门
- 测试删除填报部门

### 4. 用户统计验证
- 检查部门列表中的用户统计是否正确显示
- 验证活跃用户数量是否准确

## 🔄 回滚方案

如果部署出现问题，可以快速回滚：

```bash
cd /opt/хП░ш┤жчобчРЖ2

# 停止服务
pkill -f "python3 start.py"

# 恢复备份 (使用最新的备份时间戳)
rm -rf backend frontend
mv backend_backup_YYYYMMDD_HHMMSS backend
mv frontend_backup_YYYYMMDD_HHMMSS frontend

# 重新构建前端
cd frontend && npm run build && cp -r dist/* /var/www/html/

# 启动服务
cd ../backend && nohup python3 start.py > /var/log/backend.log 2>&1 &
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. **检查日志**:
   - 后端日志: `/var/log/backend.log`
   - Nginx日志: `/var/log/nginx/error.log`

2. **常见问题**:
   - 端口冲突: 检查8004端口是否被占用
   - 权限问题: 确保文件权限正确
   - 依赖问题: 检查Python包是否完整

3. **验证步骤**:
   - 检查进程: `ps aux | grep python`
   - 检查端口: `netstat -tlnp | grep 8004`
   - 检查API: `curl http://localhost:8004/health`

## ✅ 预期结果

部署成功后，客户反馈的所有问题都应该解决：

- ✅ 对口部门可以正常删除
- ✅ 对口部门可以正常编辑更新
- ✅ 填报部门功能继续正常
- ✅ 用户统计显示正确
- ✅ 不再出现404错误
- ✅ 所有API调用正常响应
