<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="welcome-title">欢迎使用台账管理系统</h1>
        <p class="welcome-subtitle">高效管理工作台账，提升工作效率</p>
        <div class="user-info" v-loading="userLoading">
          <el-avatar :size="40" class="user-avatar">
            {{ authStore.user?.full_name?.charAt(0) || 'U' }}
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ authStore.user?.full_name || '用户' }}</div>
            <div class="user-role">{{ getRoleName(authStore.user?.role) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">系统概览</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#409EFF">
              <Document />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalLedgers }}</div>
            <div class="stat-label">总台账数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#67C23A">
              <Check />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.completedLedgers }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#E6A23C">
              <Clock />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.pendingLedgers }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#F56C6C">
              <Warning />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.overdueReminders }}</div>
            <div class="stat-label">逾期提醒</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="actions-grid">
        <el-card class="action-card" @click="navigateTo('/ledgers/create')">
          <div class="action-content">
            <el-icon size="24" color="#409EFF">
              <Plus />
            </el-icon>
            <div class="action-text">
              <div class="action-title">新建台账</div>
              <div class="action-desc">创建新的工作台账</div>
            </div>
          </div>
        </el-card>

        <el-card class="action-card" @click="navigateTo('/ledgers')">
          <div class="action-content">
            <el-icon size="24" color="#67C23A">
              <List />
            </el-icon>
            <div class="action-text">
              <div class="action-title">台账管理</div>
              <div class="action-desc">查看和管理台账</div>
            </div>
          </div>
        </el-card>

        <el-card class="action-card" @click="navigateTo('/reminders')" v-if="authStore.canManageReminders">
          <div class="action-content">
            <el-icon size="24" color="#E6A23C">
              <Bell />
            </el-icon>
            <div class="action-text">
              <div class="action-title">提醒管理</div>
              <div class="action-desc">管理提醒规则</div>
            </div>
          </div>
        </el-card>

        <el-card class="action-card" @click="navigateTo('/user-management')" v-if="authStore.canManageUsers">
          <div class="action-content">
            <el-icon size="24" color="#F56C6C">
              <User />
            </el-icon>
            <div class="action-text">
              <div class="action-title">用户管理</div>
              <div class="action-desc">管理系统用户</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity-section">
      <h2 class="section-title">最近活动</h2>
      <el-card class="activity-card">
        <div v-if="recentActivities.length > 0" class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <el-icon :color="getActivityColor(activity.type)">
                <component :is="getActivityIcon(activity.type)" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-time">{{ formatTime(activity.created_at) }}</div>
            </div>
          </div>
        </div>
        <div v-else class="no-activity">
          <el-empty description="暂无最近活动" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { 
  Document, Check, Clock, Warning, Plus, List, Bell, User,
  Edit, Delete, View
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const stats = ref({
  totalLedgers: 0,
  completedLedgers: 0,
  pendingLedgers: 0,
  overdueReminders: 0
})

const recentActivities = ref([])
const userLoading = ref(false) // 用户信息加载状态

// 方法
const getRoleName = (role: string) => {
  const roleNames = {
    admin: '系统管理员',
    manager: '管理部门',
    user: '基层用户'
  }
  return roleNames[role] || role
}

const navigateTo = (path: string) => {
  router.push(path)
}

const getActivityIcon = (type: string) => {
  const icons = {
    create: Plus,
    edit: Edit,
    delete: Delete,
    view: View
  }
  return icons[type] || Document
}

const getActivityColor = (type: string) => {
  const colors = {
    create: '#67C23A',
    edit: '#409EFF',
    delete: '#F56C6C',
    view: '#909399'
  }
  return colors[type] || '#909399'
}

const formatTime = (time: string) => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await fetch('/api/v1/dashboard/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      stats.value = data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用模拟数据
    stats.value = {
      totalLedgers: 2,
      completedLedgers: 1,
      pendingLedgers: 1,
      overdueReminders: 0
    }
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    const response = await fetch('/api/v1/dashboard/activities', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      recentActivities.value = data.slice(0, 5) // 只显示最近5条
    }
  } catch (error) {
    console.error('加载最近活动失败:', error)
    // 使用模拟数据
    recentActivities.value = [
      {
        id: 1,
        type: 'create',
        title: '创建了台账：技术创新项目推进',
        created_at: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 2,
        type: 'edit',
        title: '编辑了台账：示例工作事项',
        created_at: new Date(Date.now() - 7200000).toISOString()
      }
    ]
  }
}

// 初始化用户信息
const initUserInfo = async () => {
  try {
    userLoading.value = true
    // 如果用户信息不存在，尝试重新获取
    if (!authStore.user) {
      await authStore.getCurrentUser()
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果获取用户信息失败，可能需要重新登录
    if (error.response?.status === 401) {
      router.push('/login')
    }
  } finally {
    userLoading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await initUserInfo() // 先初始化用户信息
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.home-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
  color: white;
  margin-bottom: 30px;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.user-name {
  font-size: 16px;
  font-weight: 500;
}

.user-role {
  font-size: 14px;
  opacity: 0.8;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #303133;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.action-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.action-desc {
  font-size: 14px;
  color: #909399;
}

.activity-card {
  margin-bottom: 20px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-title {
  font-size: 14px;
  color: #303133;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.no-activity {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
