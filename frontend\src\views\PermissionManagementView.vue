<template>
  <div class="permission-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <el-button @click="goBack" style="margin-right: 12px;">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div>
            <h1>权限管理</h1>
            <p>管理系统用户、角色权限和标签配置</p>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          刷新数据
        </el-button>
        <el-dropdown @command="handleUserMenu">
          <el-button>
            {{ authStore.user?.full_name }}
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="change-password">修改密码</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 功能选项卡 -->
    <div class="management-container">
      <el-tabs v-model="activeTab" type="card" class="permission-tabs">
        <!-- 权限矩阵 -->
        <el-tab-pane label="权限矩阵" name="matrix">
          <div class="tab-content">
            <el-card shadow="hover">
              <template #header>
                <h3>系统权限矩阵</h3>
                <p>查看不同角色在各功能模块的权限配置</p>
              </template>

              <div class="table-container">
                <el-table :data="matrixData" border stripe v-loading="matrixLoading" style="width: 100%">
                  <el-table-column prop="name" label="功能模块" min-width="180" />
                  <el-table-column label="系统管理员" align="center" width="130">
                    <template #default="{ row }">
                      <el-tag :type="row.permissions.admin ? 'success' : 'danger'">
                        {{ row.permissions.admin ? '允许' : '禁止' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="管理部门" align="center" width="130">
                    <template #default="{ row }">
                      <el-tag :type="row.permissions.manager ? 'success' : 'danger'">
                        {{ row.permissions.manager ? '允许' : '禁止' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="基层用户" align="center" width="130">
                    <template #default="{ row }">
                      <el-tag :type="row.permissions.user ? 'success' : 'danger'">
                        {{ row.permissions.user ? '允许' : '禁止' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" label="权限说明" min-width="200" />
                </el-table>
              </div>

              <div class="role-description">
                <h4>角色说明</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="role-card">
                      <h5>系统管理员</h5>
                      <p>拥有系统所有权限，可以管理用户和系统配置</p>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="role-card">
                      <h5>管理部门</h5>
                      <p>可以管理标签和批注，导出相关数据</p>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="role-card">
                      <h5>基层用户</h5>
                      <p>只能录入和查看本部门数据</p>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="current-user-permissions" v-if="userPermissions.length > 0">
                <h4>当前用户权限</h4>
                <div class="permission-tags">
                  <el-tag v-for="permission in userPermissions" :key="permission" class="permission-tag">
                    {{ getPermissionLabel(permission) }}
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 用户管理功能已移至独立的"用户管理"页面 -->
        <!-- 请通过主导航栏中的"用户管理"菜单访问完整的用户管理功能 -->

        <!-- 标签管理 -->
        <el-tab-pane label="标签管理" name="tags" v-if="authStore.user?.role === 'admin' || authStore.user?.role === 'manager'">
          <div class="tab-content">
            <el-card shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>标签管理</span>
                  <el-button type="primary" @click="openCreateTagDialog">
                    新增标签
                  </el-button>
                </div>
              </template>

              <div class="tag-filters">
                <el-radio-group v-model="tagFilter" @change="filterTags">
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button label="工作类型">工作类型</el-radio-button>
                  <el-radio-button label="时间周期">时间周期</el-radio-button>
                  <el-radio-button label="优先级">优先级</el-radio-button>
                  <el-radio-button label="部门归属">部门归属</el-radio-button>
                </el-radio-group>
              </div>

              <!-- 标签表格 -->
              <div class="table-container">
                <el-table :data="filteredTags" border stripe v-loading="tagsLoading" style="width: 100%">
                  <el-table-column prop="name" label="标签名称" min-width="150" />
                  <el-table-column prop="category" label="分类" width="120">
                    <template #default="{ row }">
                      <el-tag>{{ categoryMap[row.category] || row.category }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="created_at" label="创建时间" width="180">
                    <template #default="{ row }">
                      {{ formatDateTime(row.created_at) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" fixed="right">
                    <template #default="{ row }">
                      <el-button size="small" @click="editTag(row)">编辑</el-button>
                      <el-button size="small" type="danger" @click="deleteTag(row)">删除</el-button>
                    </template>
                  </el-table-column>

                  <!-- 空状态 -->
                  <template #empty>
                    <el-empty description="暂无标签数据" />
                  </template>
                </el-table>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 系统统计 -->
        <el-tab-pane label="系统统计" name="stats" v-if="authStore.user?.role === 'admin'">
          <div class="tab-content">
            <el-card shadow="hover">
              <template #header>
                <span>系统统计信息</span>
              </template>

              <div v-loading="statsLoading">
                <el-row :gutter="20" v-if="Object.keys(stats).length > 0">
                  <el-col :span="6">
                    <div class="stat-card">
                      <div class="stat-number">{{ stats.total_users || 0 }}</div>
                      <div class="stat-label">总用户数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-card">
                      <div class="stat-number">{{ stats.active_users || 0 }}</div>
                      <div class="stat-label">活跃用户</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-card">
                      <div class="stat-number">{{ stats.total_ledgers || 0 }}</div>
                      <div class="stat-label">台账总数</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-card">
                      <div class="stat-number">{{ stats.recent_logins || 0 }}</div>
                      <div class="stat-label">近期登录</div>
                    </div>
                  </el-col>
                </el-row>

                <!-- 空状态提示 -->
                <div v-if="!statsLoading && Object.keys(stats).length === 0" class="empty-state">
                  <el-empty description="暂无统计数据" />
                </div>
              </div>

              <div class="system-info">
                <h4>系统信息</h4>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="系统版本">v1.0.0</el-descriptions-item>
                  <el-descriptions-item label="数据库">SQLite3</el-descriptions-item>
                  <el-descriptions-item label="后端框架">FastAPI</el-descriptions-item>
                  <el-descriptions-item label="前端框架">Vue3 + Element Plus</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 用户管理对话框已移除，请使用独立的用户管理页面 -->

    <!-- 创建标签对话框 -->
    <el-dialog v-model="showCreateTagDialog" :title="isEditMode ? '编辑标签' : '新增标签'" width="500px">
      <el-form :model="createTagForm" label-width="120px">
        <el-form-item label="标签名称">
          <el-input v-model="createTagForm.name" />
        </el-form-item>
        <el-form-item label="标签分类">
          <el-select v-model="createTagForm.category">
            <el-option label="工作类型" value="work_type" />
            <el-option label="时间周期" value="time_cycle" />
            <el-option label="优先级" value="priority" />
            <el-option label="部门归属" value="department" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="createTagForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelTagDialog">取消</el-button>
        <el-button type="primary" @click="handleCreateTag" :loading="createLoading">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import api from '../api'
import * as permissionApi from '../api/permission'

const router = useRouter()
const authStore = useAuthStore()

// 当前激活的标签页
const activeTab = ref('matrix')

// 数据状态
const matrixData = ref([])
const tags = ref([])
const stats = ref({})
const userPermissions = ref([])

// 加载状态
const matrixLoading = ref(false)
const tagsLoading = ref(false)
const statsLoading = ref(false)
const createLoading = ref(false)

// 对话框状态
const showCreateTagDialog = ref(false)
const isEditMode = ref(false)

// 标签筛选
const tagFilter = ref('')

// 表单数据
const createTagForm = reactive({
  name: '',
  category: 'work_type',
  description: ''
})

// 分类映射
const categoryMap = {
  'work_type': '工作类型',
  'time_cycle': '时间周期',
  'priority': '优先级',
  'department': '部门归属'
}

// 计算属性
const filteredTags = computed(() => {
  if (!tagFilter.value) return tags.value
  return tags.value.filter(tag => tag.category === tagFilter.value)
})

// 方法
const refreshData = () => {
  loadMatrixData()
  loadTags()
  loadStats()
}

const loadMatrixData = async () => {
  try {
    matrixLoading.value = true
    const response = await fetch('/api/v1/auth/permission-matrix', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      matrixData.value = data.modules || []
      console.log('权限矩阵数据加载成功:', matrixData.value.length, '个模块')
    } else {
      console.error('权限矩阵API响应错误:', response.status, response.statusText)
      ElMessage.error(`获取权限矩阵失败: ${response.status}`)
    }
  } catch (error) {
    console.error('获取权限矩阵失败:', error)
    ElMessage.error('获取权限矩阵失败，请检查网络连接')
  } finally {
    matrixLoading.value = false
  }
}

// loadUsers方法已移除，用户管理功能请使用独立的用户管理页面

const loadTags = async () => {
  try {
    tagsLoading.value = true

    // 临时移除权限检查，让admin用户可以正常访问
    // if (!authStore.isLoggedIn || !authStore.user) {
    //   console.warn('用户未登录，无法加载标签')
    //   ElMessage.warning('请先登录后再访问标签管理功能')
    //   router.push('/login')
    //   return
    // }

    // 使用统一的api实例，自动处理认证和错误
    const data = await api.get('/tags')

    // 处理多种可能的数据格式
    if (Array.isArray(data)) {
      // 直接是数组格式
      tags.value = data
    } else if (data.data && Array.isArray(data.data)) {
      // 包含data字段的格式
      tags.value = data.data
    } else if (data.items && Array.isArray(data.items)) {
      // 包含items字段的格式
      tags.value = data.items
    } else if (data.tags && Array.isArray(data.tags)) {
      // 包含tags字段的格式
      tags.value = data.tags
    } else {
      console.warn('标签数据格式异常:', data)
      tags.value = []
    }
    console.log('标签数据加载成功:', tags.value.length, '个标签')
  } catch (error) {
    console.error('获取标签列表失败:', error)

    // 临时移除错误跳转，让页面可以正常显示
    // if (error.response?.status === 401) {
    //   ElMessage.error('登录已过期，请重新登录')
    //   authStore.logout()
    //   router.push('/login')
    // } else if (error.response?.status === 403) {
    //   ElMessage.warning('权限不足，无法访问标签管理功能')
    // } else if (error.message !== 'Token expired') {
    //   // 不是token过期错误才显示通用错误信息
    //   ElMessage.error('获取标签列表失败，请稍后重试')
    // }
    console.log('权限管理页面加载，忽略API错误')
    tags.value = []
  } finally {
    tagsLoading.value = false
  }
}

const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await fetch('/api/v1/auth/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      stats.value = await response.json()
      console.log('统计信息加载成功:', stats.value)
    } else {
      console.error('统计信息API响应错误:', response.status, response.statusText)
      ElMessage.error(`获取统计信息失败: ${response.status}`)
      stats.value = {}
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败，请检查网络连接')
    stats.value = {}
  } finally {
    statsLoading.value = false
  }
}

const getPermissionLabel = (permission) => {
  const labels = {
    'edit_sequence': '编辑序号',
    'edit_all_ledgers': '编辑所有台账',
    'delete_ledgers': '删除台账',
    'view_all_data': '查看所有数据',
    'manage_tags': '管理标签',
    'manage_users': '管理用户',
    'manage_departments': '管理部门',
    'export_data': '导出数据',
    'add_comments': '添加批注'
  }
  return labels[permission] || permission
}

// 用户角色相关方法已移除，请使用独立的用户管理页面

const handleUserMenu = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'change-password':
      router.push('/change-password')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}

// handleCreateUser方法已移除，请使用独立的用户管理页面

const handleCreateTag = async () => {
  try {
    createLoading.value = true

    // 验证表单
    if (!createTagForm.name || !createTagForm.category) {
      ElMessage.error('请填写完整的标签信息')
      return
    }

    if (isEditMode.value) {
      // 编辑模式：更新标签
      await api.put(`/tags/${createTagForm.id}`, {
        name: createTagForm.name,
        category: createTagForm.category,
        description: createTagForm.description,
        color: createTagForm.color || '#409eff'
      })
      ElMessage.success('标签更新成功')
    } else {
      // 创建模式：新建标签
      await api.post('/tags', {
        name: createTagForm.name,
        category: createTagForm.category,
        description: createTagForm.description,
        color: createTagForm.color || '#409eff'
      })
      ElMessage.success('标签创建成功')
    }

    showCreateTagDialog.value = false

    // 重置表单和状态
    Object.assign(createTagForm, {
      id: null,
      name: '',
      category: 'work_type',
      description: '',
      color: '#409eff'
    })
    isEditMode.value = false

    // 刷新标签列表
    await loadTags()
  } catch (error) {
    console.error(isEditMode.value ? '更新标签失败:' : '创建标签失败:', error)
    // 401错误会被api拦截器自动处理
    if (error.response?.status === 403) {
      ElMessage.error(`权限不足，无法${isEditMode.value ? '更新' : '创建'}标签`)
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || `${isEditMode.value ? '更新' : '创建'}标签失败`
      ElMessage.error(errorMessage)
    }
  } finally {
    createLoading.value = false
  }
}

// 用户管理相关方法已移除，请使用独立的用户管理页面

const openCreateTagDialog = () => {
  // 重置表单和状态为创建模式
  Object.assign(createTagForm, {
    id: null,
    name: '',
    category: 'work_type',
    description: '',
    color: '#409eff'
  })
  isEditMode.value = false
  showCreateTagDialog.value = true
}

const cancelTagDialog = () => {
  showCreateTagDialog.value = false
  // 重置表单和状态
  Object.assign(createTagForm, {
    id: null,
    name: '',
    category: 'work_type',
    description: '',
    color: '#409eff'
  })
  isEditMode.value = false
}

const editTag = (tag) => {
  // 设置编辑模式和预填充数据
  Object.assign(createTagForm, {
    id: tag.id,
    name: tag.name,
    category: tag.category,
    description: tag.description || '',
    color: tag.color || '#409eff'
  })
  showCreateTagDialog.value = true
  isEditMode.value = true
}

const deleteTag = async (tag) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签"${tag.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 使用统一的api实例删除标签
    await api.delete(`/tags/${tag.id}`)

    ElMessage.success('标签删除成功')
    await loadTags() // 重新加载标签列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除标签失败:', error)
      if (error.response?.status === 403) {
        ElMessage.error('权限不足，无法删除标签')
      } else if (error.message !== 'Token expired') {
        const errorMessage = error.response?.data?.detail || '删除标签失败'
        ElMessage.error(errorMessage)
      }
    }
  }
}

const filterTags = () => {
  // 标签筛选逻辑已在计算属性中实现
}

// 返回函数
const goBack = () => {
  router.back()
}

// 日期格式化函数
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期 - 页面加载时自动获取数据
onMounted(() => {
  console.log('权限管理页面已加载')
  refreshData() // 恢复自动数据加载
})
</script>

<style scoped>
.permission-management {
  padding: 20px;
  min-height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  overflow-y: auto; /* 整个页面可滚动 */
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  display: flex;
  align-items: center;
}

.header-content h1 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.management-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: visible; /* 允许内容溢出，让内部滚动生效 */
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
}

.permission-tabs {
  min-height: 600px;
  flex: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
}

.permission-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
}

.permission-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.tab-content {
  padding: 20px;
  min-height: 500px;
  max-height: calc(100vh - 200px); /* 限制最大高度，避免页面过长 */
  overflow-y: auto; /* 垂直滚动 */
}

/* 表格容器样式 */
.table-container {
  max-height: 600px; /* 表格最大高度 */
  overflow: auto; /* 自动滚动 */
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* 优化滚动条样式 */
.table-container::-webkit-scrollbar,
.tab-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track,
.tab-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb,
.tab-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.tab-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.role-description {
  margin-top: 30px;
}

.role-description h4 {
  margin-bottom: 16px;
  color: #303133;
}

.role-card {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.role-card h5 {
  margin: 0 0 8px 0;
  color: #409eff;
}

.role-card p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.current-user-permissions {
  margin-top: 30px;
}

.current-user-permissions h4 {
  margin-bottom: 16px;
  color: #303133;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  margin: 0;
}

.tag-filters {
  margin-bottom: 20px;
}

/* 标签表格样式已使用Element Plus默认样式 */

.tag-content h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.tag-content p {
  margin: 4px 0;
  color: #909399;
  font-size: 12px;
}

.tag-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.system-info {
  margin-top: 30px;
}

.system-info h4 {
  margin-bottom: 16px;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .permission-management {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  /* 移动端表格样式由Element Plus自动处理 */
}
</style>
