"""
用户管理服务层
"""
import secrets
import string
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password

class UserManagementService:
    """用户管理服务类"""
    
    def create_user(self, db: Session, user_data: UserCreate) -> User:
        """创建新用户"""
        # 创建用户对象
        user = User(
            username=user_data.username,
            email=user_data.email,
            real_name=user_data.real_name,
            hashed_password=get_password_hash(user_data.password),
            department=user_data.department,
            role=user_data.role,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # 保存到数据库
        db.add(user)
        db.commit()
        db.refresh(user)
        
        return user
    
    def update_user(self, db: Session, user: User, user_data: UserUpdate) -> User:
        """更新用户信息"""
        # 更新字段
        if user_data.email is not None:
            user.email = user_data.email
        if user_data.real_name is not None:
            user.real_name = user_data.real_name
        if user_data.department is not None:
            user.department = user_data.department
        if user_data.role is not None:
            user.role = user_data.role
        if user_data.is_active is not None:
            user.is_active = user_data.is_active
        
        user.updated_at = datetime.utcnow()
        
        # 保存到数据库
        db.commit()
        db.refresh(user)
        
        return user
    
    def delete_user(self, db: Session, user: User) -> None:
        """删除用户"""
        db.delete(user)
        db.commit()
    
    def reset_password(self, db: Session, user: User) -> str:
        """重置用户密码"""
        # 生成随机密码
        new_password = self._generate_random_password()
        
        # 更新密码
        user.hashed_password = get_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        # 保存到数据库
        db.commit()
        
        return new_password
    
    def get_user_permissions(self, user: User) -> Dict[str, bool]:
        """获取用户权限"""
        permissions = {
            'canViewLedgers': True,  # 所有用户都可以查看台账
            'canCreateLedgers': True,  # 所有用户都可以创建台账
            'canEditLedgers': True,  # 所有用户都可以编辑台账
            'canDeleteLedgers': False,  # 默认不能删除
            'canManageUsers': False,  # 默认不能管理用户
            'canManageDepartments': False,  # 默认不能管理部门
            'canManageTags': False,  # 默认不能管理标签
            'canManageReminders': False,  # 默认不能管理提醒
            'canViewReports': True,  # 所有用户都可以查看报告
            'canExportData': True,  # 所有用户都可以导出数据
        }
        
        # 根据角色设置权限
        if user.role == 'admin':
            # 系统管理员拥有所有权限
            permissions.update({
                'canDeleteLedgers': True,
                'canManageUsers': True,
                'canManageDepartments': True,
                'canManageTags': True,
                'canManageReminders': True,
            })
        elif user.role == 'manager':
            # 管理部门用户拥有部分管理权限
            permissions.update({
                'canDeleteLedgers': True,
                'canManageTags': True,
                'canManageReminders': True,
            })
        
        return permissions
    
    def get_user_stats(self, db: Session) -> Dict:
        """获取用户统计信息"""
        # 总用户数
        total_users = db.query(User).count()
        
        # 活跃用户数
        active_users = db.query(User).filter(User.is_active == True).count()
        
        # 各角色用户数
        role_stats = db.query(
            User.role,
            func.count(User.id).label('count')
        ).group_by(User.role).all()
        
        role_counts = {role: count for role, count in role_stats}
        
        # 各部门用户数
        dept_stats = db.query(
            User.department,
            func.count(User.id).label('count')
        ).group_by(User.department).all()
        
        dept_counts = {dept: count for dept, count in dept_stats if dept}
        
        # 近期新增用户数（最近7天）
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_users = db.query(User).filter(User.created_at >= week_ago).count()
        
        return {
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': total_users - active_users,
            'role_counts': role_counts,
            'department_counts': dept_counts,
            'recent_users': recent_users
        }
    
    def _generate_random_password(self, length: int = 8) -> str:
        """生成随机密码"""
        # 包含大小写字母、数字和特殊字符
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        
        # 确保密码包含各种类型的字符
        password = [
            secrets.choice(string.ascii_lowercase),  # 小写字母
            secrets.choice(string.ascii_uppercase),  # 大写字母
            secrets.choice(string.digits),  # 数字
            secrets.choice("!@#$%^&*"),  # 特殊字符
        ]
        
        # 填充剩余长度
        for _ in range(length - 4):
            password.append(secrets.choice(characters))
        
        # 打乱顺序
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
