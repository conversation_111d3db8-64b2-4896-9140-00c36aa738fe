<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建数据备份"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="备份名称" prop="backup_name">
        <el-input
          v-model="form.backup_name"
          placeholder="请输入备份名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备份范围" prop="backup_scope">
        <el-radio-group v-model="form.backup_scope">
          <el-radio value="full">完整备份</el-radio>
          <el-radio value="year">年度备份</el-radio>
        </el-radio-group>
        <div class="form-tip">
          完整备份包含所有数据，年度备份仅包含指定年度数据
        </div>
      </el-form-item>

      <el-form-item label="目标年度" prop="target_year" v-if="form.backup_scope === 'year'">
        <el-select v-model="form.target_year" placeholder="请选择年度" style="width: 100%">
          <el-option
            v-for="year in availableYears"
            :key="year"
            :label="`${year}年`"
            :value="year"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备份类型">
        <el-radio-group v-model="form.backup_type">
          <el-radio value="manual">手动备份</el-radio>
          <el-radio value="auto" disabled>自动备份</el-radio>
        </el-radio-group>
        <div class="form-tip">
          自动备份功能将在后续版本中提供
        </div>
      </el-form-item>

      <el-form-item label="备份描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备份描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 备份预览 -->
      <el-card class="preview-card" shadow="never">
        <template #header>
          <span>备份预览</span>
        </template>
        
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">备份范围：</span>
            <span class="preview-value">
              <el-tag :type="form.backup_scope === 'full' ? 'success' : 'primary'">
                {{ form.backup_scope === 'full' ? '完整备份' : `${form.target_year || '未选择'}年度备份` }}
              </el-tag>
            </span>
          </div>
          
          <div class="preview-item">
            <span class="preview-label">备份内容：</span>
            <span class="preview-value">
              {{ getBackupContent() }}
            </span>
          </div>
          
          <div class="preview-item">
            <span class="preview-label">文件格式：</span>
            <span class="preview-value">ZIP压缩包</span>
          </div>
          
          <div class="preview-item">
            <span class="preview-label">预计大小：</span>
            <span class="preview-value">{{ getEstimatedSize() }}</span>
          </div>
        </div>
      </el-card>

      <!-- 备份进度 -->
      <div v-if="backupProgress.show" class="backup-progress">
        <el-progress
          :percentage="backupProgress.percentage"
          :status="backupProgress.status"
          :stroke-width="8"
        />
        <div class="progress-text">{{ backupProgress.text }}</div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="loading">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.backup_name || (form.backup_scope === 'year' && !form.target_year)"
        >
          {{ loading ? '创建中...' : '创建备份' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currentYear: {
    type: Number,
    default: () => new Date().getFullYear()
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()

const form = reactive({
  backup_name: '',
  backup_scope: 'full',
  backup_type: 'manual',
  target_year: null,
  description: ''
})

const backupProgress = reactive({
  show: false,
  percentage: 0,
  status: '',
  text: ''
})

const rules = {
  backup_name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' },
    { min: 2, max: 100, message: '备份名称长度为2-100个字符', trigger: 'blur' }
  ],
  backup_scope: [
    { required: true, message: '请选择备份范围', trigger: 'change' }
  ],
  target_year: [
    { 
      required: true, 
      message: '请选择目标年度', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.backup_scope === 'year' && !value) {
          callback(new Error('年度备份必须选择目标年度'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 计算属性
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let year = 2020; year <= currentYear; year++) {
    years.push(year)
  }
  return years.reverse()
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    generateDefaultName()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 监听备份范围变化
watch(() => form.backup_scope, (val) => {
  if (val === 'year' && !form.target_year) {
    form.target_year = props.currentYear
  }
  generateDefaultName()
})

watch(() => form.target_year, () => {
  generateDefaultName()
})

// 方法
const generateDefaultName = () => {
  const now = new Date()
  const timestamp = now.toISOString().slice(0, 16).replace('T', '_').replace(/:/g, '')
  
  if (form.backup_scope === 'full') {
    form.backup_name = `完整备份_${timestamp}`
  } else if (form.backup_scope === 'year' && form.target_year) {
    form.backup_name = `${form.target_year}年度备份_${timestamp}`
  }
}

const getBackupContent = () => {
  if (form.backup_scope === 'full') {
    return '所有台账数据、部门配置、标签配置、用户信息、年度信息'
  } else if (form.backup_scope === 'year') {
    return `${form.target_year || '指定'}年度的台账数据和年度信息`
  }
  return '未知'
}

const getEstimatedSize = () => {
  if (form.backup_scope === 'full') {
    return '约 2-5 MB'
  } else {
    return '约 500KB - 2MB'
  }
}

const handleClose = () => {
  if (loading.value) return
  
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  form.backup_name = ''
  form.backup_scope = 'full'
  form.backup_type = 'manual'
  form.target_year = null
  form.description = ''
  
  backupProgress.show = false
  backupProgress.percentage = 0
  backupProgress.status = ''
  backupProgress.text = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 显示进度
    backupProgress.show = true
    backupProgress.percentage = 10
    backupProgress.status = ''
    backupProgress.text = '正在准备备份...'
    
    const requestData = {
      backup_name: form.backup_name,
      backup_type: form.backup_type,
      backup_scope: form.backup_scope,
      description: form.description
    }
    
    if (form.backup_scope === 'year') {
      requestData.target_year = form.target_year
    }
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      if (backupProgress.percentage < 80) {
        backupProgress.percentage += 10
        if (backupProgress.percentage === 30) {
          backupProgress.text = '正在收集数据...'
        } else if (backupProgress.percentage === 50) {
          backupProgress.text = '正在压缩文件...'
        } else if (backupProgress.percentage === 70) {
          backupProgress.text = '正在生成校验和...'
        }
      }
    }, 200)
    
    const response = await fetch('/api/v1/backups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify(requestData)
    })
    
    clearInterval(progressInterval)
    
    if (response.ok) {
      const result = await response.json()
      
      // 完成进度
      backupProgress.percentage = 100
      backupProgress.status = 'success'
      backupProgress.text = '备份创建成功！'
      
      setTimeout(() => {
        ElMessage.success({
          message: '备份创建成功',
          duration: 3000
        })
        
        emit('success', result)
        handleClose()
      }, 1000)
    } else {
      const error = await response.json()
      
      // 失败进度
      backupProgress.status = 'exception'
      backupProgress.text = '备份创建失败'
      
      ElMessage.error(error.detail || '备份创建失败')
    }
  } catch (error) {
    console.error('备份创建失败:', error)
    
    backupProgress.status = 'exception'
    backupProgress.text = '备份创建失败'
    
    ElMessage.error('备份创建失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.preview-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.preview-value {
  color: #303133;
}

.backup-progress {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
