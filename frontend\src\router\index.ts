import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/ledgers',
      name: 'ledgers',
      component: () => import('../views/LedgerListView.vue'),
      meta: { requiresAuth: true }
    },

    {
      path: '/ledgers/create',
      name: 'ledger-create',
      component: () => import('../views/LedgerInputView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/ledgers/edit/:id',
      name: 'ledger-edit',
      component: () => import('../views/LedgerInputView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/users',
      name: 'users',
      component: () => import('../views/UserManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/departments',
      name: 'departments',
      component: () => import('../views/DepartmentManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/permission-management',
      name: 'permission-management',
      component: () => import('../views/PermissionManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/year-management',
      name: 'year-management',
      component: () => import('../views/YearManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/department-management',
      name: 'department-management',
      component: () => import('../views/DepartmentManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/user-management',
      name: 'user-management',
      component: () => import('../views/UserManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/years',
      name: 'years',
      component: () => import('../views/YearManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/change-password',
      name: 'change-password',
      component: () => import('../views/ChangePasswordView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/reminders',
      name: 'reminders',
      component: () => import('../views/ReminderManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: false }
    },

    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/'
    }
  ],
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  if (!authStore.isLoggedIn) {
    authStore.initAuth()
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 临时移除权限检查，让admin用户可以访问所有页面
  }

  // 已登录用户访问登录页，跳转到首页
  if (to.name === 'login' && authStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router
