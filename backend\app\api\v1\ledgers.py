"""
台账相关API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from ...api.deps import get_db, get_current_user
from ...models.user import User
from ...services.ledger_service import LedgerService
from ...schemas.ledger import (
    LedgerResponse, LedgerListResponse, StatsResponse, LedgerCreate,
    LedgerUpdate, LedgerDraft, SequenceValidationResponse, NextSequenceResponse,
    UserPermissionsResponse
)
from ...services.user_management_service import UserManagementService

router = APIRouter()


@router.get("/", response_model=LedgerListResponse, summary="获取台账列表")
async def get_ledgers(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    responsible_dept: Optional[List[str]] = Query(None, description="责任单位筛选，支持多选"),
    counterpart_depts: Optional[List[str]] = Query(None, description="对口部门筛选"),
    tags: Optional[List[str]] = Query(None, description="标签筛选"),
    status: Optional[List[str]] = Query(None, description="状态筛选，支持多选"),
    year: int = Query(2025, description="年度筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取台账列表，支持多条件筛选和分页
    """
    service = LedgerService(db)

    filters = {
        "responsible_dept": responsible_dept,
        "counterpart_depts": counterpart_depts,
        "tags": tags,
        "status": status,
        "year": year
    }

    result = service.get_ledgers_with_pagination(
        page=page,
        limit=limit,
        filters=filters
    )

    return result


@router.get("/next-sequence", response_model=NextSequenceResponse, summary="获取下一个序号")
async def get_next_sequence(
    year: int = Query(2025, description="年度"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取下一个可用的序号
    """
    service = LedgerService(db)

    result = service.get_next_sequence_number(year)
    return result


@router.get("/validate-sequence/{sequence}", response_model=SequenceValidationResponse, summary="验证序号唯一性")
async def validate_sequence(
    sequence: str,
    year: int = Query(2025, description="年度"),
    exclude_id: Optional[int] = Query(None, description="排除的台账ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    验证序号是否可用
    """
    service = LedgerService(db)

    result = service.validate_sequence_number(sequence, year, exclude_id)
    return result


@router.get("/{ledger_id}", response_model=LedgerResponse, summary="获取台账详情")
async def get_ledger(
    ledger_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    根据ID获取台账详情
    """
    service = LedgerService(db)
    ledger = service.get_ledger_by_id(ledger_id)

    if not ledger:
        raise HTTPException(status_code=404, detail="台账不存在")

    return ledger


@router.post("/", response_model=LedgerResponse, status_code=status.HTTP_201_CREATED, summary="创建台账")
async def create_ledger(
    ledger_data: LedgerCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的台账记录
    """
    service = LedgerService(db)
    ledger = service.create_ledger(ledger_data)
    return ledger


@router.put("/{ledger_id}", response_model=LedgerResponse, summary="更新台账")
async def update_ledger(
    ledger_id: int,
    ledger_data: LedgerUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新台账记录
    """
    service = LedgerService(db)
    ledger = service.update_ledger(ledger_id, ledger_data)

    if not ledger:
        raise HTTPException(status_code=404, detail="台账不存在")

    return ledger


@router.delete("/{ledger_id}", summary="删除台账")
async def delete_ledger(
    ledger_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除台账记录
    """
    service = LedgerService(db)
    success = service.delete_ledger(ledger_id)

    if not success:
        raise HTTPException(status_code=404, detail="台账不存在")

    return {"message": "台账删除成功"}


@router.get("/stats/", response_model=StatsResponse, summary="获取统计数据")
async def get_ledger_stats(
    responsible_dept: Optional[str] = Query(None, description="责任单位筛选"),
    counterpart_depts: Optional[List[str]] = Query(None, description="对口部门筛选"),
    tags: Optional[List[str]] = Query(None, description="标签筛选"),
    year: int = Query(2025, description="年度筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取台账统计数据
    """
    service = LedgerService(db)

    filters = {
        "responsible_dept": responsible_dept,
        "counterpart_depts": counterpart_depts,
        "tags": tags,
        "year": year
    }

    stats = service.get_ledger_stats(filters)
    return stats


# ==================== 模块2：台账录入模块 API ====================







@router.post("/draft", summary="保存草稿")
async def save_draft(
    draft_data: LedgerDraft,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    保存台账草稿
    """
    service = LedgerService(db)

    result = service.save_draft(draft_data)
    return result



