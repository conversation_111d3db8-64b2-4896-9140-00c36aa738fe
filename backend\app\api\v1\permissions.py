"""
权限管理API路由
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...api.deps import get_current_user
from ...models.user import User
from ...schemas.permission import (
    PermissionResponse, RoleResponse, PermissionMatrixResponse,
    RoleCreate, RoleUpdate, UserRoleUpdate, RolePermissionUpdate
)

router = APIRouter(tags=["权限管理"])

# 临时权限数据 - 在实际项目中应该从数据库获取
PERMISSIONS = [
    {"id": 1, "name": "查看台账", "code": "ledger:view", "description": "查看台账列表和详情", "module": "台账管理"},
    {"id": 2, "name": "创建台账", "code": "ledger:create", "description": "创建新台账", "module": "台账管理"},
    {"id": 3, "name": "编辑台账", "code": "ledger:edit", "description": "编辑台账信息", "module": "台账管理"},
    {"id": 4, "name": "删除台账", "code": "ledger:delete", "description": "删除台账", "module": "台账管理"},
    {"id": 5, "name": "导出台账", "code": "ledger:export", "description": "导出台账数据", "module": "台账管理"},
    
    {"id": 6, "name": "查看用户", "code": "user:view", "description": "查看用户列表", "module": "用户管理"},
    {"id": 7, "name": "创建用户", "code": "user:create", "description": "创建新用户", "module": "用户管理"},
    {"id": 8, "name": "编辑用户", "code": "user:edit", "description": "编辑用户信息", "module": "用户管理"},
    {"id": 9, "name": "删除用户", "code": "user:delete", "description": "删除用户", "module": "用户管理"},
    
    {"id": 10, "name": "查看部门", "code": "department:view", "description": "查看部门列表", "module": "部门管理"},
    {"id": 11, "name": "创建部门", "code": "department:create", "description": "创建新部门", "module": "部门管理"},
    {"id": 12, "name": "编辑部门", "code": "department:edit", "description": "编辑部门信息", "module": "部门管理"},
    {"id": 13, "name": "删除部门", "code": "department:delete", "description": "删除部门", "module": "部门管理"},
    
    {"id": 14, "name": "管理标签", "code": "tag:manage", "description": "管理标签", "module": "标签管理"},
    {"id": 15, "name": "管理年度", "code": "year:manage", "description": "管理年度", "module": "年度管理"},
    {"id": 16, "name": "管理提醒", "code": "reminder:manage", "description": "管理提醒规则", "module": "提醒管理"},
    {"id": 17, "name": "管理权限", "code": "permission:manage", "description": "管理权限和角色", "module": "权限管理"},
]

ROLES = [
    {
        "id": 1, 
        "name": "管理员", 
        "code": "admin", 
        "description": "系统管理员，拥有所有权限",
        "permissions": [p["code"] for p in PERMISSIONS]
    },
    {
        "id": 2, 
        "name": "管理部门", 
        "code": "manager", 
        "description": "管理部门用户，拥有管理权限",
        "permissions": [
            "ledger:view", "ledger:create", "ledger:edit", "ledger:export",
            "user:view", "user:create", "user:edit",
            "department:view", "tag:manage", "reminder:manage"
        ]
    },
    {
        "id": 3, 
        "name": "普通用户", 
        "code": "user", 
        "description": "普通用户，只能操作本部门数据",
        "permissions": [
            "ledger:view", "ledger:create", "ledger:edit"
        ]
    }
]

@router.get("/permissions", summary="获取权限列表")
async def get_permissions(
    module: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取权限列表"""
    permissions = PERMISSIONS
    if module:
        permissions = [p for p in permissions if p["module"] == module]
    return permissions

@router.get("/roles", summary="获取角色列表")
async def get_roles(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取角色列表"""
    return ROLES

@router.get("/matrix", summary="获取权限矩阵")
async def get_permission_matrix(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取权限矩阵"""
    # 按模块分组权限
    modules = {}
    for permission in PERMISSIONS:
        module = permission["module"]
        if module not in modules:
            modules[module] = []
        modules[module].append(permission)
    
    # 构建权限矩阵
    matrix = []
    for module, perms in modules.items():
        module_data = {
            "module": module,
            "permissions": []
        }
        
        for perm in perms:
            perm_data = {
                "name": perm["name"],
                "code": perm["code"],
                "roles": {}
            }
            
            # 检查每个角色是否有此权限
            for role in ROLES:
                perm_data["roles"][role["code"]] = perm["code"] in role["permissions"]
            
            module_data["permissions"].append(perm_data)
        
        matrix.append(module_data)
    
    return matrix

@router.get("/users/{user_id}/permissions", summary="获取用户权限")
async def get_user_permissions(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户权限"""
    # 根据用户角色返回权限
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 根据用户角色获取权限
    role_permissions = []
    for role in ROLES:
        if role["code"] == user.role:
            role_permissions = role["permissions"]
            break
    
    # 返回权限详情
    user_permissions = []
    for perm in PERMISSIONS:
        if perm["code"] in role_permissions:
            user_permissions.append(perm)
    
    return user_permissions

@router.get("/check", summary="检查用户权限")
async def check_user_permission(
    user_id: int,
    permission_code: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """检查用户是否有指定权限"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 根据用户角色检查权限
    has_permission = False
    for role in ROLES:
        if role["code"] == user.role:
            has_permission = permission_code in role["permissions"]
            break
    
    return {"has_permission": has_permission}

@router.post("/roles", summary="创建角色")
async def create_role(
    role_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新角色"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 在实际项目中，这里应该保存到数据库
    new_role = {
        "id": len(ROLES) + 1,
        "name": role_data["name"],
        "code": role_data["code"],
        "description": role_data["description"],
        "permissions": role_data.get("permission_codes", [])
    }
    
    ROLES.append(new_role)
    return new_role

@router.put("/roles/{role_id}", summary="更新角色")
async def update_role(
    role_id: int,
    role_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新角色"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 查找角色
    role = None
    for r in ROLES:
        if r["id"] == role_id:
            role = r
            break
    
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    # 更新角色信息
    if "name" in role_data:
        role["name"] = role_data["name"]
    if "description" in role_data:
        role["description"] = role_data["description"]
    if "permission_codes" in role_data:
        role["permissions"] = role_data["permission_codes"]
    
    return role

@router.delete("/roles/{role_id}", summary="删除角色")
async def delete_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除角色"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    # 查找并删除角色
    for i, role in enumerate(ROLES):
        if role["id"] == role_id:
            ROLES.pop(i)
            return {"message": "角色删除成功"}
    
    raise HTTPException(status_code=404, detail="角色不存在")

@router.put("/user-roles", summary="更新用户角色")
async def update_user_roles(
    data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新用户角色"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    user_id = data["user_id"]
    role_codes = data["role_codes"]
    
    # 更新用户角色（在实际项目中应该更新数据库）
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 简化处理：只取第一个角色
    if role_codes:
        user.role = role_codes[0]
        db.commit()
    
    return {"message": "用户角色更新成功"}
