/**
 * 台账相关 API
 */
import api from './index'

// 台账数据类型定义
export interface Ledger {
  id: number
  sequence_number: string
  work_item: string
  measures: string
  progress: string
  effectiveness?: string
  responsible_dept: string
  counterpart_depts: string[]
  tags: string[]
  remarks?: string
  status: string
  created_at: string
  updated_at: string
  year: number
}

export interface LedgerCreate {
  sequence_number: string
  work_item: string
  measures: string
  progress: string
  effectiveness?: string
  responsible_dept: string
  counterpart_depts: string[]
  tags: string[]
  remarks?: string
  status?: string
  year?: number
}

export interface LedgerUpdate {
  sequence_number?: string
  work_item?: string
  measures?: string
  progress?: string
  effectiveness?: string
  responsible_dept?: string
  counterpart_depts?: string[]
  tags?: string[]
  remarks?: string
  status?: string
  year?: number
}

export interface LedgerListResponse {
  data: Ledger[]
  total: number
  page: number
  limit: number
  pages: number
}

export interface LedgerFilters {
  responsible_dept?: string | string[]  // 支持单选和多选
  counterpart_depts?: string[]          // 对口部门筛选
  tags?: string[]                       // 标签筛选
  status?: string | string[]            // 状态筛选，支持多选
  year?: number                         // 年度筛选
  page?: number                         // 页码
  size?: number                         // 每页数量
}

// 台账 API 方法
export const ledgerApi = {
  // 获取台账列表
  async getList(filters: LedgerFilters = {}): Promise<LedgerListResponse> {
    const params = new URLSearchParams()

    // 责任单位筛选 - 支持多选
    if (filters.responsible_dept) {
      if (Array.isArray(filters.responsible_dept)) {
        filters.responsible_dept.forEach(dept => params.append('responsible_dept', dept))
      } else {
        params.append('responsible_dept', filters.responsible_dept)
      }
    }

    // 对口部门筛选
    if (filters.counterpart_depts) {
      filters.counterpart_depts.forEach(dept => params.append('counterpart_depts', dept))
    }

    // 标签筛选
    if (filters.tags) {
      filters.tags.forEach(tag => params.append('tags', tag))
    }

    // 状态筛选
    if (filters.status) {
      if (Array.isArray(filters.status)) {
        filters.status.forEach(status => params.append('status', status))
      } else {
        params.append('status', filters.status)
      }
    }

    // 年度筛选
    if (filters.year) params.append('year', filters.year.toString())

    // 分页参数
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.size) params.append('size', filters.size.toString())

    const response = await api.get(`/ledgers?${params.toString()}`)
    return response.data
  },

  // 获取单个台账
  async getById(id: number): Promise<Ledger> {
    console.log('API: 开始获取台账，ID:', id)
    const response = await api.get(`/ledgers/${id}`)
    console.log('API: 响应数据:', response)
    return response
  },

  // 创建台账
  async create(data: LedgerCreate): Promise<Ledger> {
    const response = await api.post('/ledgers', data)
    return response.data
  },

  // 更新台账
  async update(id: number, data: LedgerUpdate): Promise<Ledger> {
    const response = await api.put(`/ledgers/${id}`, data)
    return response.data
  },

  // 删除台账
  async delete(id: number): Promise<void> {
    await api.delete(`/ledgers/${id}`)
  },

  // 获取统计数据
  async getStats(filters: Omit<LedgerFilters, 'page' | 'size'> = {}) {
    const params = new URLSearchParams()

    if (filters.responsible_dept) params.append('responsible_dept', filters.responsible_dept)
    if (filters.counterpart_depts) {
      filters.counterpart_depts.forEach(dept => params.append('counterpart_depts', dept))
    }
    if (filters.tags) {
      filters.tags.forEach(tag => params.append('tags', tag))
    }
    if (filters.year) params.append('year', filters.year.toString())

    const response = await api.get(`/ledgers/stats?${params.toString()}`)
    return response.data
  }
}
