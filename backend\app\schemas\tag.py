"""
标签相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator


class TagBase(BaseModel):
    """标签基础模型"""
    name: str = Field(..., min_length=1, max_length=50, description="标签名称")
    category: str = Field(..., description="标签分类")

    @validator('category')
    def validate_category(cls, v):
        """验证标签分类"""
        valid_categories = ["work_type", "time_cycle", "priority", "department"]
        if v not in valid_categories:
            raise ValueError(f"无效的标签分类，有效分类: {', '.join(valid_categories)}")
        return v


class TagCreate(TagBase):
    """创建标签模型"""
    pass


class TagUpdate(BaseModel):
    """更新标签模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="标签名称")
    category: Optional[str] = Field(None, description="标签分类")

    @validator('category')
    def validate_category(cls, v):
        """验证标签分类"""
        if v is not None:
            valid_categories = ["work_type", "time_cycle", "priority", "department"]
            if v not in valid_categories:
                raise ValueError(f"无效的标签分类，有效分类: {', '.join(valid_categories)}")
        return v


class TagResponse(BaseModel):
    """标签响应模型"""
    id: int
    name: str = Field(..., description="标签名称")
    category: str = Field(..., description="标签分类")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TagListResponse(BaseModel):
    """标签列表响应模型"""
    tags: list[TagResponse]
    total: int
    categories: dict[str, str]
