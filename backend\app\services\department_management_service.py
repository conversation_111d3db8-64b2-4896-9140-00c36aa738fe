"""
部门管理核心服务
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from sqlalchemy.exc import IntegrityError

from ..models.department import Department
from ..models.user import User
from ..models.ledger import Ledger
from ..models.department_change import DepartmentChange
from ..models.user_department_history import UserDepartmentHistory
from ..schemas.department_management import (
    DepartmentCreate, DepartmentUpdate, DepartmentResponse,
    DepartmentChangeCreate, ChangeImpactAnalysis, DepartmentStats
)


class DepartmentManagementService:
    """部门管理核心服务"""

    def __init__(self, db: Session):
        self.db = db

    def get_departments(
        self, 
        dept_type: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """获取部门列表"""
        query = self.db.query(Department)
        
        # 类型筛选
        if dept_type:
            query = query.filter(Department.type == dept_type)
        
        # 状态筛选
        if status:
            query = query.filter(Department.status == status)
        
        # 排序
        query = query.order_by(Department.sort_order, Department.name)
        
        # 总数
        total = query.count()
        
        # 分页
        departments = query.offset(skip).limit(limit).all()
        
        return {
            "data": departments,
            "total": total,
            "page": (skip // limit) + 1 if limit > 0 else 1,
            "limit": limit
        }

    def get_department_by_id(self, dept_id: int) -> Optional[Department]:
        """根据ID获取部门"""
        return self.db.query(Department).filter(Department.id == dept_id).first()

    def create_department(self, data: DepartmentCreate, user_id: int) -> Department:
        """创建部门"""
        try:
            # 检查名称唯一性
            existing = self.db.query(Department).filter(Department.name == data.name).first()
            if existing:
                raise ValueError(f"部门名称 '{data.name}' 已存在")
            
            # 检查父部门存在性
            if data.parent_id:
                parent = self.get_department_by_id(data.parent_id)
                if not parent:
                    raise ValueError(f"上级部门 ID {data.parent_id} 不存在")
            
            # 创建部门
            department = Department(
                name=data.name,
                type=data.type,
                description=data.description,
                sort_order=data.sort_order,
                parent_id=data.parent_id,
                status="active",
                updated_by=user_id
            )
            
            self.db.add(department)
            self.db.flush()  # 获取ID
            
            # 记录变更日志
            self._log_department_change(
                department_id=department.id,
                change_type="create",
                new_name=data.name,
                new_type=data.type,
                reason=f"创建新部门: {data.name}",
                user_id=user_id
            )
            
            self.db.commit()
            return department
            
        except IntegrityError as e:
            self.db.rollback()
            raise ValueError(f"创建部门失败: 数据完整性错误 - {str(e)}")
        except Exception as e:
            self.db.rollback()
            raise ValueError(f"创建部门失败: {str(e)}")

    def update_department(self, dept_id: int, data: DepartmentUpdate, user_id: int) -> Department:
        """更新部门"""
        try:
            department = self.get_department_by_id(dept_id)
            if not department:
                raise ValueError(f"部门 ID {dept_id} 不存在")
            
            # 分析变更影响
            impact = self.analyze_change_impact(dept_id, data.dict(exclude_unset=True))
            
            # 记录原始值
            old_name = department.name
            old_type = department.type
            
            # 更新字段
            update_data = data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(department, field):
                    setattr(department, field, value)
            
            department.updated_by = user_id
            
            # 记录变更日志
            self._log_department_change(
                department_id=dept_id,
                change_type="update",
                old_name=old_name,
                new_name=department.name,
                old_type=old_type,
                new_type=department.type,
                reason=f"更新部门信息",
                affected_ledgers=impact.affected_ledgers,
                affected_users=impact.affected_users,
                user_id=user_id
            )
            
            self.db.commit()
            return department
            
        except Exception as e:
            self.db.rollback()
            raise ValueError(f"更新部门失败: {str(e)}")

    def delete_department(self, dept_id: int, user_id: int) -> bool:
        """删除部门"""
        try:
            department = self.get_department_by_id(dept_id)
            if not department:
                raise ValueError(f"部门 ID {dept_id} 不存在")

            # 检查是否有活跃用户关联到此部门
            active_user_count = self.db.query(User).filter(
                User.department_id == dept_id,
                User.is_active == True
            ).count()
            if active_user_count > 0:
                raise ValueError(f"部门 '{department.name}' 下还有 {active_user_count} 个活跃用户，无法删除")

            # 检查是否有下级部门
            children_count = self.db.query(Department).filter(Department.parent_id == dept_id).count()
            if children_count > 0:
                raise ValueError(f"部门 '{department.name}' 下还有 {children_count} 个下级部门，无法删除")

            # 检查是否有关联的台账（按部门名称检查）
            ledger_count = self.db.query(Ledger).filter(Ledger.responsible_dept == department.name).count()
            if ledger_count > 0:
                raise ValueError(f"部门 '{department.name}' 还有 {ledger_count} 条关联台账，无法删除")

            # 检查用户部门历史记录
            from ..models.user_department_history import UserDepartmentHistory
            history_count = self.db.query(UserDepartmentHistory).filter(
                (UserDepartmentHistory.old_department_id == dept_id) |
                (UserDepartmentHistory.new_department_id == dept_id)
            ).count()

            # 记录变更日志
            self._log_department_change(
                department_id=dept_id,
                change_type="delete",
                old_name=department.name,
                old_type=department.type,
                reason=f"删除部门: {department.name}",
                affected_ledgers=ledger_count,
                affected_users=active_user_count,
                user_id=user_id
            )

            # 删除部门
            self.db.delete(department)
            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            raise ValueError(f"删除部门失败: {str(e)}")

    def analyze_change_impact(self, dept_id: int, changes: Dict[str, Any]) -> ChangeImpactAnalysis:
        """分析部门变更影响"""
        department = self.get_department_by_id(dept_id)
        if not department:
            raise ValueError(f"部门 ID {dept_id} 不存在")
        
        # 统计影响的用户数量
        affected_users = self.db.query(User).filter(User.department_id == dept_id).count()
        
        # 统计影响的台账数量
        affected_ledgers = self.db.query(Ledger).filter(Ledger.responsible_dept == department.name).count()
        
        # 统计下级部门数量
        affected_children = self.db.query(Department).filter(Department.parent_id == dept_id).count()
        
        # 风险评估
        risk_level = "low"
        warnings = []
        recommendations = []
        
        if affected_users > 10:
            risk_level = "medium"
            warnings.append(f"将影响 {affected_users} 个用户")
            
        if affected_ledgers > 50:
            risk_level = "high"
            warnings.append(f"将影响 {affected_ledgers} 条台账记录")
            
        if affected_children > 0:
            warnings.append(f"将影响 {affected_children} 个下级部门")
            
        if 'name' in changes:
            recommendations.append("建议在非工作时间进行名称变更")
            
        if 'type' in changes:
            recommendations.append("类型变更可能影响权限配置，请确认相关设置")
        
        return ChangeImpactAnalysis(
            department_id=dept_id,
            department_name=department.name,
            affected_users=affected_users,
            affected_ledgers=affected_ledgers,
            affected_children=affected_children,
            risk_level=risk_level,
            warnings=warnings,
            recommendations=recommendations
        )

    def get_department_stats(self, dept_id: int) -> DepartmentStats:
        """获取部门统计信息"""
        department = self.get_department_by_id(dept_id)
        if not department:
            raise ValueError(f"部门 ID {dept_id} 不存在")
        
        # 用户统计
        user_count = self.db.query(User).filter(User.department_id == dept_id).count()
        active_user_count = self.db.query(User).filter(
            and_(User.department_id == dept_id, User.is_active == True)
        ).count()
        
        # 台账统计
        ledger_count = self.db.query(Ledger).filter(Ledger.responsible_dept == department.name).count()
        completed_ledger_count = self.db.query(Ledger).filter(
            and_(Ledger.responsible_dept == department.name, Ledger.status == "completed")
        ).count()
        
        completion_rate = (completed_ledger_count / ledger_count * 100) if ledger_count > 0 else 0
        
        # 最后活动时间 - 处理可能的日期格式问题
        try:
            last_activity = self.db.query(func.max(Ledger.updated_at)).filter(
                Ledger.responsible_dept == department.name
            ).scalar()
        except Exception:
            last_activity = None

        return DepartmentStats(
            department_id=dept_id,
            department_name=department.name,
            user_count=user_count,
            active_user_count=active_user_count,
            ledger_count=ledger_count,
            completed_ledger_count=completed_ledger_count,
            completion_rate=round(completion_rate, 2),
            last_activity=last_activity
        )

    def _log_department_change(
        self,
        department_id: Optional[int],
        change_type: str,
        old_name: Optional[str] = None,
        new_name: Optional[str] = None,
        old_type: Optional[str] = None,
        new_type: Optional[str] = None,
        reason: Optional[str] = None,
        affected_ledgers: int = 0,
        affected_users: int = 0,
        user_id: int = None
    ):
        """记录部门变更日志"""
        change_log = DepartmentChange(
            department_id=department_id,
            change_type=change_type,
            old_name=old_name,
            new_name=new_name,
            old_type=old_type,
            new_type=new_type,
            reason=reason,
            affected_ledgers=affected_ledgers,
            affected_users=affected_users,
            status="completed",
            created_by=user_id
        )
        
        self.db.add(change_log)
        # 不在这里commit，由调用方决定
