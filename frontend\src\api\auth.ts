/**
 * 认证相关API调用
 */
import { request } from './index'

// 认证相关接口类型定义
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: UserInfo
}

export interface UserInfo {
  id: number
  username: string
  full_name: string
  email?: string
  phone?: string
  department_id?: number
  department?: string
  role: string
  is_active: boolean
  last_login?: string
  login_count: number
  created_at?: string
  updated_at?: string
}

export interface UserPermissions {
  user_id: number
  role: string
  permissions: string[]
  can_edit_sequence: boolean
  can_edit_all_ledgers: boolean
  can_manage_users: boolean
  can_manage_tags: boolean
  can_export_data: boolean
}

export interface PermissionMatrix {
  modules: Array<{
    name: string
    permissions: {
      admin: boolean
      manager: boolean
      user: boolean
    }
    description: string
  }>
}

export interface PasswordChangeRequest {
  current_password: string
  new_password: string
}

// 认证API
export const authAPI = {
  // 用户登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return request.post('/auth/login', data)
  },

  // 用户登出
  logout: (): Promise<{ message: string }> => {
    return request.post('/auth/logout')
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<UserInfo> => {
    return request.get('/auth/me')
  },

  // 获取用户权限
  getUserPermissions: (): Promise<UserPermissions> => {
    return request.get('/auth/permissions')
  },

  // 检查权限
  checkPermission: (permission: string): Promise<{ has_permission: boolean; reason?: string }> => {
    return request.post('/auth/check-permission', { permission })
  },

  // 验证操作权限
  validateAction: (data: {
    action: string
    resource_type: string
    resource_id?: number
    data?: any
  }): Promise<{ allowed: boolean; reason?: string; warnings: string[] }> => {
    return request.post('/auth/validate-action', data)
  },

  // 获取权限矩阵
  getPermissionMatrix: (): Promise<PermissionMatrix> => {
    return request.get('/auth/permission-matrix')
  },

  // 获取角色权限
  getRolePermissions: (role: string): Promise<{
    role: string
    permissions: string[]
    description: string
  }> => {
    return request.get(`/auth/role-permissions/${role}`)
  },

  // 修改密码
  changePassword: (data: PasswordChangeRequest): Promise<{ message: string }> => {
    return request.post('/auth/users/change-password', data)
  }
}

// Token管理工具
export const tokenManager = {
  // 获取Token
  getToken: (): string | null => {
    return localStorage.getItem('access_token')
  },

  // 设置Token
  setToken: (token: string): void => {
    localStorage.setItem('access_token', token)
  },

  // 移除Token
  removeToken: (): void => {
    localStorage.removeItem('access_token')
  },

  // 检查Token是否存在
  hasToken: (): boolean => {
    return !!localStorage.getItem('access_token')
  }
}

// 用户信息管理
export const userManager = {
  // 获取用户信息
  getUserInfo: (): UserInfo | null => {
    const userStr = localStorage.getItem('user_info')
    return userStr ? JSON.parse(userStr) : null
  },

  // 设置用户信息
  setUserInfo: (user: UserInfo): void => {
    localStorage.setItem('user_info', JSON.stringify(user))
  },

  // 移除用户信息
  removeUserInfo: (): void => {
    localStorage.removeItem('user_info')
  },

  // 清除所有认证信息
  clearAuth: (): void => {
    tokenManager.removeToken()
    userManager.removeUserInfo()
    localStorage.removeItem('user_permissions')
  }
}
