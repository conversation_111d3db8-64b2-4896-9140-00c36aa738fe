"""
部门相关API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from ...api.deps import get_db
from ...services.department_service import DepartmentService
from ...schemas.department import DepartmentResponse
from ...models.department import Department

router = APIRouter()


@router.get("/", summary="获取部门列表")
async def get_departments(
    type: Optional[str] = Query(None, description="部门类型筛选: reporting/local/provincial"),
    db: Session = Depends(get_db)
):
    """
    获取部门列表，支持按类型筛选
    """
    from ...services.department_service import DepartmentService

    service = DepartmentService(db)
    departments = service.get_departments(type_filter=type)

    return departments


@router.get("/counterpart", summary="获取对口部门列表")
async def get_counterpart_departments(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    dept_type: Optional[str] = Query(None, description="部门类型筛选: local/provincial"),
    db: Session = Depends(get_db)
):
    """
    获取对口部门列表，支持分页和类型筛选
    """
    # 获取所有对口部门数据
    all_counterpart = [
        {"id": 10, "name": "党委办公室", "dept_type": "local", "status": "active", "description": "本单位党委办公室"},
        {"id": 11, "name": "工会办公室", "dept_type": "local", "status": "active", "description": "本单位工会办公室"},
        {"id": 12, "name": "纪检监察室", "dept_type": "local", "status": "active", "description": "本单位纪检监察室"},
        {"id": 13, "name": "安全生产部", "dept_type": "local", "status": "active", "description": "本单位安全生产部"},
        {"id": 20, "name": "省公司办公室", "dept_type": "provincial", "status": "active", "description": "省分公司办公室"},
        {"id": 21, "name": "省公司技术部", "dept_type": "provincial", "status": "active", "description": "省分公司技术部"},
        {"id": 22, "name": "省公司财务部", "dept_type": "provincial", "status": "active", "description": "省分公司财务部"},
        {"id": 23, "name": "省公司人力资源部", "dept_type": "provincial", "status": "active", "description": "省分公司人力资源部"},
        {"id": 24, "name": "省公司市场部", "dept_type": "provincial", "status": "active", "description": "省分公司市场部"},
        {"id": 25, "name": "省公司运营部", "dept_type": "provincial", "status": "active", "description": "省分公司运营部"}
    ]

    # 类型筛选
    if dept_type:
        filtered_data = [dept for dept in all_counterpart if dept["dept_type"] == dept_type]
    else:
        filtered_data = all_counterpart

    # 分页处理
    total = len(filtered_data)
    data = filtered_data[skip:skip + limit]

    return {
        "data": data,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }


@router.get("/reporting", summary="获取填报单位列表")
async def get_reporting_departments(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """
    获取填报单位列表，支持分页
    """
    # 获取填报单位数据
    all_reporting = [
        {"id": 1, "name": "综合办", "type": "reporting", "status": "active", "description": "负责综合协调工作"},
        {"id": 2, "name": "技术部", "type": "reporting", "status": "active", "description": "负责技术管理工作"},
        {"id": 3, "name": "财务部", "type": "reporting", "status": "active", "description": "负责财务管理工作"},
        {"id": 4, "name": "人力资源部", "type": "reporting", "status": "active", "description": "负责人力资源管理"},
        {"id": 5, "name": "市场部", "type": "reporting", "status": "active", "description": "负责市场营销工作"},
        {"id": 6, "name": "运营部", "type": "reporting", "status": "active", "description": "负责运营管理工作"},
        {"id": 7, "name": "研发中心", "type": "reporting", "status": "active", "description": "负责产品研发工作"},
        {"id": 8, "name": "质量管理部", "type": "reporting", "status": "active", "description": "负责质量管理工作"}
    ]

    # 分页处理
    total = len(all_reporting)
    data = all_reporting[skip:skip + limit]

    return {
        "data": data,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }
