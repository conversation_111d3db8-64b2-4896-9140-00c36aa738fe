<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>年度管理 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .year-selector {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #f0f8ff, #e3f2fd);
            border-radius: 8px;
            border: 1px solid #bbdefb;
        }
        
        .year-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .year-btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .year-btn:hover {
            background: #1565c0;
        }
        
        .year-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .current-year {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            min-width: 80px;
            text-align: center;
        }
        
        .year-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 15px;
        }
        
        .year-current {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .year-archived {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .management-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .management-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-content {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .history-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .archive-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            transition: background 0.3s;
        }
        
        .archive-item:hover {
            background: #f8f9fa;
        }
        
        .archive-item:last-child {
            border-bottom: none;
        }
        
        .archive-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .archive-year {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .archive-details {
            font-size: 14px;
            color: #666;
        }
        
        .archive-actions {
            display: flex;
            gap: 10px;
        }
        
        .warning-box {
            background: #fff3e0;
            border: 1px solid #ffb74d;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .warning-title {
            font-weight: bold;
            color: #f57c00;
            margin-bottom: 8px;
        }
        
        .warning-content {
            color: #f57c00;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #81c784;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .success-title {
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 8px;
        }
        
        .success-content {
            color: #2e7d32;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab active">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="page-container">
        <h2 class="page-title">年度管理页面</h2>
        
        <!-- 年度选择器 -->
        <div class="year-selector">
            <div class="year-nav">
                <button class="year-btn" onclick="changeYear(-1)">◀ 上一年</button>
                <div class="current-year" id="current-year">2025</div>
                <button class="year-btn" onclick="changeYear(1)" disabled>下一年 ▶</button>
            </div>
            <div class="year-status year-current" id="year-status">当前年度</div>
        </div>
        
        <!-- 统计数据 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">台账总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">142</div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">进行中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">6</div>
                <div class="stat-label">草稿</div>
            </div>
        </div>
        
        <!-- 管理功能卡片 -->
        <div class="management-cards">
            <!-- 年度台账创建 -->
            <div class="management-card">
                <div class="card-title">
                    📅 年度台账创建
                </div>
                <div class="card-content">
                    创建新年度的空白台账，继承上一年度的基础配置（部门设置、标签体系等），序号重新开始编排。
                </div>
                <div class="card-actions">
                    <button class="btn btn-primary" onclick="createNewYear()">创建2026年度台账</button>
                    <button class="btn btn-secondary" onclick="previewNewYear()">预览配置</button>
                </div>
            </div>
            
            <!-- 历史台账管理 -->
            <div class="management-card">
                <div class="card-title">
                    📚 历史台账管理
                </div>
                <div class="card-content">
                    管理历史年度的台账数据，支持查询、导出和数据对比。历史数据为只读状态，确保数据完整性。
                </div>
                <div class="card-actions">
                    <button class="btn btn-secondary" onclick="viewHistory()">查看历史数据</button>
                    <button class="btn btn-secondary" onclick="exportHistory()">导出历史台账</button>
                    <button class="btn btn-secondary" onclick="compareYears()">年度对比</button>
                </div>
            </div>
            
            <!-- 数据迁移 -->
            <div class="management-card">
                <div class="card-title">
                    🔄 数据迁移
                </div>
                <div class="card-content">
                    年度切换时的数据平滑迁移，配置信息的继承和更新，用户权限的年度更新。
                </div>
                <div class="card-actions">
                    <button class="btn btn-warning" onclick="migrateData()">执行数据迁移</button>
                    <button class="btn btn-secondary" onclick="checkMigration()">检查迁移状态</button>
                </div>
            </div>
            
            <!-- 数据备份 -->
            <div class="management-card">
                <div class="card-title">
                    💾 数据备份
                </div>
                <div class="card-content">
                    定期备份台账数据，确保数据安全。支持手动备份和自动备份策略配置。
                </div>
                <div class="card-actions">
                    <button class="btn btn-success" onclick="backupData()">立即备份</button>
                    <button class="btn btn-secondary" onclick="restoreData()">恢复数据</button>
                    <button class="btn btn-secondary" onclick="backupSettings()">备份设置</button>
                </div>
            </div>
        </div>
        
        <!-- 历史台账列表 -->
        <div class="history-table">
            <div class="table-header">
                📚 历史台账档案
            </div>
            
            <div class="archive-item">
                <div class="archive-info">
                    <div class="archive-year">2024年度台账</div>
                    <div class="archive-details">台账数量：148条 | 归档时间：2025-01-15 | 数据大小：2.3MB</div>
                </div>
                <div class="archive-actions">
                    <button class="btn btn-small btn-secondary">查看</button>
                    <button class="btn btn-small btn-secondary">导出</button>
                    <button class="btn btn-small btn-primary">对比</button>
                </div>
            </div>
            
            <div class="archive-item">
                <div class="archive-info">
                    <div class="archive-year">2023年度台账</div>
                    <div class="archive-details">台账数量：132条 | 归档时间：2024-01-12 | 数据大小：2.1MB</div>
                </div>
                <div class="archive-actions">
                    <button class="btn btn-small btn-secondary">查看</button>
                    <button class="btn btn-small btn-secondary">导出</button>
                    <button class="btn btn-small btn-primary">对比</button>
                </div>
            </div>
            
            <div class="archive-item">
                <div class="archive-info">
                    <div class="archive-year">2022年度台账</div>
                    <div class="archive-details">台账数量：125条 | 归档时间：2023-01-10 | 数据大小：1.9MB</div>
                </div>
                <div class="archive-actions">
                    <button class="btn btn-small btn-secondary">查看</button>
                    <button class="btn btn-small btn-secondary">导出</button>
                    <button class="btn btn-small btn-primary">对比</button>
                </div>
            </div>
            
            <div class="archive-item">
                <div class="archive-info">
                    <div class="archive-year">2021年度台账</div>
                    <div class="archive-details">台账数量：118条 | 归档时间：2022-01-08 | 数据大小：1.8MB</div>
                </div>
                <div class="archive-actions">
                    <button class="btn btn-small btn-secondary">查看</button>
                    <button class="btn btn-small btn-secondary">导出</button>
                    <button class="btn btn-small btn-primary">对比</button>
                </div>
            </div>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        let currentYear = 2025;
        
        // 年度切换
        function changeYear(direction) {
            const newYear = currentYear + direction;
            if (newYear >= 2021 && newYear <= 2025) {
                currentYear = newYear;
                document.getElementById('current-year').textContent = currentYear;
                
                // 更新年度状态
                const statusElement = document.getElementById('year-status');
                if (currentYear === 2025) {
                    statusElement.textContent = '当前年度';
                    statusElement.className = 'year-status year-current';
                } else {
                    statusElement.textContent = '历史年度';
                    statusElement.className = 'year-status year-archived';
                }
                
                // 更新按钮状态
                const prevBtn = document.querySelector('.year-btn');
                const nextBtn = document.querySelectorAll('.year-btn')[1];
                
                prevBtn.disabled = currentYear <= 2021;
                nextBtn.disabled = currentYear >= 2025;
                
                showMessage(`已切换到${currentYear}年度`, 'info');
            }
        }
        
        // 创建新年度台账
        function createNewYear() {
            if (confirm('确定要创建2026年度台账吗？此操作将：\n\n1. 创建新的空白台账\n2. 继承当前配置信息\n3. 将2025年数据归档\n\n请确认是否继续？')) {
                showMessage('正在创建2026年度台账...', 'info');
                
                setTimeout(() => {
                    showMessage('2026年度台账创建成功！', 'success');
                    
                    // 显示成功提示框
                    const successBox = document.createElement('div');
                    successBox.className = 'success-box';
                    successBox.innerHTML = `
                        <div class="success-title">✅ 年度台账创建成功</div>
                        <div class="success-content">
                            • 2026年度空白台账已创建<br>
                            • 部门设置和标签体系已继承<br>
                            • 2025年数据已自动归档<br>
                            • 序号已重新开始编排
                        </div>
                    `;
                    
                    const container = document.querySelector('.page-container');
                    container.insertBefore(successBox, container.children[2]);
                    
                    // 3秒后移除提示框
                    setTimeout(() => {
                        successBox.remove();
                    }, 5000);
                }, 2000);
            }
        }
        
        // 预览新年度配置
        function previewNewYear() {
            showMessage('正在加载配置预览...', 'info');
            
            setTimeout(() => {
                alert('2026年度配置预览：\n\n' +
                      '• 填报单位：12个（继承自2025年）\n' +
                      '• 对口部门：17个（继承自2025年）\n' +
                      '• 关键词标签：15个（继承自2025年）\n' +
                      '• 用户权限：保持现有设置\n' +
                      '• 序号编排：从001开始\n\n' +
                      '配置信息将在创建时自动应用。');
            }, 1000);
        }
        
        // 查看历史数据
        function viewHistory() {
            showMessage('正在加载历史数据...', 'info');
            setTimeout(() => {
                window.location.href = '01-台账查看.html';
            }, 1000);
        }
        
        // 导出历史台账
        function exportHistory() {
            showMessage('正在准备历史台账导出...', 'info');
            setTimeout(() => {
                showMessage('历史台账导出成功！', 'success');
            }, 2000);
        }
        
        // 年度对比
        function compareYears() {
            showMessage('正在生成年度对比报告...', 'info');
            setTimeout(() => {
                alert('年度对比报告：\n\n' +
                      '2024年 vs 2025年：\n' +
                      '• 台账数量：148 → 156 (+8)\n' +
                      '• 完成率：95.3% → 91.0% (-4.3%)\n' +
                      '• 重点工作：45 → 52 (+7)\n' +
                      '• 大监督事项：38 → 41 (+3)\n\n' +
                      '详细对比报告已生成，可在导出功能中下载。');
            }, 2000);
        }
        
        // 数据迁移
        function migrateData() {
            if (confirm('数据迁移是重要操作，建议在系统维护时间进行。\n\n确定要执行数据迁移吗？')) {
                showMessage('正在执行数据迁移...', 'warning');
                
                setTimeout(() => {
                    showMessage('数据迁移完成！', 'success');
                }, 3000);
            }
        }
        
        // 检查迁移状态
        function checkMigration() {
            showMessage('正在检查迁移状态...', 'info');
            setTimeout(() => {
                alert('迁移状态检查结果：\n\n' +
                      '• 数据完整性：✅ 正常\n' +
                      '• 配置继承：✅ 正常\n' +
                      '• 权限更新：✅ 正常\n' +
                      '• 索引重建：✅ 正常\n\n' +
                      '系统运行状态良好。');
            }, 1500);
        }
        
        // 数据备份
        function backupData() {
            showMessage('正在执行数据备份...', 'info');
            setTimeout(() => {
                showMessage('数据备份完成！备份文件已保存到服务器。', 'success');
            }, 2000);
        }
        
        // 恢复数据
        function restoreData() {
            if (confirm('数据恢复将覆盖当前数据，请确认是否继续？\n\n建议在执行前先进行当前数据备份。')) {
                showMessage('正在恢复数据...', 'warning');
                setTimeout(() => {
                    showMessage('数据恢复完成！', 'success');
                }, 3000);
            }
        }
        
        // 备份设置
        function backupSettings() {
            showMessage('正在打开备份设置...', 'info');
            setTimeout(() => {
                alert('备份设置：\n\n' +
                      '• 自动备份：每日凌晨2点\n' +
                      '• 备份保留：30天\n' +
                      '• 备份位置：/backup/ledger/\n' +
                      '• 压缩方式：ZIP格式\n\n' +
                      '如需修改设置，请联系系统管理员。');
            }, 1000);
        }
    </script>
</body>
</html>
