<template>
  <div class="user-management">
    <div class="page-header">
      <div class="header-title">
        <el-button @click="goBack" style="margin-right: 12px;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>用户管理</h1>
          <p>管理系统用户、角色权限和用户归属</p>
        </div>
      </div>
    </div>

    <div class="management-container">
      <!-- 用户列表 -->
      <div class="user-list-section">
        <div class="section-header">
          <h2>用户列表</h2>
          <el-button type="primary" @click="showAddUserDialog = true">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>

        <div class="table-container">
          <el-table :data="users" style="width: 100%" v-loading="loading">
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="real_name" label="真实姓名" width="120" />
            <el-table-column prop="email" label="邮箱" width="200" />
            <el-table-column prop="department" label="所属部门" width="120" />
            <el-table-column prop="role" label="角色" width="100">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.role)">
                  {{ getRoleDisplayName(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="is_active" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'danger'">
                  {{ row.is_active ? '活跃' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="editUser(row)">编辑</el-button>
                <el-button size="small" @click="resetPassword(row)">重置密码</el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteUser(row)"
                  :disabled="row.id === currentUserId"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 用户统计 -->
      <div class="user-stats">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ userStats.total }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ userStats.active }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ userStats.admins }}</div>
            <div class="stat-label">管理员</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ userStats.recent }}</div>
            <div class="stat-label">近期新增</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增用户对话框 -->
    <el-dialog v-model="showAddUserDialog" title="新增用户" width="500px">
      <el-form :model="newUser" :rules="userRules" ref="addUserForm" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="newUser.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="full_name">
          <el-input v-model="newUser.full_name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="newUser.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="newUser.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select
            v-model="newUser.department"
            placeholder="选择部门"
            style="width: 100%"
            filterable
            clearable
            :loading="!departments.length"
            data-testid="new-user-department-select"
            @focus="loadDepartments"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id || dept.name"
              :label="dept.name"
              :value="dept.name"
              :data-testid="`new-user-dept-option-${dept.name}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select
            v-model="newUser.role"
            placeholder="选择角色"
            style="width: 100%"
            data-testid="new-user-role-select"
          >
            <el-option
              label="系统管理员"
              value="admin"
              data-testid="role-option-admin"
            />
            <el-option
              label="管理部门"
              value="manager"
              data-testid="role-option-manager"
            />
            <el-option
              label="基层用户"
              value="user"
              data-testid="role-option-user"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddUserDialog = false">取消</el-button>
        <el-button type="primary" @click="createUser" :loading="submitting">创建</el-button>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog v-model="showEditUserDialog" title="编辑用户" width="500px">
      <el-form :model="editingUser" :rules="editUserRules" ref="editUserForm" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editingUser.username" disabled />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="editingUser.real_name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editingUser.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-select
            v-model="editingUser.department"
            placeholder="选择部门"
            style="width: 100%"
            filterable
            clearable
            :loading="!departments.length"
            data-testid="edit-user-department-select"
            @focus="loadDepartments"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id || dept.name"
              :label="dept.name"
              :value="dept.name"
              :data-testid="`edit-user-dept-option-${dept.name}`"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select
            v-model="editingUser.role"
            placeholder="选择角色"
            style="width: 100%"
            data-testid="edit-user-role-select"
          >
            <el-option
              label="系统管理员"
              value="admin"
              data-testid="edit-role-option-admin"
            />
            <el-option
              label="管理部门"
              value="manager"
              data-testid="edit-role-option-manager"
            />
            <el-option
              label="基层用户"
              value="user"
              data-testid="edit-role-option-user"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="editingUser.is_active" active-text="活跃" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditUserDialog = false">取消</el-button>
        <el-button type="primary" @click="updateUser" :loading="submitting">更新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import api from '@/api'

const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const users = ref([])
const departments = ref([])
const showAddUserDialog = ref(false)
const showEditUserDialog = ref(false)

// 表单数据
const newUser = reactive({
  username: '',
  full_name: '',
  email: '',
  password: '',
  department: '',
  department_id: null,
  role: 'user'
})

const editingUser = reactive({
  id: null,
  username: '',
  real_name: '',
  email: '',
  department: '',
  role: '',
  is_active: true
})

// 表单验证规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const editUserRules = {
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 计算属性
const currentUserId = computed(() => authStore.user?.id)

const userStats = computed(() => {
  const total = users.value.length
  const active = users.value.filter(u => u.is_active).length
  const admins = users.value.filter(u => u.role === 'admin').length
  const recent = users.value.filter(u => {
    const createdAt = new Date(u.created_at)
    const weekAgo = new Date()
    weekAgo.setDate(weekAgo.getDate() - 7)
    return createdAt > weekAgo
  }).length

  return { total, active, admins, recent }
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getRoleTagType = (role) => {
  const types = {
    admin: 'danger',
    manager: 'warning',
    user: 'info'
  }
  return types[role] || 'info'
}

const getRoleDisplayName = (role) => {
  const names = {
    admin: '系统管理员',
    manager: '管理部门',
    user: '基层用户'
  }
  return names[role] || role
}

const loadUsers = async () => {
  loading.value = true
  try {
    // 使用统一的api实例
    const data = await api.get('/users/')
    // 处理多种可能的数据格式
    const newUsers = data.items || data.users || data.data || data || []

    // 强制触发响应式更新
    users.value = []
    await nextTick()
    users.value = [...newUsers]

    console.log('用户数据加载成功:', users.value.length, '个用户')
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 401错误会被api拦截器自动处理
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法加载用户列表')
    } else if (error.message !== 'Token expired') {
      ElMessage.error('加载用户列表失败，请稍后重试')
    }
    users.value = []
  } finally {
    loading.value = false
  }
}

const loadDepartments = async () => {
  try {
    // 只获取填报单位类型的部门
    const data = await api.get('/departments?type=reporting')
    departments.value = data.items || data || []

    // 如果API返回的部门数据为空，使用默认部门列表
    if (departments.value.length === 0) {
      departments.value = getDefaultDepartments()
    }

    console.log('填报单位数据加载成功:', departments.value.length, '个部门')
  } catch (error) {
    console.error('加载部门列表失败:', error)
    // 401错误会被api拦截器自动处理
    if (error.message !== 'Token expired') {
      console.warn('部门列表加载失败，将使用默认部门列表')
      departments.value = getDefaultDepartments()
    }
  }
}

// 获取默认部门列表
const getDefaultDepartments = () => {
  return [
    { id: 1, name: '综合办' },
    { id: 2, name: '人力资源部' },
    { id: 3, name: '财务部' },
    { id: 4, name: '市场部' },
    { id: 5, name: '技术部' },
    { id: 6, name: '运营部' },
    { id: 7, name: '法务部' },
    { id: 8, name: '审计部' },
    { id: 9, name: '安全部' },
    { id: 10, name: '质量部' },
    { id: 11, name: '采购部' },
    { id: 12, name: '客服部' }
  ]
}



const createUser = async () => {
  // 表单验证
  if (!newUser.username || !newUser.full_name || !newUser.email || !newUser.password || !newUser.department) {
    ElMessage.warning('请填写完整的用户信息')
    return
  }

  // 查找部门ID
  const selectedDept = departments.value.find(dept => dept.name === newUser.department)
  if (!selectedDept) {
    ElMessage.error('请选择有效的部门')
    return
  }

  submitting.value = true
  try {
    // 使用统一的api实例，发送正确的字段名
    await api.post('/users', {
      username: newUser.username,
      full_name: newUser.full_name,
      email: newUser.email,
      password: newUser.password,
      department_id: selectedDept.id,
      role: newUser.role
    })

    ElMessage.success('用户创建成功')
    showAddUserDialog.value = false

    // 重置表单
    Object.assign(newUser, {
      username: '',
      full_name: '',
      email: '',
      password: '',
      department: '',
      department_id: null,
      role: 'user'
    })

    await loadUsers()
  } catch (error) {
    console.error('创建用户失败:', error)
    // 401错误会被api拦截器自动处理
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法创建用户')
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || '创建用户失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    submitting.value = false
  }
}

const editUser = (user) => {
  Object.assign(editingUser, {
    id: user.id,
    username: user.username,
    real_name: user.real_name,
    email: user.email,
    department: user.department,
    role: user.role,
    is_active: user.is_active
  })
  showEditUserDialog.value = true
}

const updateUser = async () => {
  submitting.value = true
  try {
    const response = await fetch(`/api/v1/users/${editingUser.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({
        real_name: editingUser.real_name,
        email: editingUser.email,
        department: editingUser.department,
        role: editingUser.role,
        is_active: editingUser.is_active
      })
    })

    if (response.ok) {
      ElMessage.success('用户更新成功')
      showEditUserDialog.value = false
      await loadUsers()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '更新用户失败')
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    ElMessage.error('更新用户失败')
  } finally {
    submitting.value = false
  }
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${user.real_name || user.username}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/v1/users/${user.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      ElMessage.success('用户删除成功')
      await loadUsers()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '删除用户失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

const resetPassword = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户"${user.real_name || user.username}"的密码吗？`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/v1/users/${user.id}/reset-password`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      const data = await response.json()
      ElMessage.success(`密码重置成功，新密码：${data.new_password}`)
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '重置密码失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 返回函数
const goBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  // 检查用户管理权限
  if (!authStore.canManageUsers) {
    ElMessage.warning('您没有用户管理权限，请联系管理员')
    router.push('/ledgers')
    return
  }

  // 有权限则加载数据
  loadUsers()
  loadDepartments()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* 表格容器样式 */
.table-container {
  max-height: 600px;
  overflow: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 20px;
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar,
.user-management::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track,
.user-management::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb,
.user-management::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.user-management::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.page-header {
  margin-bottom: 20px;
}

.header-title {
  display: flex;
  align-items: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.management-container {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
}

.user-list-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  color: #303133;
}

.user-stats {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}
</style>
