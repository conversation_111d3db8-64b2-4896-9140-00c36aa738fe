<template>
  <div class="reminder-management">
    <div class="page-header">
      <div class="header-left">
        <div class="header-title">
          <el-button @click="goBack" style="margin-right: 12px;">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div>
            <h2>提醒管理</h2>
            <p>管理系统提醒规则和通知设置</p>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateRuleDialog" v-if="canManageRules">
          <el-icon><Plus /></el-icon>
          新建提醒规则
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#409eff"><Bell /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total_reminders }}</div>
          <div class="stat-label">总提醒数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#e6a23c"><Clock /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending_reminders }}</div>
          <div class="stat-label">待发送</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#67c23a"><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.sent_reminders }}</div>
          <div class="stat-label">已发送</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#f56c6c"><Warning /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.overdue_reminders }}</div>
          <div class="stat-label">已过期</div>
        </div>
      </div>
    </div>

    <!-- 选项卡 -->
    <el-tabs v-model="activeTab" class="reminder-tabs">
      <!-- 提醒规则 -->
      <el-tab-pane label="提醒规则" name="rules">
        <div class="rules-section">
          <div class="section-header">
            <div class="header-left">
              <h3>提醒规则列表</h3>
            </div>
            <div class="header-right">
              <el-button @click="loadReminderRules">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <el-table 
            :data="reminderRules" 
            v-loading="rulesLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="name" label="规则名称" min-width="150" />
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="work_type" label="工作类型" width="120" />
            <el-table-column prop="department" label="适用部门" width="120" />
            <el-table-column prop="advance_days" label="提前天数" width="100" align="center" />
            <el-table-column prop="is_active" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
                  {{ row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="editRule(row)">编辑</el-button>
                <el-button 
                  size="small" 
                  :type="row.is_active ? 'warning' : 'success'"
                  @click="toggleRuleStatus(row)"
                >
                  {{ row.is_active ? '禁用' : '启用' }}
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="deleteRule(row)"
                  v-if="canManageRules"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="rulesPage"
              v-model:page-size="rulesPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="rulesTotalCount"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadReminderRules"
              @current-change="loadReminderRules"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 我的提醒 -->
      <el-tab-pane label="我的提醒" name="reminders">
        <div class="reminders-section">
          <div class="section-header">
            <div class="header-left">
              <h3>我的提醒记录</h3>
            </div>
            <div class="header-right">
              <el-select v-model="reminderStatusFilter" placeholder="状态筛选" clearable style="width: 120px">
                <el-option label="待发送" value="pending" />
                <el-option label="已发送" value="sent" />
                <el-option label="已读" value="read" />
                <el-option label="已忽略" value="dismissed" />
              </el-select>
              <el-select v-model="reminderPriorityFilter" placeholder="优先级筛选" clearable style="width: 120px">
                <el-option label="低" value="low" />
                <el-option label="普通" value="normal" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
              <el-button @click="loadMyReminders">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <el-table 
            :data="myReminders" 
            v-loading="remindersLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="title" label="提醒标题" min-width="200" />
            <el-table-column prop="content" label="提醒内容" min-width="250" show-overflow-tooltip />
            <el-table-column prop="priority" label="优先级" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ getPriorityLabel(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="scheduled_time" label="计划时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.scheduled_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button 
                  size="small" 
                  v-if="!row.is_read && row.status === 'sent'"
                  @click="markReminderRead(row.id)"
                >
                  标记已读
                </el-button>
                <el-button 
                  size="small" 
                  type="warning"
                  v-if="!row.is_dismissed && row.status !== 'dismissed'"
                  @click="dismissReminder(row.id)"
                >
                  忽略
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="remindersPage"
              v-model:page-size="remindersPageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="remindersTotalCount"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadMyReminders"
              @current-change="loadMyReminders"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建/编辑提醒规则对话框 -->
    <ReminderRuleDialog
      v-model="showRuleDialog"
      :rule="selectedRule"
      @saved="handleRuleSaved"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Bell, Clock, Check, Warning, Refresh, ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import ReminderRuleDialog from '@/components/ReminderRuleDialog.vue'
import * as reminderApi from '@/api/reminder'

// 状态管理
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('rules')
const stats = ref({
  total_reminders: 0,
  pending_reminders: 0,
  sent_reminders: 0,
  read_reminders: 0,
  overdue_reminders: 0,
  total_rules: 0,
  active_rules: 0
})

// 提醒规则相关
const reminderRules = ref([])
const rulesLoading = ref(false)
const rulesPage = ref(1)
const rulesPageSize = ref(20)
const rulesTotalCount = ref(0)
const showRuleDialog = ref(false)
const selectedRule = ref(null)

// 我的提醒相关
const myReminders = ref([])
const remindersLoading = ref(false)
const remindersPage = ref(1)
const remindersPageSize = ref(20)
const remindersTotalCount = ref(0)
const reminderStatusFilter = ref('')
const reminderPriorityFilter = ref('')

// 计算属性
const canManageRules = computed(() => {
  return authStore.canManageReminders
})

// 返回函数
const goBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  loadStats()
  loadReminderRules()
  loadMyReminders()
})

// 方法
const loadStats = async () => {
  try {
    const response = await fetch('/api/v1/reminders/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      stats.value = await response.json()
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadReminderRules = async () => {
  try {
    rulesLoading.value = true
    const skip = (rulesPage.value - 1) * rulesPageSize.value
    const response = await fetch(`/api/v1/reminders/rules?page=${rulesPage.value}&limit=${rulesPageSize.value}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      reminderRules.value = data.items
      rulesTotalCount.value = data.total
    }
  } catch (error) {
    console.error('加载提醒规则失败:', error)
    ElMessage.error('加载提醒规则失败')
  } finally {
    rulesLoading.value = false
  }
}

const loadMyReminders = async () => {
  try {
    remindersLoading.value = true
    const params = new URLSearchParams({
      page: remindersPage.value.toString(),
      limit: remindersPageSize.value.toString()
    })
    
    if (reminderStatusFilter.value) {
      params.append('status', reminderStatusFilter.value)
    }
    if (reminderPriorityFilter.value) {
      params.append('priority', reminderPriorityFilter.value)
    }
    
    const response = await fetch(`/api/v1/reminders/my?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      myReminders.value = data.items
      remindersTotalCount.value = data.total
    }
  } catch (error) {
    console.error('加载我的提醒失败:', error)
    ElMessage.error('加载我的提醒失败')
  } finally {
    remindersLoading.value = false
  }
}

const showCreateRuleDialog = () => {
  selectedRule.value = null
  showRuleDialog.value = true
}

const editRule = (rule: any) => {
  selectedRule.value = rule
  showRuleDialog.value = true
}

const toggleRuleStatus = async (rule: any) => {
  try {
    const response = await fetch(`/api/v1/reminders/rules/${rule.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({
        is_active: !rule.is_active
      })
    })
    
    if (response.ok) {
      ElMessage.success(rule.is_active ? '规则已禁用' : '规则已启用')
      loadReminderRules()
    }
  } catch (error) {
    console.error('切换规则状态失败:', error)
    ElMessage.error('操作失败')
  }
}

const deleteRule = async (rule: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除提醒规则"${rule.name}"吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await fetch(`/api/v1/reminders/rules/${rule.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('提醒规则删除成功')
      loadReminderRules()
      loadStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除提醒规则失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const markReminderRead = async (reminderId: number) => {
  try {
    const response = await fetch(`/api/v1/reminders/${reminderId}/read`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('提醒已标记为已读')
      loadMyReminders()
      loadStats()
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

const dismissReminder = async (reminderId: number) => {
  try {
    const response = await fetch(`/api/v1/reminders/${reminderId}/dismiss`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('提醒已忽略')
      loadMyReminders()
      loadStats()
    }
  } catch (error) {
    console.error('忽略提醒失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleRuleSaved = () => {
  showRuleDialog.value = false
  loadReminderRules()
  loadStats()
}

// 工具方法
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const getPriorityType = (priority: string) => {
  const typeMap = {
    'low': 'info',
    'normal': '',
    'high': 'warning',
    'urgent': 'danger'
  }
  return typeMap[priority] || ''
}

const getPriorityLabel = (priority: string) => {
  const labelMap = {
    'low': '低',
    'normal': '普通',
    'high': '高',
    'urgent': '紧急'
  }
  return labelMap[priority] || priority
}

const getStatusType = (status: string) => {
  const typeMap = {
    'pending': 'warning',
    'sent': 'primary',
    'read': 'success',
    'dismissed': 'info'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap = {
    'pending': '待发送',
    'sent': '已发送',
    'read': '已读',
    'dismissed': '已忽略'
  }
  return labelMap[status] || status
}
</script>

<style scoped>
.reminder-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-title {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.header-left p {
  margin: 0;
  color: var(--el-text-color-secondary);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.reminder-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
