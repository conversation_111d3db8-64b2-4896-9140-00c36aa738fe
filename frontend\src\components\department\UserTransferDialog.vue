<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户调动"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="用户信息">
        <div class="user-info" v-if="user">
          <div class="user-detail">
            <span class="user-name">{{ user.full_name }}</span>
            <span class="user-username">({{ user.username }})</span>
          </div>
          <div class="current-dept">
            当前部门: {{ currentDepartment?.name || '无' }}
          </div>
        </div>
      </el-form-item>

      <el-form-item label="目标部门" prop="new_department_id">
        <el-select 
          v-model="form.new_department_id" 
          placeholder="请选择目标部门" 
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="dept in availableDepartments"
            :key="dept.id"
            :label="`${dept.name} (${getTypeText(dept.type)})`"
            :value="dept.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="调动原因">
        <el-input
          v-model="form.change_reason"
          type="textarea"
          :rows="3"
          placeholder="请输入调动原因（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="生效时间">
        <el-date-picker
          v-model="form.effective_date"
          type="datetime"
          placeholder="选择生效时间"
          style="width: 100%"
          :default-value="new Date()"
        />
        <div class="form-tip">不选择则立即生效</div>
      </el-form-item>

      <!-- 调动预览 -->
      <el-card class="preview-card" shadow="never" v-if="form.new_department_id">
        <template #header>
          <span>调动预览</span>
        </template>
        
        <div class="transfer-preview">
          <div class="transfer-flow">
            <div class="dept-box current">
              <div class="dept-name">{{ currentDepartment?.name || '无部门' }}</div>
              <div class="dept-type">{{ getTypeText(currentDepartment?.type) }}</div>
            </div>
            
            <div class="arrow">
              <el-icon><Right /></el-icon>
            </div>
            
            <div class="dept-box target">
              <div class="dept-name">{{ getTargetDepartmentName() }}</div>
              <div class="dept-type">{{ getTypeText(getTargetDepartment()?.type) }}</div>
            </div>
          </div>
          
          <div class="transfer-info">
            <div class="info-item">
              <span class="info-label">调动用户：</span>
              <span class="info-value">{{ user?.full_name }}</span>
            </div>
            <div class="info-item" v-if="form.effective_date">
              <span class="info-label">生效时间：</span>
              <span class="info-value">{{ formatDateTime(form.effective_date) }}</span>
            </div>
            <div class="info-item" v-if="form.change_reason">
              <span class="info-label">调动原因：</span>
              <span class="info-value">{{ form.change_reason }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.new_department_id"
        >
          {{ loading ? '调动中...' : '确认调动' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Right } from '@element-plus/icons-vue'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  },
  currentDepartment: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const availableDepartments = ref([])

const form = reactive({
  new_department_id: null,
  change_reason: '',
  effective_date: null
})

const rules = {
  new_department_id: [
    { required: true, message: '请选择目标部门', trigger: 'change' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
    loadDepartments()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const loadDepartments = async () => {
  try {
    // 加载所有部门类型
    const [reportingRes, counterpartRes] = await Promise.all([
      fetch('/api/v1/departments/reporting', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      }),
      fetch('/api/v1/departments/counterpart', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      })
    ])
    
    const departments = []
    
    if (reportingRes.ok) {
      const reportingData = await reportingRes.json()
      departments.push(...(reportingData.data || []))
    }
    
    if (counterpartRes.ok) {
      const counterpartData = await counterpartRes.json()
      departments.push(...(counterpartData.data || []))
    }
    
    // 过滤掉当前部门
    availableDepartments.value = departments.filter(dept => 
      dept.id !== props.currentDepartment?.id && dept.status === 'active'
    )
    
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

const resetForm = () => {
  Object.assign(form, {
    new_department_id: null,
    change_reason: '',
    effective_date: null
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const requestData = {
      user_id: props.user.id,
      new_department_id: form.new_department_id,
      change_reason: form.change_reason || null,
      effective_date: form.effective_date || null
    }
    
    const response = await fetch('/api/v1/departments/users/transfer', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify(requestData)
    })
    
    if (response.ok) {
      const result = await response.json()
      
      ElMessage.success({
        message: '用户调动成功',
        duration: 3000
      })
      
      emit('success', result)
      handleClose()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '用户调动失败')
    }
  } catch (error) {
    console.error('用户调动失败:', error)
    ElMessage.error('用户调动失败')
  } finally {
    loading.value = false
  }
}

const getTargetDepartment = () => {
  return availableDepartments.value.find(dept => dept.id === form.new_department_id)
}

const getTargetDepartmentName = () => {
  const dept = getTargetDepartment()
  return dept ? dept.name : '未选择'
}

const getTypeText = (type) => {
  switch (type) {
    case 'reporting': return '填报单位'
    case 'local': return '本单位对口部门'
    case 'provincial': return '省分公司对口部门'
    default: return '未知类型'
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.user-info {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.user-detail {
  margin-bottom: 8px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-username {
  color: #909399;
  margin-left: 8px;
}

.current-dept {
  font-size: 12px;
  color: #606266;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.preview-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.transfer-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.transfer-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.dept-box {
  padding: 12px;
  border-radius: 4px;
  text-align: center;
  min-width: 120px;
}

.dept-box.current {
  background-color: #f0f9ff;
  border: 1px solid #409eff;
}

.dept-box.target {
  background-color: #f0f9ff;
  border: 1px solid #67c23a;
}

.dept-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.dept-type {
  font-size: 12px;
  color: #909399;
}

.arrow {
  color: #409eff;
  font-size: 20px;
}

.transfer-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-value {
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
