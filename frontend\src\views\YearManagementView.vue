<template>
  <div class="year-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <el-button @click="goBack" style="margin-right: 12px;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>年度管理</h1>
          <p>管理年度数据、创建新年度、备份和归档历史数据</p>
        </div>
      </div>
    </div>

    <!-- 年度选择器 -->
    <el-card class="year-selector-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>年度选择器</span>
          <el-button
            type="primary"
            @click="openCreateDialog"
            v-if="canCreateYear"
            data-testid="create-year-button"
          >
            创建新年度
          </el-button>
        </div>
      </template>

      <div class="year-selector">
        <div class="year-list" v-loading="yearsLoading">
          <div
            v-for="year in years"
            :key="year.id"
            :class="['year-item', { 'active': year.year === currentYear, 'archived': year.status === 'archived' }]"
            @click="selectYear(year.year)"
            :data-testid="`year-item-${year.year}`"
          >
            <div class="year-number">{{ year.year }}</div>
            <div class="year-info">
              <div class="year-status">
                <el-tag :type="getYearStatusType(year.status)">{{ getYearStatusText(year.status) }}</el-tag>
              </div>
              <div class="year-stats">
                <span>{{ year.total_ledgers || 0 }}条台账</span>
                <span>完成率 {{ year.completion_rate || 0 }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 年度统计面板 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="18">
        <el-card class="stats-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>{{ currentYear }}年度统计</span>
              <el-button @click="refreshStats" :loading="statsLoading">
                刷新
              </el-button>
            </div>
          </template>

          <div v-if="yearStats" class="stats-content">
            <!-- 基础统计 -->
            <div class="basic-stats">
              <div class="stat-item">
                <div class="stat-number">{{ yearStats.total_ledgers || 0 }}</div>
                <div class="stat-label">台账总数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ yearStats.completed_ledgers || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ yearStats.in_progress_ledgers || 0 }}</div>
                <div class="stat-label">进行中</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ yearStats.draft_ledgers || 0 }}</div>
                <div class="stat-label">草稿</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ yearStats.completion_rate || 0 }}%</div>
                <div class="stat-label">完成率</div>
              </div>
            </div>
          </div>

          <div v-else class="no-stats">
            <el-empty description="暂无统计数据" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="quick-actions-card" shadow="hover">
          <template #header>
            <span>快速操作</span>
          </template>

          <div class="quick-actions">
            <el-button
              type="primary"
              @click="viewYearLedgers"
              block
              data-testid="view-ledgers-button"
            >
              查看台账
            </el-button>

            <el-button
              @click="showBackupDialog = true"
              block
              v-if="authStore.user?.role === 'admin'"
              data-testid="create-backup-button"
            >
              创建备份
            </el-button>

            <el-button
              @click="exportYearData"
              block
              data-testid="export-data-button"
            >
              导出数据
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 创建年度对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新年度"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      :z-index="3000"
      data-testid="create-year-dialog"
    >
      <el-form :model="createForm" label-width="120px" data-testid="create-year-form">
        <el-form-item label="年度">
          <el-input-number
            v-model="createForm.year"
            :min="2020"
            :max="2030"
            placeholder="请输入年度"
            data-testid="year-input"
          />
        </el-form-item>
        <el-form-item label="继承配置">
          <el-switch
            v-model="createForm.inherit_config"
            active-text="继承现有配置"
            inactive-text="不继承配置"
            data-testid="inherit-config-switch"
          />
        </el-form-item>
        <el-form-item label="归档上一年度">
          <el-switch
            v-model="createForm.archive_previous"
            active-text="自动归档"
            inactive-text="保持活跃"
            data-testid="archive-previous-switch"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false" data-testid="cancel-button">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreateYear"
          :loading="createLoading"
          data-testid="create-button"
        >
          {{ createLoading ? '创建中...' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 备份对话框 -->
    <el-dialog
      v-model="showBackupDialog"
      title="创建备份"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
      :z-index="3000"
      data-testid="backup-dialog"
    >
      <el-form :model="backupForm" label-width="120px" data-testid="backup-form">
        <el-form-item label="备份名称">
          <el-input
            v-model="backupForm.backup_name"
            placeholder="请输入备份名称"
            data-testid="backup-name-input"
          />
        </el-form-item>
        <el-form-item label="备份描述">
          <el-input
            v-model="backupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入备份描述（可选）"
            data-testid="backup-description-input"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showBackupDialog = false" data-testid="backup-cancel-button">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreateBackup"
          :loading="backupLoading"
          data-testid="backup-create-button"
        >
          {{ backupLoading ? '创建中...' : '创建备份' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import api from '../api'
import * as yearApi from '../api/year'

// Store
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const years = ref([])
const currentYear = ref(new Date().getFullYear())
const yearStats = ref(null)
const yearsLoading = ref(false)
const statsLoading = ref(false)
const createLoading = ref(false)
const backupLoading = ref(false)

// 对话框状态
const showCreateDialog = ref(false)
const showBackupDialog = ref(false)

// 表单数据
const createForm = reactive({
  year: new Date().getFullYear() + 1,
  inherit_config: true,
  archive_previous: false
})

const backupForm = reactive({
  backup_name: '',
  description: ''
})

// 计算属性
const canCreateYear = computed(() => {
  return authStore.isAdmin || authStore.user?.role === 'admin'
})

// 方法
const openCreateDialog = () => {
  console.log('打开创建年度对话框')
  showCreateDialog.value = true
}
const loadYears = async () => {
  try {
    yearsLoading.value = true
    const response = await fetch('/api/v1/years', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      years.value = data.data || []
    } else {
      ElMessage.error('获取年度列表失败')
    }
  } catch (error) {
    console.error('获取年度列表失败:', error)
    ElMessage.error('获取年度列表失败')
  } finally {
    yearsLoading.value = false
  }
}

const loadYearStats = async (year) => {
  try {
    statsLoading.value = true
    const response = await fetch(`/api/v1/years/${year}/stats`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      yearStats.value = await response.json()
    } else {
      yearStats.value = null
      ElMessage.error('获取年度统计失败')
    }
  } catch (error) {
    console.error('获取年度统计失败:', error)
    yearStats.value = null
    ElMessage.error('获取年度统计失败')
  } finally {
    statsLoading.value = false
  }
}

const selectYear = (year) => {
  currentYear.value = year
  loadYearStats(year)
}

const refreshStats = () => {
  loadYearStats(currentYear.value)
}

const getYearStatusType = (status) => {
  switch (status) {
    case 'active': return 'success'
    case 'archived': return 'info'
    default: return 'info'
  }
}

const getYearStatusText = (status) => {
  switch (status) {
    case 'active': return '当前'
    case 'archived': return '已归档'
    default: return '未知'
  }
}

const viewYearLedgers = () => {
  // 跳转到台账查看页面，并设置年度筛选
  window.location.href = `/#/ledgers?year=${currentYear.value}`
}

const handleCreateYear = async () => {
  try {
    // 表单验证
    if (!createForm.year) {
      ElMessage.warning('请输入年度')
      return
    }

    createLoading.value = true

    // 使用统一的api实例
    await api.post('/years', {
      year: createForm.year,
      inherit_config: createForm.inherit_config,
      archive_previous: createForm.archive_previous
    })

    ElMessage.success('年度创建成功')
    showCreateDialog.value = false

    // 重置表单
    createForm.year = new Date().getFullYear() + 1
    createForm.inherit_config = true
    createForm.archive_previous = false

    await loadYears()
  } catch (error) {
    console.error('年度创建失败:', error)
    // 401错误会被api拦截器自动处理
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法创建年度')
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || '年度创建失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    createLoading.value = false
  }
}

const handleCreateBackup = async () => {
  try {
    backupLoading.value = true
    const response = await fetch('/api/v1/backups', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({
        ...backupForm,
        backup_type: 'manual',
        backup_scope: 'year',
        target_year: currentYear.value
      })
    })

    if (response.ok) {
      ElMessage.success('备份创建成功')
      showBackupDialog.value = false
      backupForm.backup_name = ''
      backupForm.description = ''
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '备份创建失败')
    }
  } catch (error) {
    console.error('备份创建失败:', error)
    ElMessage.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}

const exportYearData = async () => {
  try {
    const response = await fetch(`/api/v1/years/${currentYear.value}/export`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${currentYear.value}年度数据.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)
      ElMessage.success('数据导出成功')
    } else {
      ElMessage.error('数据导出失败')
    }
  } catch (error) {
    console.error('数据导出失败:', error)
    ElMessage.error('数据导出失败')
  }
}

// 返回函数
const goBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  loadYears()
  loadYearStats(currentYear.value)
})
</script>

<style scoped>
.year-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-title {
  display: flex;
  align-items: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.year-selector-card {
  margin-bottom: 20px;
}

.year-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.year-item {
  padding: 16px;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.year-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.year-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.year-item.archived {
  opacity: 0.7;
}

.year-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.year-info {
  font-size: 12px;
  color: #909399;
}

.year-status {
  margin-bottom: 4px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.basic-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.no-stats {
  padding: 40px 0;
}
</style>
