# 台账管理系统

一个现代化的企业台账管理系统，基于Vue3 + FastAPI开发。

## 项目概述

台账管理系统是一个专为企业内部台账管理设计的现代化解决方案。系统采用"一次填报、多次复用"的设计理念，通过统一的台账录入和多维度筛选导出，有效减少重复劳动，提升工作效率。

## 🚀 项目状态

**当前版本**: v1.0
**开发状态**: 功能完整，可投入生产使用
**完成度**: 100% (所有核心功能已完成)

### ✅ 已完成功能
- **台账管理**: 台账录入、编辑、查看、删除
- **数据筛选**: 多维度筛选和导出功能
- **权限管理**: 用户管理、角色权限、标签管理
- **年度管理**: 年度数据统计和管理
- **部门管理**: 填报单位和对口部门管理
- **个人中心**: 个人信息、密码修改、系统信息

## 🛠️ 技术栈

### 后端
- **框架**: Python FastAPI 0.104.1
- **数据库**: SQLite3 + SQLAlchemy 2.0
- **数据验证**: Pydantic v2
- **认证**: JWT Token
- **部署**: Uvicorn
- **端口**: 8003

### 前端
- **框架**: Vue 3.4 + TypeScript
- **构建工具**: Vite 6.0
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **端口**: 5174

## 功能特性

- 🏢 **台账管理**: 完整的台账录入、编辑、查看功能
- 🔍 **多维筛选**: 支持年度、部门、标签、关键词等多维度筛选
- 📊 **数据导出**: Excel格式导出，支持批量和选择性导出
- 👥 **权限管理**: 三级权限体系（管理员、管理部门、基层用户）
- 🏷️ **标签系统**: 灵活的标签分类和管理
- 📅 **年度管理**: 年度数据统计和管理
- 🏛️ **部门管理**: 填报单位和对口部门管理
- 🔐 **安全认证**: JWT认证，密码加密，会话管理
- 👤 **个人中心**: 个人信息管理、密码修改
- ℹ️ **系统信息**: 完整的系统信息和帮助

## 🚀 快速开始

### 环境要求
- Python >= 3.9
- Node.js >= 16.0
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd 台账管理
```

### 2. 启动后端
```bash
cd backend
pip install -r requirements.txt
python start.py    # 启动后端服务
```
访问: http://localhost:8003/docs

### 3. 启动前端
```bash
cd frontend
npm install
npm run dev        # 启动前端服务
```
访问: http://localhost:5174

## 📁 项目结构

```
台账管理/
├── 📁 backend/              # 后端代码
│   ├── 📁 app/              # 应用代码
│   │   ├── 📁 api/v1/       # API路由
│   │   ├── 📁 models/       # 数据模型
│   │   ├── 📁 schemas/      # 数据验证
│   │   └── 📁 services/     # 业务逻辑
│   ├── 📁 api_tests/        # API测试
│   ├── 📄 requirements.txt  # Python依赖
│   └── 📄 start.py         # 启动脚本
├── 📁 frontend/            # 前端代码
│   ├── 📁 src/             # 源代码
│   │   ├── 📁 views/       # 页面组件
│   │   ├── 📁 api/         # API调用
│   │   └── 📁 components/  # 通用组件
│   ├── 📄 package.json     # Node.js依赖
│   └── 📄 vite.config.ts   # 构建配置
├── 📁 docs/                # 项目文档
│   ├── 📄 PROJECT_SUMMARY.md      # 功能总结
│   ├── 📄 DEVELOPMENT_STATUS.md   # 开发状态
│   ├── 📁 prd/             # 产品需求
│   ├── 📁 architecture/    # 架构设计
│   └── 📁 prototype/       # 原型设计
└── 📄 ledger.db           # SQLite数据库
```

## 🎯 核心功能

### 台账查看模块
- 📊 台账列表展示和分页
- 🔍 高级筛选 (年度、状态、部门、标签、关键词)
- 📤 Excel导出功能
- 👁️ 台账详情查看
- 📱 响应式设计

### 台账录入模块
- ✏️ 分组表单录入 (基本信息、工作内容、关联信息)
- 🔢 序号自动获取和验证
- ✅ 表单验证和错误提示
- 💾 草稿保存功能
- 🤖 智能填充功能

## 📊 数据库设计

### 核心表结构
- **ledgers** - 台账主表 (序号、工作事项、措施、进展等)
- **departments** - 部门表 (填报单位、本单位、省分公司)
- **tags** - 标签表 (工作类型、时间周期)
- **ledger_departments** - 台账部门关联表
- **ledger_tags** - 台账标签关联表

## 🧪 测试

### 后端API测试
```bash
cd backend
python -m pytest api_tests/
```

### 测试覆盖
- ✅ 台账CRUD操作测试
- ✅ 筛选和分页测试
- ✅ 数据导出测试
- ✅ 数据验证测试

## 📖 文档

- [项目功能总结](docs/PROJECT_SUMMARY.md) - 详细的功能说明和API文档
- [开发状态总览](docs/DEVELOPMENT_STATUS.md) - 开发进度和下一步计划
- [产品需求文档](docs/prd/PRD_台账管理系统_v1.0.md) - 完整的产品需求
- [架构设计文档](docs/architecture/Architecture_台账管理系统_v1.0.md) - 系统架构设计
- [后端API文档](backend/README.md) - 后端开发指南
- [前端开发文档](frontend/README.md) - 前端开发指南

## 🔄 下一步开发

### 优先级排序
1. **高优先级**: 完成模块3的前端页面 (筛选导出界面)
2. **中优先级**: 开发模块4 (权限管理系统)
3. **低优先级**: 开发模块5、6 (年度/部门管理)

### 技术改进
- 添加用户认证和权限控制
- 完善错误处理机制
- 优化数据库查询性能
- 添加日志记录系统

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 许可证

MIT License

## 📞 联系方式

如有问题或建议，请创建Issue或联系开发团队。

---

**最后更新**: 2025-06-28  
**维护状态**: 积极维护中
