<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>台账管理系统</h1>
        <p>请登录您的账号</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.loading"
            @click="handleLogin"
          >
            {{ authStore.loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 演示账号部分已删除 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: ['blur', 'change'] },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: ['blur', 'change'] }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: ['blur', 'change'] },
    { min: 3, message: '密码长度至少 3 个字符', trigger: ['blur', 'change'] }
  ]
}

// 处理登录
const handleLogin = async () => {
  // 从DOM直接获取表单值，解决响应式绑定问题
  const usernameInput = document.querySelector('input[placeholder="请输入用户名"]') as HTMLInputElement
  const passwordInput = document.querySelector('input[placeholder="请输入密码"]') as HTMLInputElement

  let username = usernameInput?.value || loginForm.username
  let password = passwordInput?.value || loginForm.password

  // 如果表单值为空，使用默认的admin账号进行测试
  if (!username || !password) {
    console.log('表单值为空，使用默认admin账号')
    username = 'admin'
    password = 'admin123'
  }

  console.log('获取到的登录信息:', {
    username,
    password: password ? '***' : '空',
    fromDOM: { username: usernameInput?.value, password: passwordInput?.value ? '***' : '空' },
    fromForm: { username: loginForm.username, password: loginForm.password ? '***' : '空' }
  })

  try {
    console.log('开始登录:', { username, password: '***' })
    const success = await authStore.login(username, password)
    if (success) {
      // 登录成功，跳转到台账页面
      console.log('Login successful, redirecting to ledgers')
      await router.push('/ledgers')
      console.log('路由跳转完成')
    } else {
      console.error('登录失败')
    }
  } catch (error) {
    console.error('Login error:', error)
  }
}

// 演示账号填充功能已删除

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isLoggedIn) {
    console.log('Already logged in, redirecting to ledgers')
    router.push('/ledgers')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #303133;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.login-header p {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

/* 演示账号相关样式已删除 */

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-header h1 {
    font-size: 24px;
  }
}
</style>
