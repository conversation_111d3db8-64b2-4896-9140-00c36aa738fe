<template>
  <div class="about-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>关于系统</h1>
        <p>台账管理系统 - 企业级台账管理解决方案</p>
      </div>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="about-container">
      <el-row :gutter="20">
        <!-- 系统概述 -->
        <el-col :span="24">
          <el-card shadow="hover" class="overview-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><InfoFilled /></el-icon>
                <span>系统概述</span>
              </div>
            </template>
            <div class="overview-content">
              <div class="system-logo">
                <el-icon size="80" color="#409eff"><Document /></el-icon>
                <h2>台账管理系统</h2>
                <p class="version">版本 v1.0.0</p>
              </div>
              <div class="system-description">
                <p>
                  台账管理系统是一个专为企业内部台账管理设计的现代化解决方案。
                  系统采用"一次填报、多次复用"的设计理念，通过统一的台账录入和多维度筛选导出，
                  有效减少重复劳动，提升工作效率。
                </p>
                <p>
                  系统支持三级权限体系、智能标签筛选、年度数据管理等功能，
                  为企业提供完整的台账管理解决方案。
                </p>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 技术架构 -->
        <el-col :span="12">
          <el-card shadow="hover" class="tech-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Setting /></el-icon>
                <span>技术架构</span>
              </div>
            </template>
            <div class="tech-content">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="前端框架">
                  <el-tag type="primary">Vue 3.4</el-tag>
                  <el-tag type="success">TypeScript</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="UI组件库">
                  <el-tag type="primary">Element Plus</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="构建工具">
                  <el-tag type="warning">Vite 6.0</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="状态管理">
                  <el-tag type="info">Pinia</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="后端框架">
                  <el-tag type="danger">FastAPI</el-tag>
                  <el-tag type="success">Python 3.9+</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="数据库">
                  <el-tag type="info">SQLite3</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="ORM框架">
                  <el-tag type="warning">SQLAlchemy</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </el-col>

        <!-- 功能特性 -->
        <el-col :span="12">
          <el-card shadow="hover" class="features-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Star /></el-icon>
                <span>功能特性</span>
              </div>
            </template>
            <div class="features-content">
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>台账录入与管理</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>多维度筛选导出</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>三级权限体系</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>智能标签管理</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>年度数据管理</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>部门组织管理</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>用户权限管理</span>
              </div>
              <div class="feature-item">
                <el-icon color="#67c23a"><Check /></el-icon>
                <span>数据安全保护</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 系统统计 -->
        <el-col :span="12">
          <el-card shadow="hover" class="stats-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><DataAnalysis /></el-icon>
                <span>系统统计</span>
              </div>
            </template>
            <div class="stats-content">
              <el-row :gutter="16">
                <el-col :span="12">
                  <div class="stat-item">
                    <div class="stat-number">{{ systemStats.totalUsers }}</div>
                    <div class="stat-label">用户总数</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="stat-item">
                    <div class="stat-number">{{ systemStats.totalLedgers }}</div>
                    <div class="stat-label">台账总数</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="stat-item">
                    <div class="stat-number">{{ systemStats.totalDepartments }}</div>
                    <div class="stat-label">部门总数</div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="stat-item">
                    <div class="stat-number">{{ systemStats.totalTags }}</div>
                    <div class="stat-label">标签总数</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>

        <!-- 联系信息 -->
        <el-col :span="12">
          <el-card shadow="hover" class="contact-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Phone /></el-icon>
                <span>联系信息</span>
              </div>
            </template>
            <div class="contact-content">
              <div class="contact-item">
                <el-icon><Message /></el-icon>
                <span>技术支持：<EMAIL></span>
              </div>
              <div class="contact-item">
                <el-icon><Phone /></el-icon>
                <span>联系电话：************</span>
              </div>
              <div class="contact-item">
                <el-icon><Location /></el-icon>
                <span>公司地址：北京市朝阳区科技园区</span>
              </div>
              <div class="contact-item">
                <el-icon><Link /></el-icon>
                <span>官方网站：www.ledger-system.com</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 版权信息 -->
        <el-col :span="24">
          <el-card shadow="hover" class="copyright-card">
            <div class="copyright-content">
              <p>© 2025 台账管理系统. 保留所有权利.</p>
              <p>本系统由 <strong>Augment Agent</strong> 开发，基于现代化技术栈构建。</p>
              <div class="build-info">
                <el-tag size="small">构建时间: {{ buildTime }}</el-tag>
                <el-tag size="small" type="success">运行环境: {{ environment }}</el-tag>
                <el-tag size="small" type="warning">API版本: v1.0</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { 
  ArrowLeft, 
  InfoFilled, 
  Document, 
  Setting, 
  Star, 
  Check, 
  DataAnalysis, 
  Phone, 
  Message, 
  Location, 
  Link 
} from '@element-plus/icons-vue'

// 系统统计数据
const systemStats = reactive({
  totalUsers: 0,
  totalLedgers: 0,
  totalDepartments: 0,
  totalTags: 0
})

// 构建信息
const buildTime = ref(new Date().toLocaleString('zh-CN'))
const environment = ref('Production')

// 加载系统统计数据
const loadSystemStats = async () => {
  try {
    // 这里可以调用API获取真实的统计数据
    // const stats = await systemAPI.getStats()
    
    // 模拟数据
    Object.assign(systemStats, {
      totalUsers: 25,
      totalLedgers: 156,
      totalDepartments: 12,
      totalTags: 48
    })
  } catch (error) {
    console.error('加载系统统计失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSystemStats()
})
</script>

<style scoped>
.about-view {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
}

.about-container {
  max-width: 1200px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
}

.overview-card {
  margin-bottom: 20px;
}

.overview-content {
  display: flex;
  gap: 30px;
  align-items: center;
}

.system-logo {
  text-align: center;
  min-width: 200px;
}

.system-logo h2 {
  margin: 15px 0 5px 0;
  color: #303133;
}

.version {
  color: #909399;
  font-size: 14px;
  margin: 0;
}

.system-description {
  flex: 1;
}

.system-description p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 15px;
}

.tech-card, .features-card, .stats-card, .contact-card {
  margin-bottom: 20px;
  height: 100%;
}

.features-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.stats-content {
  padding: 10px 0;
}

.stat-item {
  text-align: center;
  padding: 15px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.copyright-card {
  margin-top: 20px;
}

.copyright-content {
  text-align: center;
  color: #909399;
}

.copyright-content p {
  margin: 8px 0;
}

.build-info {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .about-view {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .overview-content {
    flex-direction: column;
    text-align: center;
  }
  
  .build-info {
    flex-direction: column;
    align-items: center;
  }
}
</style>
