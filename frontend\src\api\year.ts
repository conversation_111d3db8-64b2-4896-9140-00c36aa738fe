/**
 * 年度管理API
 */
import api from './index'

export interface Year {
  id: number
  year: number
  status: 'active' | 'archived' | 'draft'
  total_ledgers: number
  completion_rate: number
  created_at: string
  updated_at: string
}

export interface YearCreate {
  year: number
  inherit_config?: boolean
  archive_previous?: boolean
}

export interface YearStats {
  total_ledgers: number
  completed_ledgers: number
  pending_ledgers: number
  completion_rate: number
  department_stats: Array<{
    department: string
    total: number
    completed: number
    rate: number
  }>
}

export interface YearComparison {
  current_year: number
  previous_year: number
  current_stats: YearStats
  previous_stats: YearStats
  growth_rate: number
}

// 获取年度列表
export const getYears = async (params?: {
  skip?: number
  limit?: number
}): Promise<{
  items: Year[]
  total: number
  page: number
  limit: number
}> => {
  const response = await api.get('/years', { params })
  return response.data
}

// 创建新年度
export const createYear = async (data: YearCreate): Promise<Year> => {
  const response = await api.post('/years', data)
  return response.data
}

// 获取年度详情
export const getYear = async (year: number): Promise<Year> => {
  const response = await api.get(`/years/${year}`)
  return response.data
}

// 更新年度
export const updateYear = async (year: number, data: Partial<YearCreate>): Promise<Year> => {
  const response = await api.put(`/years/${year}`, data)
  return response.data
}

// 删除年度
export const deleteYear = async (year: number): Promise<void> => {
  await api.delete(`/years/${year}`)
}

// 归档年度
export const archiveYear = async (year: number): Promise<Year> => {
  const response = await api.post(`/years/${year}/archive`)
  return response.data
}

// 获取年度统计
export const getYearStats = async (year: number): Promise<YearStats> => {
  const response = await api.get(`/years/${year}/stats`)
  return response.data
}

// 获取年度对比
export const getYearComparison = async (currentYear: number, previousYear: number): Promise<YearComparison> => {
  const response = await api.get(`/years/comparison`, {
    params: { current_year: currentYear, previous_year: previousYear }
  })
  return response.data
}

// 复制年度配置
export const copyYearConfig = async (fromYear: number, toYear: number): Promise<void> => {
  await api.post(`/years/${toYear}/copy-config`, { from_year: fromYear })
}

// 获取可用年度列表（用于下拉选择）
export const getAvailableYears = async (): Promise<number[]> => {
  const response = await api.get('/years/available')
  return response.data
}
