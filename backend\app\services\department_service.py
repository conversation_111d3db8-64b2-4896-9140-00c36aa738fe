"""
部门业务逻辑服务
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from ..models.department import Department
from ..schemas.department import DepartmentResponse


class DepartmentService:
    """部门业务逻辑服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_departments(self, type_filter: Optional[str] = None):
        """
        获取部门列表，支持按类型筛选
        """
        query = self.db.query(Department).filter(Department.status == "active")
        
        if type_filter:
            query = query.filter(Department.type == type_filter)
        
        departments = query.all()
        
        return [
            {
                "id": dept.id,
                "name": dept.name,
                "type": dept.type,
                "status": dept.status
            }
            for dept in departments
        ]
