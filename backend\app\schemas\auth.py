"""
认证相关的Pydantic模型
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr, validator


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    full_name: str = Field(..., min_length=1, max_length=100, description="真实姓名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    department_id: Optional[int] = Field(None, description="所属部门ID")
    role: str = Field(default="user", description="用户角色")
    is_active: bool = Field(default=True, description="是否启用")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    
    @validator('password')
    def validate_password(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c.isalpha() for c in v):
            raise ValueError('密码必须包含至少一个字母')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        """验证用户名格式"""
        if not v.replace('_', '').isalnum():
            raise ValueError('用户名只能包含字母、数字和下划线')
        if v[0].isdigit():
            raise ValueError('用户名不能以数字开头')
        return v


class UserUpdate(BaseModel):
    """更新用户模型"""
    full_name: Optional[str] = Field(None, min_length=1, max_length=100, description="真实姓名")
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    department_id: Optional[int] = Field(None, description="所属部门ID")
    role: Optional[str] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否启用")


class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    department: Optional[str] = Field(None, description="部门名称")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")
    login_count: int = Field(default=0, description="登录次数")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应模型"""
    users: List[UserResponse]
    pagination: dict


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user: UserResponse = Field(..., description="用户信息")


class TokenData(BaseModel):
    """令牌数据模型"""
    user_id: Optional[int] = None
    username: Optional[str] = None
    role: Optional[str] = None


class PasswordChangeRequest(BaseModel):
    """修改密码请求模型"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        """验证新密码强度"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        if not any(c.isdigit() for c in v):
            raise ValueError('密码必须包含至少一个数字')
        if not any(c.isalpha() for c in v):
            raise ValueError('密码必须包含至少一个字母')
        return v


class UserPermissions(BaseModel):
    """用户权限模型"""
    user_id: int
    role: str
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    can_edit_sequence: bool = Field(default=False, description="是否可以编辑序号")
    can_edit_all_ledgers: bool = Field(default=False, description="是否可以编辑所有台账")
    can_manage_users: bool = Field(default=False, description="是否可以管理用户")
    can_manage_tags: bool = Field(default=False, description="是否可以管理标签")
    can_export_data: bool = Field(default=False, description="是否可以导出数据")


class PermissionMatrix(BaseModel):
    """权限矩阵模型"""
    modules: List[dict] = Field(default_factory=list, description="模块权限列表")


class RolePermissions(BaseModel):
    """角色权限模型"""
    role: str = Field(..., description="角色名称")
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    description: str = Field(..., description="角色描述")


class UserSessionInfo(BaseModel):
    """用户会话信息模型"""
    id: int
    token_hash: str
    expires_at: datetime
    is_active: bool
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    last_used: datetime
    
    class Config:
        from_attributes = True


class LoginLogEntry(BaseModel):
    """登录日志条目模型"""
    id: int
    username: str
    login_time: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    success: bool
    failure_reason: Optional[str] = None
    
    class Config:
        from_attributes = True


class UserStats(BaseModel):
    """用户统计模型"""
    total_users: int = Field(..., description="用户总数")
    active_users: int = Field(..., description="活跃用户数")
    admin_count: int = Field(..., description="管理员数量")
    manager_count: int = Field(..., description="管理部门数量")
    user_count: int = Field(..., description="基层用户数量")
    recent_logins: int = Field(..., description="最近登录数量")


class PermissionCheckRequest(BaseModel):
    """权限检查请求模型"""
    permission: str = Field(..., description="权限名称")
    resource_id: Optional[int] = Field(None, description="资源ID")


class PermissionCheckResponse(BaseModel):
    """权限检查响应模型"""
    has_permission: bool = Field(..., description="是否拥有权限")
    reason: Optional[str] = Field(None, description="原因说明")


class ActionValidationRequest(BaseModel):
    """操作验证请求模型"""
    action: str = Field(..., description="操作类型")
    resource_type: str = Field(..., description="资源类型")
    resource_id: Optional[int] = Field(None, description="资源ID")
    data: Optional[dict] = Field(None, description="操作数据")


class ActionValidationResponse(BaseModel):
    """操作验证响应模型"""
    allowed: bool = Field(..., description="是否允许操作")
    reason: Optional[str] = Field(None, description="原因说明")
    warnings: List[str] = Field(default_factory=list, description="警告信息")


# 常用的响应模型
class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str = Field(..., description="消息内容")
    success: bool = Field(default=True, description="是否成功")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    detail: Optional[str] = Field(None, description="详细信息")
    code: Optional[str] = Field(None, description="错误代码")
