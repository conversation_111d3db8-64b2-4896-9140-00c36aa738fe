<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台账录入 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-row .form-label {
            width: 120px;
            margin-bottom: 0;
        }
        
        .form-row .form-control {
            flex: 1;
        }
        
        .rich-editor {
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 120px;
        }
        
        .editor-toolbar {
            background: #f8f9fa;
            padding: 8px;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 5px;
        }
        
        .editor-btn {
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .editor-btn:hover {
            background: #e9ecef;
        }
        
        .editor-content {
            padding: 10px;
            min-height: 80px;
            outline: none;
        }
        
        .checkbox-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .checkbox-grid label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            white-space: nowrap;
        }
        
        .tag-selector {
            display: flex;
            flex-direction: column;
            gap: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .tag-category {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .tag-category-title {
            font-weight: bold;
            color: #666;
            font-size: 13px;
        }
        
        .tag-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag-option {
            background: #f0f8ff;
            color: #1976d2;
            border: 1px solid #1976d2;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            user-select: none;
        }
        
        .tag-option:hover {
            background: #e3f2fd;
        }
        
        .tag-option.selected {
            background: #1976d2;
            color: white;
        }
        
        .form-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .readonly-field {
            background: #f0f8ff;
            border-color: #1976d2;
        }
        
        .field-note {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab active">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="page-container">
        <h2 class="page-title">台账录入页面</h2>
        
        <div class="form-container">
            <form id="ledger-form">
                <!-- 序号 -->
                <div class="form-row">
                    <label class="form-label">序号<span class="required">*</span>：</label>
                    <input type="text" value="001" class="form-control readonly-field" style="width: 100px;" readonly>
                    <span class="field-note">(仅主管可编辑)</span>
                </div>
                
                <!-- 工作事项名称 -->
                <div class="form-row">
                    <label class="form-label">工作事项名称<span class="required">*</span>：</label>
                    <input type="text" placeholder="请输入工作事项名称" class="form-control" required>
                </div>
                
                <!-- 具体措施 -->
                <div class="form-group">
                    <label class="form-label">具体措施<span class="required">*</span>：</label>
                    <div class="rich-editor">
                        <div class="editor-toolbar">
                            <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                            <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                            <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                            <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">• 列表</button>
                        </div>
                        <div class="editor-content" contenteditable="true" placeholder="请详细描述具体措施..."></div>
                    </div>
                </div>
                
                <!-- 进展情况 -->
                <div class="form-group">
                    <label class="form-label">进展情况<span class="required">*</span>：</label>
                    <div class="rich-editor">
                        <div class="editor-toolbar">
                            <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                            <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                            <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                            <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">• 列表</button>
                        </div>
                        <div class="editor-content" contenteditable="true" placeholder="请详细描述当前进展情况..."></div>
                    </div>
                </div>
                
                <!-- 成效描述 -->
                <div class="form-group">
                    <label class="form-label">成效描述：</label>
                    <textarea placeholder="请描述取得的成效（选填）..." class="form-control" rows="4"></textarea>
                </div>
                
                <!-- 责任单位 -->
                <div class="form-row">
                    <label class="form-label">责任单位<span class="required">*</span>：</label>
                    <select class="form-control" style="width: 200px;" required>
                        <option value="">请选择责任单位</option>
                        <option value="综合办" selected>综合办</option>
                        <option value="人力">人力</option>
                        <option value="财务">财务</option>
                        <option value="党建部">党建部</option>
                        <option value="纪委办">纪委办</option>
                        <option value="工会">工会</option>
                        <option value="管控部">管控部</option>
                        <option value="生产中心A">生产中心A</option>
                        <option value="生产中心B">生产中心B</option>
                        <option value="运输中心">运输中心</option>
                        <option value="运维中心">运维中心</option>
                        <option value="团委">团委</option>
                    </select>
                </div>
                
                <!-- 对口部门 -->
                <div class="form-group">
                    <label class="form-label">对口部门<span class="required">*</span>：</label>
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <div class="tag-category-title">本单位：</div>
                            <div class="checkbox-grid">
                                <label><input type="checkbox" name="local_dept" value="综合办" checked> 综合办</label>
                                <label><input type="checkbox" name="local_dept" value="人力"> 人力</label>
                                <label><input type="checkbox" name="local_dept" value="财务"> 财务</label>
                                <label><input type="checkbox" name="local_dept" value="党建部"> 党建部</label>
                                <label><input type="checkbox" name="local_dept" value="纪委办"> 纪委办</label>
                                <label><input type="checkbox" name="local_dept" value="工会"> 工会</label>
                                <label><input type="checkbox" name="local_dept" value="管控部"> 管控部</label>
                                <label><input type="checkbox" name="local_dept" value="团委"> 团委</label>
                            </div>
                        </div>
                        <div>
                            <div class="tag-category-title">省分公司：</div>
                            <div class="checkbox-grid">
                                <label><input type="checkbox" name="prov_dept" value="综合办(党委办)" checked> 综合办(党委办)</label>
                                <label><input type="checkbox" name="prov_dept" value="人力"> 人力</label>
                                <label><input type="checkbox" name="prov_dept" value="财务"> 财务</label>
                                <label><input type="checkbox" name="prov_dept" value="党建"> 党建</label>
                                <label><input type="checkbox" name="prov_dept" value="纪检"> 纪检</label>
                                <label><input type="checkbox" name="prov_dept" value="质服"> 质服</label>
                                <label><input type="checkbox" name="prov_dept" value="运管"> 运管</label>
                                <label><input type="checkbox" name="prov_dept" value="工会"> 工会</label>
                                <label><input type="checkbox" name="prov_dept" value="团委"> 团委</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 关键词标签 -->
                <div class="form-group">
                    <label class="form-label">关键词标签<span class="required">*</span>：</label>
                    <div class="tag-selector">
                        <div class="tag-category">
                            <div class="tag-category-title">工作类型：</div>
                            <div class="tag-options">
                                <span class="tag-option selected" data-value="重点工作">重点工作</span>
                                <span class="tag-option selected" data-value="大监督">大监督</span>
                                <span class="tag-option" data-value="专项工作">专项工作</span>
                                <span class="tag-option" data-value="安全生产">安全生产</span>
                                <span class="tag-option" data-value="质量管控">质量管控</span>
                            </div>
                        </div>
                        <div class="tag-category">
                            <div class="tag-category-title">时间周期：</div>
                            <div class="tag-options">
                                <span class="tag-option" data-value="季度报送">季度报送</span>
                                <span class="tag-option" data-value="半年报送">半年报送</span>
                                <span class="tag-option" data-value="年度报送">年度报送</span>
                                <span class="tag-option" data-value="临时任务">临时任务</span>
                            </div>
                        </div>
                        <div class="tag-category">
                            <div class="tag-category-title">重要程度：</div>
                            <div class="tag-options">
                                <span class="tag-option" data-value="高优先级">高优先级</span>
                                <span class="tag-option" data-value="中优先级">中优先级</span>
                                <span class="tag-option" data-value="低优先级">低优先级</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 备注 -->
                <div class="form-row">
                    <label class="form-label">备注：</label>
                    <input type="text" placeholder="其他需要说明的内容（选填）" class="form-control">
                </div>
                
                <!-- 填报日期 -->
                <div class="form-row">
                    <label class="form-label">填报日期：</label>
                    <input type="text" value="2025-06-26" class="form-control" style="width: 150px; background: #f8f9fa;" readonly>
                    <span class="field-note">(自动记录)</span>
                </div>
                
                <!-- 提交按钮 -->
                <div class="form-buttons">
                    <button type="button" class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                    <button type="submit" class="btn btn-primary">提交</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        // 富文本编辑器功能
        function formatText(command) {
            document.execCommand(command, false, null);
        }
        
        // 标签选择功能
        document.addEventListener('DOMContentLoaded', function() {
            const tagOptions = document.querySelectorAll('.tag-option');
            tagOptions.forEach(option => {
                option.addEventListener('click', function() {
                    this.classList.toggle('selected');
                });
            });
        });
        
        // 保存草稿
        function saveDraft() {
            showMessage('草稿已保存！', 'success');
        }
        
        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？所有填写的内容将丢失。')) {
                document.getElementById('ledger-form').reset();
                
                // 重置标签选择
                const tagOptions = document.querySelectorAll('.tag-option');
                tagOptions.forEach(option => {
                    option.classList.remove('selected');
                });
                
                // 重置富文本编辑器
                const editors = document.querySelectorAll('.editor-content');
                editors.forEach(editor => {
                    editor.innerHTML = '';
                });
                
                showMessage('表单已重置！', 'info');
            }
        }
        
        // 表单提交
        document.getElementById('ledger-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查必填字段
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#f44336';
                    isValid = false;
                } else {
                    field.style.borderColor = '#ddd';
                }
            });
            
            // 检查富文本编辑器
            const editors = document.querySelectorAll('.editor-content');
            editors.forEach(editor => {
                if (!editor.textContent.trim()) {
                    editor.style.borderColor = '#f44336';
                    isValid = false;
                } else {
                    editor.style.borderColor = '#ddd';
                }
            });
            
            // 检查标签选择
            const selectedTags = document.querySelectorAll('.tag-option.selected');
            if (selectedTags.length === 0) {
                showMessage('请至少选择一个关键词标签！', 'warning');
                isValid = false;
            }
            
            // 检查对口部门
            const checkedDepts = document.querySelectorAll('input[name="local_dept"]:checked, input[name="prov_dept"]:checked');
            if (checkedDepts.length === 0) {
                showMessage('请至少选择一个对口部门！', 'warning');
                isValid = false;
            }
            
            if (isValid) {
                showMessage('台账提交成功！', 'success');
                setTimeout(() => {
                    window.location.href = '01-台账查看.html';
                }, 1500);
            } else {
                showMessage('请填写所有必填字段！', 'warning');
            }
        });
        
        // 富文本编辑器占位符处理
        document.querySelectorAll('.editor-content').forEach(editor => {
            const placeholder = editor.getAttribute('placeholder');
            
            editor.addEventListener('focus', function() {
                if (this.textContent === placeholder) {
                    this.textContent = '';
                    this.style.color = '#333';
                }
            });
            
            editor.addEventListener('blur', function() {
                if (this.textContent.trim() === '') {
                    this.textContent = placeholder;
                    this.style.color = '#999';
                }
            });
            
            // 初始化占位符
            if (editor.textContent.trim() === '') {
                editor.textContent = placeholder;
                editor.style.color = '#999';
            }
        });
    </script>
</body>
</html>
