"""
部门数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .base import Base


class Department(Base):
    """部门表模型"""

    __tablename__ = "departments"

    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # 基础字段
    name = Column(String(100), unique=True, nullable=False, index=True, comment="部门名称")
    type = Column(String(20), nullable=False, index=True, comment="部门类型: reporting/local/provincial")
    status = Column(String(20), default="active", nullable=False, index=True, comment="状态: active/inactive")

    # 扩展字段
    sort_order = Column(Integer, default=0, index=True, comment="排序顺序")
    description = Column(Text, nullable=True, comment="部门描述")
    parent_id = Column(Integer, ForeignKey("departments.id"), nullable=True, index=True, comment="上级部门ID")

    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), index=True, comment="更新时间")
    updated_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="更新人ID")

    # 关联关系
    users = relationship("User", foreign_keys="User.department_id", back_populates="department")
    changes = relationship("DepartmentChange", back_populates="department", cascade="all, delete-orphan")
    parent = relationship("Department", remote_side=[id], back_populates="children")
    children = relationship("Department", back_populates="parent")
    updater = relationship("User", foreign_keys=[updated_by])

    def __repr__(self):
        return f"<Department(id={self.id}, name='{self.name}', type='{self.type}')>"

    @property
    def is_active(self) -> bool:
        """是否激活"""
        return self.status == "active"

    @property
    def user_count(self) -> int:
        """用户数量"""
        return len([user for user in self.users if user.is_active])

    @property
    def has_children(self) -> bool:
        """是否有下级部门"""
        return len(self.children) > 0

    @property
    def is_reporting_unit(self) -> bool:
        """是否为填报单位"""
        return self.type == "reporting"

    @property
    def is_local_counterpart(self) -> bool:
        """是否为本单位对口部门"""
        return self.type == "local"

    @property
    def is_provincial_counterpart(self) -> bool:
        """是否为省分公司对口部门"""
        return self.type == "provincial"

    @property
    def user_count(self) -> int:
        """部门用户数量"""
        return len([user for user in self.users if user.is_active])

    @property
    def has_children(self) -> bool:
        """是否有下级部门"""
        return len(self.children) > 0

    def get_all_children(self) -> list:
        """获取所有下级部门（递归）"""
        all_children = []
        for child in self.children:
            all_children.append(child)
            all_children.extend(child.get_all_children())
        return all_children
