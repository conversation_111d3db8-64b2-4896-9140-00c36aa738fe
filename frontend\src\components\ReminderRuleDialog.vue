<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑提醒规则' : '新建提醒规则'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="left"
    >
      <el-form-item label="规则名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入规则名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="规则描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入规则描述"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="工作类型" prop="work_type">
        <el-select
          v-model="formData.work_type"
          placeholder="请选择工作类型"
          clearable
          style="width: 100%"
        >
          <el-option label="重点工作" value="重点工作" />
          <el-option label="大监督" value="大监督" />
          <el-option label="专项工作" value="专项工作" />
          <el-option label="安全生产" value="安全生产" />
          <el-option label="质量管控" value="质量管控" />
          <el-option label="党建工作" value="党建工作" />
          <el-option label="业务发展" value="业务发展" />
        </el-select>
      </el-form-item>

      <el-form-item label="适用部门" prop="department">
        <el-select
          v-model="formData.department"
          placeholder="请选择适用部门"
          clearable
          style="width: 100%"
        >
          <el-option label="综合办" value="综合办" />
          <el-option label="人力资源部" value="人力资源部" />
          <el-option label="财务部" value="财务部" />
          <el-option label="市场部" value="市场部" />
          <el-option label="技术部" value="技术部" />
          <el-option label="运营部" value="运营部" />
          <el-option label="法务部" value="法务部" />
          <el-option label="审计部" value="审计部" />
          <el-option label="安全部" value="安全部" />
          <el-option label="质量部" value="质量部" />
        </el-select>
      </el-form-item>

      <el-form-item label="适用标签" prop="tags">
        <el-select
          v-model="formData.tags"
          placeholder="请选择适用标签"
          multiple
          clearable
          style="width: 100%"
        >
          <el-option label="重点工作" value="重点工作" />
          <el-option label="创新项目" value="创新项目" />
          <el-option label="季度报送" value="季度报送" />
          <el-option label="半年报送" value="半年报送" />
          <el-option label="年度报送" value="年度报送" />
          <el-option label="临时任务" value="临时任务" />
          <el-option label="长期跟踪" value="长期跟踪" />
        </el-select>
      </el-form-item>

      <el-form-item label="提前天数" prop="advance_days">
        <el-input-number
          v-model="formData.advance_days"
          :min="1"
          :max="365"
          placeholder="提前天数"
          style="width: 100%"
        />
        <div class="form-tip">设置在截止日期前多少天发送提醒</div>
      </el-form-item>

      <el-form-item label="提醒频率" prop="reminder_frequency">
        <el-radio-group v-model="formData.reminder_frequency">
          <el-radio label="once">仅一次</el-radio>
          <el-radio label="daily">每日</el-radio>
          <el-radio label="weekly">每周</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="升级提醒">
        <el-switch
          v-model="formData.enable_escalation"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item
        v-if="formData.enable_escalation"
        label="升级间隔天数"
        prop="escalation_days"
      >
        <el-input-number
          v-model="formData.escalation_days"
          :min="1"
          :max="30"
          placeholder="升级间隔天数"
          style="width: 100%"
        />
        <div class="form-tip">超过截止日期后，每隔多少天发送升级提醒</div>
      </el-form-item>

      <el-form-item label="规则状态">
        <el-switch
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
  rule?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  rule: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  saved: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  name: '',
  description: '',
  work_type: '',
  department: '',
  tags: [] as string[],
  advance_days: 7,
  reminder_frequency: 'once',
  enable_escalation: false,
  escalation_days: 3,
  is_active: true
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.rule)

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 100, message: '规则名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  advance_days: [
    { required: true, message: '请输入提前天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '提前天数必须在 1 到 365 之间', trigger: 'blur' }
  ],
  escalation_days: [
    { type: 'number', min: 1, max: 30, message: '升级间隔天数必须在 1 到 30 之间', trigger: 'blur' }
  ]
}

// 监听规则变化，初始化表单数据
watch(() => props.rule, (newRule) => {
  if (newRule) {
    Object.assign(formData, {
      name: newRule.name || '',
      description: newRule.description || '',
      work_type: newRule.work_type || '',
      department: newRule.department || '',
      tags: newRule.tags || [],
      advance_days: newRule.advance_days || 7,
      reminder_frequency: newRule.reminder_frequency || 'once',
      enable_escalation: newRule.enable_escalation || false,
      escalation_days: newRule.escalation_days || 3,
      is_active: newRule.is_active !== undefined ? newRule.is_active : true
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      name: '',
      description: '',
      work_type: '',
      department: '',
      tags: [],
      advance_days: 7,
      reminder_frequency: 'once',
      enable_escalation: false,
      escalation_days: 3,
      is_active: true
    })
  }
}, { immediate: true })

// 方法
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:modelValue', false)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const url = isEdit.value 
      ? `/api/v1/reminders/rules/${props.rule.id}`
      : '/api/v1/reminders/rules'
    
    const method = isEdit.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify(formData)
    })

    if (response.ok) {
      ElMessage.success(isEdit.value ? '规则更新成功' : '规则创建成功')
      emit('saved')
      handleClose()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '操作失败')
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
