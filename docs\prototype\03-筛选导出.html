<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选导出 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }
        
        .step-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .step-number.active {
            background: #1976d2;
            color: white;
        }
        
        .step-number.inactive {
            background: #ddd;
            color: #666;
        }
        
        .step-text {
            font-weight: bold;
        }
        
        .step-text.active {
            color: #1976d2;
        }
        
        .step-text.inactive {
            color: #666;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #ddd;
        }
        
        .step-content {
            display: none;
        }
        
        .step-content.active {
            display: block;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .filter-card {
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .filter-card h4 {
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tag-filter-group {
            margin-bottom: 15px;
        }
        
        .tag-filter-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #666;
            font-size: 13px;
        }
        
        .tag-filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag-filter-option {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            background: white;
            padding: 5px 10px;
            border-radius: 15px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }
        
        .tag-filter-option:hover {
            background: #f0f8ff;
            border-color: #1976d2;
        }
        
        .dept-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .preview-table {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            margin-bottom: 20px;
        }
        
        .preview-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .result-highlight {
            color: #e91e63;
            font-weight: bold;
            font-size: 18px;
        }
        
        .format-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .format-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .format-option.selected {
            border: 2px solid #1976d2;
            background: #f0f8ff;
        }
        
        .format-option:not(.selected) {
            border: 1px solid #ddd;
            background: white;
        }
        
        .format-option:hover {
            background: #f8f9fa;
        }
        
        .format-info {
            display: flex;
            flex-direction: column;
        }
        
        .format-title {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .format-desc {
            font-size: 12px;
            color: #666;
        }
        
        .export-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }
        
        .export-tip {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
        }
        
        .tip-title {
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 5px;
        }
        
        .tip-content {
            font-size: 13px;
            color: #2e7d32;
            line-height: 1.5;
        }
        
        .step-buttons {
            text-align: center;
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab active">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="page-container">
        <h2 class="page-title">筛选导出页面</h2>
        
        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step-container">
                <div class="step-item">
                    <div class="step-number active" id="step-num-1">1</div>
                    <span class="step-text active" id="step-text-1">选择筛选条件</span>
                </div>
                <div class="step-line"></div>
                <div class="step-item">
                    <div class="step-number inactive" id="step-num-2">2</div>
                    <span class="step-text inactive" id="step-text-2">预览结果</span>
                </div>
                <div class="step-line"></div>
                <div class="step-item">
                    <div class="step-number inactive" id="step-num-3">3</div>
                    <span class="step-text inactive" id="step-text-3">导出设置</span>
                </div>
            </div>
        </div>
        
        <!-- 第一步：筛选条件 -->
        <div id="step-1" class="step-content active">
            <h3 style="color: #1976d2; margin-bottom: 20px;">第一步：选择筛选条件</h3>
            <div class="filter-grid">
                <!-- 左列 -->
                <div>
                    <!-- 按关键词标签筛选 -->
                    <div class="filter-card">
                        <h4>🏷️ 按关键词标签筛选</h4>
                        <div class="tag-filter-group">
                            <div class="tag-filter-title">工作类型：</div>
                            <div class="tag-filter-options">
                                <label class="tag-filter-option">
                                    <input type="checkbox" checked> 重点工作
                                </label>
                                <label class="tag-filter-option">
                                    <input type="checkbox" checked> 大监督
                                </label>
                                <label class="tag-filter-option">
                                    <input type="checkbox"> 专项工作
                                </label>
                                <label class="tag-filter-option">
                                    <input type="checkbox"> 安全生产
                                </label>
                            </div>
                        </div>
                        <div class="tag-filter-group">
                            <div class="tag-filter-title">时间周期：</div>
                            <div class="tag-filter-options">
                                <label class="tag-filter-option">
                                    <input type="checkbox"> 季度报送
                                </label>
                                <label class="tag-filter-option">
                                    <input type="checkbox"> 半年报送
                                </label>
                                <label class="tag-filter-option">
                                    <input type="checkbox"> 年度报送
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 按责任单位筛选 -->
                    <div class="filter-card">
                        <h4>🏢 按责任单位筛选</h4>
                        <div class="dept-grid">
                            <label><input type="checkbox" checked> 综合办</label>
                            <label><input type="checkbox"> 人力</label>
                            <label><input type="checkbox"> 财务</label>
                            <label><input type="checkbox"> 党建部</label>
                            <label><input type="checkbox"> 纪委办</label>
                            <label><input type="checkbox"> 工会</label>
                            <label><input type="checkbox"> 管控部</label>
                            <label><input type="checkbox"> 生产中心A</label>
                            <label><input type="checkbox"> 生产中心B</label>
                            <label><input type="checkbox"> 运输中心</label>
                            <label><input type="checkbox"> 运维中心</label>
                            <label><input type="checkbox"> 团委</label>
                        </div>
                    </div>
                </div>
                
                <!-- 右列 -->
                <div>
                    <!-- 按对口部门筛选 -->
                    <div class="filter-card">
                        <h4>🏛️ 按对口部门筛选</h4>
                        <div class="tag-filter-group">
                            <div class="tag-filter-title">本单位：</div>
                            <div class="dept-grid">
                                <label><input type="checkbox" checked> 综合办</label>
                                <label><input type="checkbox"> 人力</label>
                                <label><input type="checkbox"> 财务</label>
                                <label><input type="checkbox"> 党建部</label>
                                <label><input type="checkbox"> 纪委办</label>
                                <label><input type="checkbox"> 工会</label>
                                <label><input type="checkbox"> 管控部</label>
                                <label><input type="checkbox"> 团委</label>
                            </div>
                        </div>
                        <div class="tag-filter-group">
                            <div class="tag-filter-title">省分公司：</div>
                            <div class="dept-grid">
                                <label><input type="checkbox" checked> 综合办(党委办)</label>
                                <label><input type="checkbox"> 人力</label>
                                <label><input type="checkbox"> 财务</label>
                                <label><input type="checkbox"> 党建</label>
                                <label><input type="checkbox"> 纪检</label>
                                <label><input type="checkbox"> 质服</label>
                                <label><input type="checkbox"> 运管</label>
                                <label><input type="checkbox"> 工会</label>
                                <label><input type="checkbox"> 团委</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 按时间范围筛选 -->
                    <div class="filter-card">
                        <h4>📅 按时间范围筛选</h4>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <span>从：</span>
                            <input type="date" value="2025-01-01" class="form-control" style="width: auto;">
                            <span>到：</span>
                            <input type="date" value="2025-12-31" class="form-control" style="width: auto;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="step-buttons">
                <button class="btn btn-primary" onclick="goToStep(2)">下一步：预览结果</button>
            </div>
        </div>
        
        <!-- 第二步：预览结果 -->
        <div id="step-2" class="step-content">
            <h3 style="color: #1976d2; margin-bottom: 20px;">第二步：预览筛选结果</h3>
            <div class="preview-header">
                <h4 style="color: #1976d2; margin: 0;">筛选结果：共找到 <span class="result-highlight">15</span> 条记录</h4>
                <button class="btn btn-secondary" onclick="goToStep(1)">返回修改条件</button>
            </div>
            <div class="preview-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>工作事项</th>
                            <th>责任单位</th>
                            <th>关键词标签</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>001</td>
                            <td>安全生产检查工作</td>
                            <td>综合办</td>
                            <td>重点工作, 大监督</td>
                        </tr>
                        <tr>
                            <td>003</td>
                            <td>质量管控体系建设</td>
                            <td>管控部</td>
                            <td>重点工作, 专项工作</td>
                        </tr>
                        <tr>
                            <td>005</td>
                            <td>人员培训计划实施</td>
                            <td>人力</td>
                            <td>重点工作</td>
                        </tr>
                        <tr>
                            <td>007</td>
                            <td>财务预算执行监控</td>
                            <td>财务</td>
                            <td>大监督</td>
                        </tr>
                        <tr>
                            <td>009</td>
                            <td>党建工作创新实践</td>
                            <td>党建部</td>
                            <td>重点工作, 专项工作</td>
                        </tr>
                        <tr style="color: #666; text-align: center;">
                            <td colspan="4" style="padding: 15px;">... 还有10条记录 ...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="step-buttons">
                <button class="btn btn-secondary" onclick="goToStep(1)">上一步</button>
                <button class="btn btn-primary" onclick="goToStep(3)">下一步：导出设置</button>
            </div>
        </div>
        
        <!-- 第三步：导出设置 -->
        <div id="step-3" class="step-content">
            <h3 style="color: #1976d2; margin-bottom: 20px;">第三步：选择导出格式</h3>
            
            <div class="filter-grid">
                <!-- 导出格式选择 -->
                <div>
                    <h4 style="margin-bottom: 15px; color: #555;">选择导出格式：</h4>
                    <div class="format-options">
                        <label class="format-option selected">
                            <input type="radio" name="format" value="excel" checked style="transform: scale(1.2);">
                            <div class="format-info">
                                <div class="format-title" style="color: #1976d2;">Excel格式 (.xlsx)</div>
                                <div class="format-desc">推荐格式，便于数据分析和二次处理</div>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="word" style="transform: scale(1.2);">
                            <div class="format-info">
                                <div class="format-title">Word格式 (.docx)</div>
                                <div class="format-desc">适合制作报告文档和打印</div>
                            </div>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="format" value="pdf" style="transform: scale(1.2);">
                            <div class="format-info">
                                <div class="format-title">PDF格式 (.pdf)</div>
                                <div class="format-desc">适合存档和正式文档</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                <!-- 导出模板选择 -->
                <div>
                    <h4 style="margin-bottom: 15px; color: #555;">选择导出模板：</h4>
                    <div style="margin-bottom: 20px;">
                        <select class="form-control">
                            <option value="standard">标准台账模板</option>
                            <option value="summary">汇总报告模板</option>
                            <option value="detail">详细信息模板</option>
                            <option value="custom">自定义模板</option>
                        </select>
                    </div>
                    
                    <h4 style="margin-bottom: 15px; color: #555;">导出选项：</h4>
                    <div class="export-options">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" checked>
                            <span>包含批注信息</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" checked>
                            <span>包含关键词标签</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox">
                            <span>包含历史修改记录</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox">
                            <span>生成目录索引</span>
                        </label>
                    </div>
                    
                    <div class="export-tip">
                        <div class="tip-title">💡 导出提示</div>
                        <div class="tip-content">
                            • Excel格式支持数据筛选和图表制作<br>
                            • 导出后可在Excel中进行数据分析<br>
                            • 建议选择"包含关键词标签"便于分类
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="step-buttons">
                <button class="btn btn-secondary" onclick="goToStep(2)">上一步</button>
                <button class="btn btn-success" onclick="exportFile()">🔽 确认导出</button>
            </div>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        // 步骤切换功能
        function goToStep(stepNumber) {
            // 隐藏所有步骤内容
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 显示目标步骤
            document.getElementById(`step-${stepNumber}`).classList.add('active');
            
            // 更新步骤指示器
            for (let i = 1; i <= 3; i++) {
                const stepNum = document.getElementById(`step-num-${i}`);
                const stepText = document.getElementById(`step-text-${i}`);
                
                if (i <= stepNumber) {
                    stepNum.classList.remove('inactive');
                    stepNum.classList.add('active');
                    stepText.classList.remove('inactive');
                    stepText.classList.add('active');
                } else {
                    stepNum.classList.remove('active');
                    stepNum.classList.add('inactive');
                    stepText.classList.remove('active');
                    stepText.classList.add('inactive');
                }
            }
        }
        
        // 导出文件功能
        function exportFile() {
            const format = document.querySelector('input[name="format"]:checked').value;
            const template = document.querySelector('select').value;
            
            showMessage('正在生成导出文件...', 'info');
            
            // 模拟导出过程
            setTimeout(() => {
                showMessage(`${format.toUpperCase()}文件导出成功！`, 'success');
                
                // 模拟文件下载
                const link = document.createElement('a');
                link.href = '#';
                link.download = `台账导出_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : format === 'word' ? 'docx' : 'pdf'}`;
                link.click();
            }, 2000);
        }
        
        // 格式选择功能
        document.addEventListener('DOMContentLoaded', function() {
            const formatOptions = document.querySelectorAll('.format-option');
            formatOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有选中状态
                    formatOptions.forEach(opt => opt.classList.remove('selected'));
                    // 添加当前选中状态
                    this.classList.add('selected');
                    // 选中对应的单选按钮
                    this.querySelector('input[type="radio"]').checked = true;
                });
            });
        });
    </script>
</body>
</html>
