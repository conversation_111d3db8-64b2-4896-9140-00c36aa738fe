/**
 * 部门相关 API
 */
import api from './index'

// 部门数据类型定义
export interface Department {
  id: number
  name: string
  type: 'reporting' | 'local' | 'provincial'
}

// 部门 API 方法
export const departmentApi = {
  // 获取部门列表
  async getList(): Promise<Department[]> {
    console.log('departmentApi.getList 开始调用')
    const response = await api.get('/departments')
    console.log('departmentApi.getList 原始响应:', response)

    // 修复响应数据解析 - axios拦截器已经返回了response.data
    if (Array.isArray(response)) {
      return response
    } else if (response && response.data && Array.isArray(response.data)) {
      return response.data
    } else {
      console.warn('部门API响应格式异常:', response)
      return []
    }
  },

  // 按类型获取部门
  async getByType(type: Department['type']): Promise<Department[]> {
    const departments = await this.getList()
    return departments.filter(dept => dept.type === type)
  },

  // 获取填报单位
  async getReportingUnits(): Promise<Department[]> {
    return this.getByType('reporting')
  },

  // 获取本单位对口部门
  async getLocalDepartments(): Promise<Department[]> {
    return this.getByType('local')
  },

  // 获取省分公司对口部门
  async getProvincialDepartments(): Promise<Department[]> {
    return this.getByType('provincial')
  }
}
