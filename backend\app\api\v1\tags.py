"""
标签管理API路由（支持权限控制）
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status, Request
from sqlalchemy.orm import Session
from ...api.deps import get_db, get_current_user, require_manager_or_admin
from ...core.security import get_client_ip, log_security_event
from ...models.user import User
from ...models.tag import Tag
from ...schemas.tag import TagResponse, TagCreate, TagUpdate
from ...schemas.auth import MessageResponse

router = APIRouter()


@router.get("/", summary="获取标签列表")
async def get_tags(
    category: Optional[str] = Query(None, description="标签分类筛选: work_type/time_cycle/priority/department"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取标签列表，支持按分类筛选
    所有用户都可以查看标签
    """
    query = db.query(Tag)

    # 分类筛选
    if category:
        query = query.filter(Tag.category == category)

    tags = query.all()

    # 返回与前端期望格式一致的数据
    tag_list = [
        {
            "id": tag.id,
            "name": tag.name,
            "category": tag.category,
            "created_at": tag.created_at.isoformat() if tag.created_at else None
        } for tag in tags
    ]

    # 同时支持直接数组和包含data字段的格式
    return {
        "data": tag_list,  # 前端某些地方期望的格式
        "items": tag_list,  # 兼容其他格式
        "tags": tag_list   # 直接返回数组时的兼容
    } if len(tag_list) > 0 else tag_list


@router.post("/", response_model=TagResponse, summary="创建标签")
async def create_tag(
    request: Request,
    tag_data: TagCreate,
    current_user: User = Depends(require_manager_or_admin),
    db: Session = Depends(get_db)
):
    """
    创建新标签
    需要管理部门或管理员权限
    """
    # 检查标签名是否已存在
    existing_tag = db.query(Tag).filter(
        Tag.name == tag_data.name,
        Tag.category == tag_data.category
    ).first()

    if existing_tag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"标签 '{tag_data.name}' 在分类 '{tag_data.category}' 中已存在"
        )

    # 创建标签
    tag = Tag(
        name=tag_data.name,
        category=tag_data.category
    )

    db.add(tag)
    db.commit()
    db.refresh(tag)

    # 记录操作日志
    log_security_event(
        "tag_created",
        current_user.id,
        {
            "tag_id": tag.id,
            "tag_name": tag.name,
            "tag_category": tag.category
        },
        get_client_ip(request)
    )

    return TagResponse(
        id=tag.id,
        name=tag.name,
        category=tag.category,
        created_at=tag.created_at
    )


@router.put("/{tag_id}", response_model=TagResponse, summary="更新标签")
async def update_tag(
    tag_id: int,
    request: Request,
    tag_data: TagUpdate,
    current_user: User = Depends(require_manager_or_admin),
    db: Session = Depends(get_db)
):
    """
    更新标签信息
    需要管理部门或管理员权限
    """
    tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )

    # 检查新名称是否与其他标签冲突
    if tag_data.name and tag_data.name != tag.name:
        existing_tag = db.query(Tag).filter(
            Tag.name == tag_data.name,
            Tag.category == (tag_data.category or tag.category),
            Tag.id != tag_id
        ).first()

        if existing_tag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"标签名 '{tag_data.name}' 已存在"
            )

    # 更新标签
    old_data = {"name": tag.name, "category": tag.category}

    if tag_data.name:
        tag.name = tag_data.name
    if tag_data.category:
        tag.category = tag_data.category

    db.commit()
    db.refresh(tag)

    # 记录操作日志
    log_security_event(
        "tag_updated",
        current_user.id,
        {
            "tag_id": tag.id,
            "old_data": old_data,
            "new_data": {"name": tag.name, "category": tag.category}
        },
        get_client_ip(request)
    )

    return TagResponse(
        id=tag.id,
        name=tag.name,
        category=tag.category,
        created_at=tag.created_at
    )


@router.delete("/{tag_id}", response_model=MessageResponse, summary="删除标签")
async def delete_tag(
    tag_id: int,
    request: Request,
    current_user: User = Depends(require_manager_or_admin),
    db: Session = Depends(get_db)
):
    """
    删除标签
    需要管理部门或管理员权限
    """
    tag = db.query(Tag).filter(Tag.id == tag_id).first()
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )

    # 检查标签是否被使用
    # 这里可以添加检查台账是否使用了该标签的逻辑
    # from ...models.ledger import Ledger
    # ledger_count = db.query(Ledger).filter(Ledger.tags.contains(tag.name)).count()
    # if ledger_count > 0:
    #     raise HTTPException(
    #         status_code=status.HTTP_400_BAD_REQUEST,
    #         detail=f"标签正在被 {ledger_count} 个台账使用，无法删除"
    #     )

    tag_name = tag.name
    tag_category = tag.category

    db.delete(tag)
    db.commit()

    # 记录操作日志
    log_security_event(
        "tag_deleted",
        current_user.id,
        {
            "tag_id": tag_id,
            "tag_name": tag_name,
            "tag_category": tag_category
        },
        get_client_ip(request)
    )

    return MessageResponse(message=f"标签 '{tag_name}' 已删除")


@router.get("/categories", summary="获取标签分类列表")
async def get_tag_categories(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取所有标签分类
    """
    categories = db.query(Tag.category).distinct().all()
    category_list = [cat[0] for cat in categories if cat[0]]

    return {
        "categories": category_list,
        "descriptions": {
            "work_type": "工作类型",
            "time_cycle": "时间周期",
            "priority": "优先级",
            "department": "部门归属"
        }
    }


@router.get("/permissions", summary="获取标签管理权限")
async def get_tag_permissions(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户的标签管理权限
    """
    can_manage = current_user.can_manage_tags

    return {
        "can_create": can_manage,
        "can_update": can_manage,
        "can_delete": can_manage,
        "can_view": True,  # 所有用户都可以查看
        "role": current_user.role,
        "message": "管理部门和管理员可以管理标签，基层用户只能查看和选择" if not can_manage else "您拥有标签管理权限"
    }
