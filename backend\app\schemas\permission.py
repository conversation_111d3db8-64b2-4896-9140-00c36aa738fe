"""
权限管理相关的Pydantic模型
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime

class PermissionBase(BaseModel):
    """权限基础模型"""
    name: str
    code: str
    description: str
    module: str

class PermissionCreate(PermissionBase):
    """创建权限模型"""
    pass

class PermissionUpdate(BaseModel):
    """更新权限模型"""
    name: Optional[str] = None
    description: Optional[str] = None

class PermissionResponse(PermissionBase):
    """权限响应模型"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True

class RoleBase(BaseModel):
    """角色基础模型"""
    name: str
    code: str
    description: str

class RoleCreate(RoleBase):
    """创建角色模型"""
    permission_codes: List[str] = []

class RoleUpdate(BaseModel):
    """更新角色模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    permission_codes: Optional[List[str]] = None

class RoleResponse(RoleBase):
    """角色响应模型"""
    id: int
    permissions: List[str] = []
    created_at: datetime

    class Config:
        from_attributes = True

class UserRoleUpdate(BaseModel):
    """用户角色更新模型"""
    user_id: int
    role_codes: List[str]

class RolePermissionUpdate(BaseModel):
    """角色权限更新模型"""
    role_id: int
    permission_codes: List[str]

class PermissionMatrixItem(BaseModel):
    """权限矩阵项模型"""
    name: str
    code: str
    roles: Dict[str, bool]

class PermissionMatrixModule(BaseModel):
    """权限矩阵模块模型"""
    module: str
    permissions: List[PermissionMatrixItem]

class PermissionMatrixResponse(BaseModel):
    """权限矩阵响应模型"""
    matrix: List[PermissionMatrixModule]

class UserPermissionCheck(BaseModel):
    """用户权限检查模型"""
    user_id: int
    permission_code: str

class UserPermissionCheckResponse(BaseModel):
    """用户权限检查响应模型"""
    has_permission: bool

class BatchUserRoleUpdate(BaseModel):
    """批量用户角色更新模型"""
    updates: List[UserRoleUpdate]

class BatchRolePermissionUpdate(BaseModel):
    """批量角色权限更新模型"""
    updates: List[RolePermissionUpdate]
