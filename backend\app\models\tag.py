"""
标签数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func
from .base import Base


class Tag(Base):
    """标签表模型"""

    __tablename__ = "tags"

    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # 基础字段
    name = Column(String(100), nullable=False, index=True, comment="标签名称")
    category = Column(String(50), nullable=False, index=True, comment="标签分类: work_type/time_cycle/priority/department")
    status = Column(String(20), default="active", nullable=False, index=True, comment="状态: active/inactive")

    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    def __repr__(self):
        return f"<Tag(id={self.id}, name='{self.name}', category='{self.category}')>"
