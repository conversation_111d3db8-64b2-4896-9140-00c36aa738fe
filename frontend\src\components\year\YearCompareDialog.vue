<template>
  <el-dialog
    v-model="dialogVisible"
    title="年度数据对比"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="compare-content">
      <!-- 年度选择 -->
      <div class="year-selection">
        <h4>选择对比年度</h4>
        <el-checkbox-group v-model="selectedYears" :max="3">
          <el-checkbox
            v-for="year in years"
            :key="year.id"
            :value="year.year"
            :disabled="selectedYears.length >= 3 && !selectedYears.includes(year.year)"
          >
            {{ year.year }}年
            <el-tag size="small" :type="getYearStatusType(year.status)" style="margin-left: 8px">
              {{ getYearStatusText(year.status) }}
            </el-tag>
          </el-checkbox>
        </el-checkbox-group>
        <div class="selection-tip">
          最多可选择3个年度进行对比
        </div>
      </div>

      <!-- 对比按钮 -->
      <div class="compare-actions">
        <el-button 
          type="primary" 
          @click="performCompare"
          :loading="loading"
          :disabled="selectedYears.length < 2"
        >
          {{ loading ? '对比中...' : '开始对比' }}
        </el-button>
        <el-button @click="clearComparison">清空结果</el-button>
      </div>

      <!-- 对比结果 -->
      <div v-if="comparisonResult" class="comparison-result">
        <h4>对比结果</h4>
        
        <!-- 基础数据对比表格 -->
        <el-table :data="comparisonTableData" border style="width: 100%; margin-bottom: 20px;">
          <el-table-column prop="metric" label="指标" width="120" />
          <el-table-column
            v-for="year in selectedYears"
            :key="year"
            :prop="`year_${year}`"
            :label="`${year}年`"
            align="center"
          />
        </el-table>

        <!-- 趋势分析 -->
        <div v-if="comparisonResult.trends && Object.keys(comparisonResult.trends).length > 0" class="trends-analysis">
          <h5>趋势分析</h5>
          <div class="trends-list">
            <div v-for="(value, key) in comparisonResult.trends" :key="key" class="trend-item">
              <span class="trend-label">{{ getTrendLabel(key) }}：</span>
              <span :class="['trend-value', getTrendClass(value)]">
                {{ formatTrendValue(key, value) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 对比图表 -->
        <div class="comparison-charts">
          <h5>可视化对比</h5>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon size="48"><TrendCharts /></el-icon>
              <p>图表功能将在后续版本中提供</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="请选择年度并点击开始对比" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="success" 
          @click="exportComparison"
          :disabled="!comparisonResult"
        >
          导出对比结果
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  years: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const selectedYears = ref([])
const comparisonResult = ref(null)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 计算属性
const comparisonTableData = computed(() => {
  if (!comparisonResult.value || !comparisonResult.value.comparison) {
    return []
  }

  const metrics = [
    { key: 'total_ledgers', label: '台账总数' },
    { key: 'completed_ledgers', label: '已完成' },
    { key: 'completion_rate', label: '完成率(%)' }
  ]

  return metrics.map(metric => {
    const row = { metric: metric.label }
    selectedYears.value.forEach(year => {
      const yearData = comparisonResult.value.comparison[year]
      if (yearData) {
        row[`year_${year}`] = yearData[metric.key] || 0
      }
    })
    return row
  })
})

// 方法
const getYearStatusType = (status) => {
  switch (status) {
    case 'active': return 'success'
    case 'archived': return 'info'
    default: return 'info'
  }
}

const getYearStatusText = (status) => {
  switch (status) {
    case 'active': return '当前'
    case 'archived': return '已归档'
    default: return '未知'
  }
}

const performCompare = async () => {
  if (selectedYears.value.length < 2) {
    ElMessage.warning('请至少选择2个年度进行对比')
    return
  }

  try {
    loading.value = true
    
    const yearsParam = selectedYears.value.join(',')
    const response = await fetch(`/api/v1/years/compare?years=${yearsParam}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })

    if (response.ok) {
      comparisonResult.value = await response.json()
      ElMessage.success('对比完成')
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '对比失败')
    }
  } catch (error) {
    console.error('年度对比失败:', error)
    ElMessage.error('年度对比失败')
  } finally {
    loading.value = false
  }
}

const clearComparison = () => {
  comparisonResult.value = null
  selectedYears.value = []
}

const getTrendLabel = (key) => {
  const labels = {
    ledger_growth: '台账增长率',
    completion_change: '完成率变化'
  }
  return labels[key] || key
}

const getTrendClass = (value) => {
  if (typeof value !== 'number') return ''
  
  if (value > 0) return 'trend-positive'
  if (value < 0) return 'trend-negative'
  return 'trend-neutral'
}

const formatTrendValue = (key, value) => {
  if (typeof value !== 'number') return value
  
  const sign = value > 0 ? '+' : ''
  return `${sign}${value.toFixed(1)}%`
}

const exportComparison = () => {
  if (!comparisonResult.value) return
  
  // 生成导出数据
  const exportData = {
    comparison_date: new Date().toISOString(),
    selected_years: selectedYears.value,
    comparison_data: comparisonResult.value.comparison,
    trends: comparisonResult.value.trends || {}
  }
  
  // 创建下载链接
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `年度对比结果_${selectedYears.value.join('_')}_${new Date().toISOString().slice(0, 10)}.json`
  link.click()
  
  URL.revokeObjectURL(url)
  ElMessage.success('对比结果已导出')
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.compare-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.year-selection h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.selection-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.compare-actions {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
}

.comparison-result h4,
.comparison-result h5 {
  margin: 0 0 16px 0;
  color: #303133;
}

.trends-analysis {
  margin-bottom: 24px;
}

.trends-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.trend-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.trend-value {
  font-weight: bold;
}

.trend-positive {
  color: #67c23a;
}

.trend-negative {
  color: #f56c6c;
}

.trend-neutral {
  color: #909399;
}

.comparison-charts {
  margin-top: 24px;
}

.chart-container {
  height: 200px;
  border: 1px dashed #e4e7ed;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.chart-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.empty-state {
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
