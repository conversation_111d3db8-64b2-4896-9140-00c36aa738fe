"""
标签管理API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..core.database import get_db
from ..core.auth import get_current_user
from ..models.user import User
from ..models.tag import Tag
from ..schemas.tag import TagCreate, TagUpdate, TagResponse, TagListResponse
from ..services.tag_service import TagService

router = APIRouter()
tag_service = TagService()

@router.get("/", response_model=TagListResponse)
async def get_tags(
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = None,
    department: Optional[str] = None,
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取标签列表"""
    # 所有用户都可以查看标签
    tags = tag_service.get_tags(
        db=db,
        skip=skip,
        limit=limit,
        category=category,
        department=department,
        is_active=is_active,
        search=search
    )
    
    total = tag_service.count_tags(
        db=db,
        category=category,
        department=department,
        is_active=is_active,
        search=search
    )
    
    return TagListResponse(
        items=[TagResponse.from_orm(tag) for tag in tags],
        total=total,
        skip=skip,
        limit=limit
    )

@router.post("/", response_model=TagResponse)
async def create_tag(
    tag_data: TagCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新标签"""
    # 检查权限：管理员和管理部门可以创建标签
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和管理部门可以创建标签"
        )
    
    # 检查标签名称是否已存在
    existing_tag = tag_service.get_tag_by_name(db, tag_data.name, tag_data.category)
    if existing_tag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该分类下已存在同名标签"
        )
    
    # 创建标签
    tag = tag_service.create_tag(db, tag_data, current_user.id)
    return TagResponse.from_orm(tag)

@router.get("/{tag_id}", response_model=TagResponse)
async def get_tag(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取标签详情"""
    tag = tag_service.get_tag(db, tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    return TagResponse.from_orm(tag)

@router.put("/{tag_id}", response_model=TagResponse)
async def update_tag(
    tag_id: int,
    tag_data: TagUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新标签"""
    # 检查权限：管理员和管理部门可以更新标签
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和管理部门可以更新标签"
        )
    
    tag = tag_service.get_tag(db, tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查是否有权限编辑此标签
    if current_user.role != 'admin' and tag.created_by != current_user.id:
        # 管理部门只能编辑自己创建的标签
        if tag.department and tag.department != current_user.department:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只能编辑本部门创建的标签"
            )
    
    # 检查标签名称是否已存在（排除当前标签）
    if tag_data.name and tag_data.name != tag.name:
        existing_tag = tag_service.get_tag_by_name(db, tag_data.name, tag.category)
        if existing_tag and existing_tag.id != tag_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该分类下已存在同名标签"
            )
    
    # 更新标签
    updated_tag = tag_service.update_tag(db, tag, tag_data)
    return TagResponse.from_orm(updated_tag)

@router.delete("/{tag_id}")
async def delete_tag(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除标签"""
    # 检查权限：管理员和管理部门可以删除标签
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和管理部门可以删除标签"
        )
    
    tag = tag_service.get_tag(db, tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查是否有权限删除此标签
    if current_user.role != 'admin' and tag.created_by != current_user.id:
        # 管理部门只能删除自己创建的标签
        if tag.department and tag.department != current_user.department:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只能删除本部门创建的标签"
            )
    
    # 检查标签是否被使用
    if tag_service.is_tag_in_use(db, tag_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签正在使用中，无法删除"
        )
    
    # 删除标签
    tag_service.delete_tag(db, tag)
    return {"message": "标签删除成功"}

@router.get("/categories/list")
async def get_tag_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取标签分类列表"""
    categories = tag_service.get_tag_categories(db)
    return {"categories": categories}

@router.get("/stats/summary")
async def get_tag_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取标签统计信息"""
    # 检查权限：管理员和管理部门可以查看统计
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    stats = tag_service.get_tag_stats(db)
    return stats

@router.post("/batch")
async def create_tags_batch(
    tags_data: List[TagCreate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量创建标签"""
    # 检查权限：管理员和管理部门可以批量创建标签
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，只有管理员和管理部门可以批量创建标签"
        )
    
    created_tags = []
    errors = []
    
    for tag_data in tags_data:
        try:
            # 检查标签名称是否已存在
            existing_tag = tag_service.get_tag_by_name(db, tag_data.name, tag_data.category)
            if existing_tag:
                errors.append(f"标签 '{tag_data.name}' 在分类 '{tag_data.category}' 下已存在")
                continue
            
            # 创建标签
            tag = tag_service.create_tag(db, tag_data, current_user.id)
            created_tags.append(TagResponse.from_orm(tag))
        except Exception as e:
            errors.append(f"创建标签 '{tag_data.name}' 失败: {str(e)}")
    
    return {
        "created_tags": created_tags,
        "errors": errors,
        "success_count": len(created_tags),
        "error_count": len(errors)
    }

@router.put("/{tag_id}/toggle-status")
async def toggle_tag_status(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """切换标签状态（启用/停用）"""
    # 检查权限：管理员和管理部门可以切换标签状态
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    tag = tag_service.get_tag(db, tag_id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="标签不存在"
        )
    
    # 检查是否有权限操作此标签
    if current_user.role != 'admin' and tag.created_by != current_user.id:
        if tag.department and tag.department != current_user.department:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只能操作本部门创建的标签"
            )
    
    # 切换状态
    updated_tag = tag_service.toggle_tag_status(db, tag)
    return TagResponse.from_orm(updated_tag)
