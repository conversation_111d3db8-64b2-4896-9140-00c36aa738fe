<template>
  <el-drawer
    v-model="visible"
    title="台账详情"
    direction="rtl"
    size="60%"
    :close-on-click-modal="false"
  >
    <div v-if="ledger" class="ledger-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>序号：</label>
              <span class="value">{{ ledger.sequence_number }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>责任单位：</label>
              <span class="value">{{ ledger.responsible_dept }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>年度：</label>
              <span class="value">{{ ledger.year }}年</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="getStatusType(ledger.status)" class="value">
                {{ getStatusText(ledger.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>创建时间：</label>
              <span class="value">{{ formatDate(ledger.created_at) }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 工作内容 -->
      <div class="detail-section">
        <h3 class="section-title">工作内容</h3>

        <div class="detail-item">
          <label>工作事项：</label>
          <div class="value content-text">{{ ledger.work_item }}</div>
        </div>

        <div class="detail-item">
          <label>具体措施：</label>
          <div class="value content-text">{{ ledger.measures }}</div>
        </div>

        <div class="detail-item">
          <label>进展情况：</label>
          <div class="value content-text">{{ ledger.progress }}</div>
        </div>

        <div class="detail-item" v-if="ledger.effectiveness">
          <label>成效：</label>
          <div class="value content-text">{{ ledger.effectiveness }}</div>
        </div>
      </div>

      <!-- 关联信息 -->
      <div class="detail-section">
        <h3 class="section-title">关联信息</h3>

        <div class="detail-item">
          <label>对口部门：</label>
          <div class="value">
            <el-tag
              v-for="dept in ledger.counterpart_depts"
              :key="dept"
              class="tag-item"
              size="small"
            >
              {{ dept }}
            </el-tag>
          </div>
        </div>

        <div class="detail-item">
          <label>关键词标签：</label>
          <div class="value">
            <el-tag
              v-for="tag in ledger.tags"
              :key="tag"
              class="tag-item"
              size="small"
              type="info"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section" v-if="ledger.remarks">
        <h3 class="section-title">备注信息</h3>
        <div class="detail-item">
          <div class="value content-text">{{ ledger.remarks }}</div>
        </div>
      </div>

      <!-- 操作历史 -->
      <div class="detail-section">
        <h3 class="section-title">操作历史</h3>
        <div class="history-item">
          <div class="history-time">{{ formatDate(ledger.created_at) }}</div>
          <div class="history-action">创建台账</div>
        </div>
        <div class="history-item" v-if="ledger.updated_at !== ledger.created_at">
          <div class="history-time">{{ formatDate(ledger.updated_at) }}</div>
          <div class="history-action">更新台账</div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Ledger } from '../api/ledger'

// Props
interface Props {
  modelValue: boolean
  ledger?: Ledger | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  ledger: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'edit': [ledger: Ledger]
  'delete': [ledger: Ledger]
}>()

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Methods
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': 'info',
    'in_progress': 'warning',
    'completed': 'success',
    'completed_ongoing': 'primary'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'in_progress': '进行中',
    'completed': '已完成',
    'completed_ongoing': '已完成并持续推进'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleEdit = () => {
  if (props.ledger) {
    emit('edit', props.ledger)
  }
}

const handleDelete = async () => {
  if (!props.ledger) return

  try {
    await ElMessageBox.confirm(
      `确定要删除台账"${props.ledger.work_item}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    emit('delete', props.ledger)
  } catch (error) {
    // 用户取消删除
  }
}
</script>

<style scoped>
.ledger-detail {
  padding: 0 20px;
}

.detail-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e3f2fd;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item label {
  display: inline-block;
  width: 100px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 4px;
}

.detail-item .value {
  color: #303133;
  line-height: 1.6;
}

.content-text {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1976d2;
  white-space: pre-wrap;
  word-break: break-word;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 4px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  width: 150px;
  font-size: 12px;
  color: #909399;
}

.history-action {
  color: #606266;
  font-size: 14px;
}

.drawer-footer {
  text-align: right;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-drawer__header) {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0;
}
</style>
