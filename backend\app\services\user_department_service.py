"""
用户部门归属管理服务
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from ..models.user import User
from ..models.department import Department
from ..models.user_department_history import UserDepartmentHistory
from ..schemas.department_management import (
    UserTransferRequest, BatchUserTransferRequest, UserTransferResponse,
    BatchOperationResult
)


class UserDepartmentService:
    """用户部门归属管理服务"""

    def __init__(self, db: Session):
        self.db = db

    def transfer_user(self, request: UserTransferRequest, operator_id: int) -> UserTransferResponse:
        """用户调动"""
        try:
            # 验证用户存在
            user = self.db.query(User).filter(User.id == request.user_id).first()
            if not user:
                raise ValueError(f"用户 ID {request.user_id} 不存在")
            
            # 验证新部门存在
            new_department = self.db.query(Department).filter(Department.id == request.new_department_id).first()
            if not new_department:
                raise ValueError(f"部门 ID {request.new_department_id} 不存在")
            
            # 记录原部门
            old_department_id = user.department_id
            old_department = None
            if old_department_id:
                old_department = self.db.query(Department).filter(Department.id == old_department_id).first()
            
            # 创建调动历史记录
            transfer_history = UserDepartmentHistory(
                user_id=request.user_id,
                old_department_id=old_department_id,
                new_department_id=request.new_department_id,
                change_reason=request.change_reason,
                effective_date=request.effective_date or datetime.utcnow(),
                created_by=operator_id
            )
            
            # 更新用户部门
            user.department_id = request.new_department_id
            
            # 保存记录
            self.db.add(transfer_history)
            self.db.commit()
            
            # 构造响应
            return UserTransferResponse(
                id=transfer_history.id,
                user_id=user.id,
                user_name=user.full_name,
                old_department_id=old_department_id,
                old_department_name=old_department.name if old_department else None,
                new_department_id=request.new_department_id,
                new_department_name=new_department.name,
                change_reason=request.change_reason,
                transfer_summary=transfer_history.transfer_summary,
                effective_date=transfer_history.effective_date,
                created_at=transfer_history.created_at
            )
            
        except Exception as e:
            self.db.rollback()
            raise ValueError(f"用户调动失败: {str(e)}")

    def batch_transfer_users(self, request: BatchUserTransferRequest, operator_id: int) -> BatchOperationResult:
        """批量用户调动"""
        results = []
        errors = []
        success_count = 0
        
        try:
            for transfer_request in request.transfers:
                try:
                    # 添加批量原因到个人原因中
                    if request.batch_reason:
                        combined_reason = f"{request.batch_reason}"
                        if transfer_request.change_reason:
                            combined_reason += f" - {transfer_request.change_reason}"
                        transfer_request.change_reason = combined_reason
                    
                    # 执行单个调动
                    result = self.transfer_user(transfer_request, operator_id)
                    results.append({
                        "success": True,
                        "user_id": transfer_request.user_id,
                        "data": result.dict()
                    })
                    success_count += 1
                    
                except Exception as e:
                    error_msg = f"用户 {transfer_request.user_id} 调动失败: {str(e)}"
                    results.append({
                        "success": False,
                        "user_id": transfer_request.user_id,
                        "error": error_msg
                    })
                    errors.append(error_msg)
            
            return BatchOperationResult(
                total=len(request.transfers),
                success=success_count,
                failed=len(request.transfers) - success_count,
                results=results,
                errors=errors
            )
            
        except Exception as e:
            self.db.rollback()
            raise ValueError(f"批量用户调动失败: {str(e)}")

    def get_user_department_history(
        self,
        user_id: Optional[int] = None,
        department_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 50
    ) -> Dict[str, Any]:
        """获取用户部门调动历史"""
        query = self.db.query(UserDepartmentHistory)
        
        # 筛选条件
        if user_id:
            query = query.filter(UserDepartmentHistory.user_id == user_id)
        
        if department_id:
            query = query.filter(
                or_(
                    UserDepartmentHistory.old_department_id == department_id,
                    UserDepartmentHistory.new_department_id == department_id
                )
            )
        
        # 排序
        query = query.order_by(desc(UserDepartmentHistory.created_at))
        
        # 总数
        total = query.count()
        
        # 分页
        histories = query.offset(skip).limit(limit).all()
        
        # 构造响应数据
        response_data = []
        for history in histories:
            user = self.db.query(User).filter(User.id == history.user_id).first()
            old_dept = None
            new_dept = None
            
            if history.old_department_id:
                old_dept = self.db.query(Department).filter(Department.id == history.old_department_id).first()
            
            if history.new_department_id:
                new_dept = self.db.query(Department).filter(Department.id == history.new_department_id).first()
            
            response_data.append(UserTransferResponse(
                id=history.id,
                user_id=history.user_id,
                user_name=user.full_name if user else "未知用户",
                old_department_id=history.old_department_id,
                old_department_name=old_dept.name if old_dept else None,
                new_department_id=history.new_department_id,
                new_department_name=new_dept.name if new_dept else None,
                change_reason=history.change_reason,
                transfer_summary=history.transfer_summary,
                effective_date=history.effective_date,
                created_at=history.created_at
            ))
        
        return {
            "data": response_data,
            "total": total,
            "page": (skip // limit) + 1 if limit > 0 else 1,
            "limit": limit
        }

    def get_department_users(self, department_id: int) -> List[Dict[str, Any]]:
        """获取部门用户列表"""
        department = self.db.query(Department).filter(Department.id == department_id).first()
        if not department:
            raise ValueError(f"部门 ID {department_id} 不存在")
        
        users = self.db.query(User).filter(User.department_id == department_id).all()
        
        result = []
        for user in users:
            # 获取用户最近的调动记录
            latest_transfer = self.db.query(UserDepartmentHistory).filter(
                UserDepartmentHistory.user_id == user.id
            ).order_by(desc(UserDepartmentHistory.created_at)).first()
            
            result.append({
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "is_active": user.is_active,
                "created_at": user.created_at,
                "last_transfer_date": latest_transfer.created_at if latest_transfer else None,
                "last_transfer_reason": latest_transfer.change_reason if latest_transfer else None
            })
        
        return result

    def get_user_transfer_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取用户调动统计"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"用户 ID {user_id} 不存在")
        
        # 调动次数
        transfer_count = self.db.query(UserDepartmentHistory).filter(
            UserDepartmentHistory.user_id == user_id
        ).count()
        
        # 最近调动
        latest_transfer = self.db.query(UserDepartmentHistory).filter(
            UserDepartmentHistory.user_id == user_id
        ).order_by(desc(UserDepartmentHistory.created_at)).first()
        
        # 在当前部门的时间
        current_dept_duration = None
        if latest_transfer:
            current_dept_duration = (datetime.utcnow() - latest_transfer.effective_date).days
        elif user.created_at:
            current_dept_duration = (datetime.utcnow() - user.created_at).days
        
        # 历史部门列表
        history_query = self.db.query(UserDepartmentHistory).filter(
            UserDepartmentHistory.user_id == user_id
        ).order_by(UserDepartmentHistory.effective_date).all()
        
        department_history = []
        for history in history_query:
            old_dept = None
            new_dept = None
            
            if history.old_department_id:
                old_dept = self.db.query(Department).filter(Department.id == history.old_department_id).first()
            
            if history.new_department_id:
                new_dept = self.db.query(Department).filter(Department.id == history.new_department_id).first()
            
            department_history.append({
                "effective_date": history.effective_date,
                "old_department": old_dept.name if old_dept else None,
                "new_department": new_dept.name if new_dept else None,
                "reason": history.change_reason
            })
        
        return {
            "user_id": user_id,
            "user_name": user.full_name,
            "current_department": user.department.name if user.department else None,
            "transfer_count": transfer_count,
            "current_department_duration_days": current_dept_duration,
            "latest_transfer_date": latest_transfer.effective_date if latest_transfer else None,
            "department_history": department_history
        }
