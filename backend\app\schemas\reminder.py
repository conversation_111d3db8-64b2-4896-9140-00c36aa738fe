"""
提醒系统数据模式
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator


# 提醒规则相关模式
class ReminderRuleBase(BaseModel):
    """提醒规则基础模式"""
    name: str = Field(..., description="规则名称")
    description: Optional[str] = Field(None, description="规则描述")
    work_type: Optional[str] = Field(None, description="工作类型")
    department: Optional[str] = Field(None, description="适用部门")
    tags: List[str] = Field(default_factory=list, description="适用标签")
    advance_days: int = Field(7, ge=1, le=365, description="提前天数")
    reminder_frequency: str = Field("once", description="提醒频率")
    enable_escalation: bool = Field(False, description="是否启用升级提醒")
    escalation_days: int = Field(3, ge=1, le=30, description="升级提醒间隔天数")
    escalation_levels: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="升级提醒级别配置")
    is_active: bool = Field(True, description="是否启用")


class ReminderRuleCreate(ReminderRuleBase):
    """创建提醒规则"""
    pass


class ReminderRuleUpdate(BaseModel):
    """更新提醒规则"""
    name: Optional[str] = None
    description: Optional[str] = None
    work_type: Optional[str] = None
    department: Optional[str] = None
    tags: Optional[List[str]] = None
    advance_days: Optional[int] = Field(None, ge=1, le=365)
    reminder_frequency: Optional[str] = None
    enable_escalation: Optional[bool] = None
    escalation_days: Optional[int] = Field(None, ge=1, le=30)
    escalation_levels: Optional[List[Dict[str, Any]]] = None
    is_active: Optional[bool] = None


class ReminderRuleResponse(ReminderRuleBase):
    """提醒规则响应"""
    id: int
    created_by: int
    created_at: datetime
    updated_at: datetime

    @field_validator('escalation_levels', mode='before')
    @classmethod
    def validate_escalation_levels(cls, v):
        """处理escalation_levels的None值"""
        if v is None:
            return []
        return v

    class Config:
        from_attributes = True


# 提醒记录相关模式
class ReminderBase(BaseModel):
    """提醒记录基础模式"""
    title: str = Field(..., description="提醒标题")
    content: str = Field(..., description="提醒内容")
    reminder_type: str = Field("deadline", description="提醒类型")
    priority: str = Field("normal", description="优先级")
    scheduled_time: datetime = Field(..., description="计划提醒时间")


class ReminderCreate(ReminderBase):
    """创建提醒记录"""
    rule_id: int = Field(..., description="提醒规则ID")
    ledger_id: int = Field(..., description="台账ID")
    user_id: int = Field(..., description="提醒对象用户ID")
    extra_data: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class ReminderUpdate(BaseModel):
    """更新提醒记录"""
    title: Optional[str] = None
    content: Optional[str] = None
    priority: Optional[str] = None
    scheduled_time: Optional[datetime] = None
    status: Optional[str] = None
    is_read: Optional[bool] = None
    is_dismissed: Optional[bool] = None


class ReminderResponse(ReminderBase):
    """提醒记录响应"""
    id: int
    rule_id: int
    ledger_id: int
    user_id: int
    sent_time: Optional[datetime]
    read_time: Optional[datetime]
    status: str
    is_read: bool
    is_dismissed: bool
    extra_data: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    # 关联数据
    ledger_title: Optional[str] = None
    user_name: Optional[str] = None
    rule_name: Optional[str] = None
    
    class Config:
        from_attributes = True


# 通知中心相关模式
class NotificationBase(BaseModel):
    """通知基础模式"""
    title: str = Field(..., description="通知标题")
    content: str = Field(..., description="通知内容")
    notification_type: str = Field("reminder", description="通知类型")
    icon: Optional[str] = Field(None, description="图标")
    color: str = Field("primary", description="颜色主题")
    action_url: Optional[str] = Field(None, description="操作链接")
    action_text: Optional[str] = Field(None, description="操作按钮文本")


class NotificationCreate(NotificationBase):
    """创建通知"""
    user_id: int = Field(..., description="用户ID")
    reminder_id: Optional[int] = Field(None, description="关联提醒ID")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class NotificationUpdate(BaseModel):
    """更新通知"""
    is_read: Optional[bool] = None
    is_pinned: Optional[bool] = None
    is_deleted: Optional[bool] = None


class NotificationResponse(NotificationBase):
    """通知响应"""
    id: int
    user_id: int
    reminder_id: Optional[int]
    is_read: bool
    is_pinned: bool
    is_deleted: bool
    created_at: datetime
    read_at: Optional[datetime]
    expires_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# 列表响应模式
class ReminderRuleListResponse(BaseModel):
    """提醒规则列表响应"""
    items: List[ReminderRuleResponse]
    total: int
    page: int
    limit: int
    has_next: bool


class ReminderListResponse(BaseModel):
    """提醒记录列表响应"""
    items: List[ReminderResponse]
    total: int
    page: int
    limit: int
    has_next: bool


class NotificationListResponse(BaseModel):
    """通知列表响应"""
    items: List[NotificationResponse]
    total: int
    page: int
    limit: int
    has_next: bool
    unread_count: int


# 统计模式
class ReminderStats(BaseModel):
    """提醒统计"""
    total_rules: int = Field(0, description="总规则数")
    active_rules: int = Field(0, description="活跃规则数")
    total_reminders: int = Field(0, description="总提醒数")
    pending_reminders: int = Field(0, description="待发送提醒数")
    sent_reminders: int = Field(0, description="已发送提醒数")
    read_reminders: int = Field(0, description="已读提醒数")
    overdue_reminders: int = Field(0, description="过期提醒数")


class NotificationStats(BaseModel):
    """通知统计"""
    total_notifications: int = Field(0, description="总通知数")
    unread_notifications: int = Field(0, description="未读通知数")
    pinned_notifications: int = Field(0, description="置顶通知数")
    today_notifications: int = Field(0, description="今日通知数")


# 批量操作模式
class BatchReminderOperation(BaseModel):
    """批量提醒操作"""
    reminder_ids: List[int] = Field(..., description="提醒ID列表")
    action: str = Field(..., description="操作类型: mark_read, dismiss, delete")


class BatchNotificationOperation(BaseModel):
    """批量通知操作"""
    notification_ids: List[int] = Field(..., description="通知ID列表")
    action: str = Field(..., description="操作类型: mark_read, pin, unpin, delete")


# 提醒设置模式
class ReminderSettings(BaseModel):
    """用户提醒设置"""
    enable_email_notifications: bool = Field(True, description="启用邮件通知")
    enable_system_notifications: bool = Field(True, description="启用系统通知")
    notification_frequency: str = Field("immediate", description="通知频率")
    quiet_hours_start: Optional[str] = Field(None, description="免打扰开始时间")
    quiet_hours_end: Optional[str] = Field(None, description="免打扰结束时间")
    weekend_notifications: bool = Field(False, description="周末通知")


class ReminderSettingsUpdate(BaseModel):
    """更新提醒设置"""
    enable_email_notifications: Optional[bool] = None
    enable_system_notifications: Optional[bool] = None
    notification_frequency: Optional[str] = None
    quiet_hours_start: Optional[str] = None
    quiet_hours_end: Optional[str] = None
    weekend_notifications: Optional[bool] = None
