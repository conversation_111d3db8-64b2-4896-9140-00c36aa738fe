"""
用户管理API路由
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from ...api.deps import get_db, get_current_user, require_admin
from ...core.auth import get_password_hash, verify_password
from ...core.security import (
    validate_password_strength, validate_username, validate_email,
    validate_phone, get_client_ip, log_security_event, SecurityEventType
)
from ...models.user import User
from ...models.department import Department
from ...schemas.auth import (
    UserCreate, UserUpdate, UserResponse, UserListResponse,
    MessageResponse, PasswordChangeRequest, UserStats
)

router = APIRouter()


@router.get("/", summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    department: Optional[str] = Query(None, description="部门筛选"),
    role: Optional[str] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取用户列表（分页、搜索、筛选）
    """
    # 构建查询
    query = db.query(User)

    # 搜索条件
    if search:
        search_filter = or_(
            User.username.contains(search),
            User.full_name.contains(search),
            User.email.contains(search)
        )
        query = query.filter(search_filter)

    # 部门筛选
    if department:
        query = query.join(Department).filter(Department.name == department)

    # 角色筛选
    if role:
        query = query.filter(User.role == role)

    # 状态筛选 - 默认只显示活跃用户
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    else:
        # 默认只显示活跃用户
        query = query.filter(User.is_active == True)

    # 总数统计
    total = query.count()

    # 分页
    offset = (page - 1) * limit
    users = query.offset(offset).limit(limit).all()

    # 构建响应数据 - 简化格式避免序列化问题
    user_responses = []
    for user in users:
        user_responses.append({
            "id": user.id,
            "username": user.username,
            "full_name": user.full_name,
            "email": user.email or "",
            "phone": user.phone or "",
            "department_id": user.department_id,
            "department": None,  # 暂时移除关联查询
            "role": user.role,
            "is_active": user.is_active,
            "login_count": user.login_count or 0
        })

    pagination = {
        "page": page,
        "limit": limit,
        "total": total,
        "pages": (total + limit - 1) // limit
    }

    return {"users": user_responses, "pagination": pagination}


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    request: Request,
    user_data: UserCreate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    创建新用户
    """
    # 验证用户名格式
    is_valid, errors = validate_username(user_data.username)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"用户名格式错误: {', '.join(errors)}"
        )

    # 验证密码强度
    is_valid, errors = validate_password_strength(user_data.password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"密码强度不足: {', '.join(errors)}"
        )

    # 验证邮箱格式
    if user_data.email and not validate_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱格式错误"
        )

    # 验证手机号格式
    if user_data.phone and not validate_phone(user_data.phone):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号格式错误"
        )

    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == user_data.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    if user_data.email:
        existing_email = db.query(User).filter(User.email == user_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被使用"
            )

    # 验证部门是否存在
    if user_data.department_id:
        department = db.query(Department).filter(Department.id == user_data.department_id).first()
        if not department:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的部门不存在"
            )

    # 创建用户
    user = User(
        username=user_data.username,
        password_hash=get_password_hash(user_data.password),
        full_name=user_data.full_name,
        email=user_data.email,
        phone=user_data.phone,
        department_id=user_data.department_id,
        role=user_data.role,
        is_active=user_data.is_active
    )

    db.add(user)
    db.commit()
    db.refresh(user)

    # 记录安全事件
    log_security_event(
        "user_created",
        current_user.id,
        {
            "created_user_id": user.id,
            "created_username": user.username,
            "created_role": user.role
        },
        get_client_ip(request)
    )

    return UserResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        phone=user.phone,
        department_id=user.department_id,
        department=user.department.name if user.department else None,
        role=user.role,
        is_active=user.is_active,
        last_login=user.last_login,
        login_count=user.login_count,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取指定用户的详细信息
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    return UserResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        phone=user.phone,
        department_id=user.department_id,
        department=user.department.name if user.department else None,
        role=user.role,
        is_active=user.is_active,
        last_login=user.last_login,
        login_count=user.login_count,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户信息")
async def update_user(
    user_id: int,
    request: Request,
    user_data: UserUpdate,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    更新用户信息
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 验证邮箱格式
    if user_data.email and not validate_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱格式错误"
        )

    # 验证手机号格式
    if user_data.phone and not validate_phone(user_data.phone):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号格式错误"
        )

    # 检查邮箱是否已被其他用户使用
    if user_data.email:
        existing_email = db.query(User).filter(
            and_(User.email == user_data.email, User.id != user_id)
        ).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被其他用户使用"
            )

    # 验证部门是否存在
    if user_data.department_id:
        department = db.query(Department).filter(Department.id == user_data.department_id).first()
        if not department:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的部门不存在"
            )

    # 更新用户信息
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)

    db.commit()
    db.refresh(user)

    # 记录安全事件
    log_security_event(
        "user_updated",
        current_user.id,
        {
            "updated_user_id": user.id,
            "updated_username": user.username,
            "updated_fields": list(update_data.keys())
        },
        get_client_ip(request)
    )

    return UserResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        phone=user.phone,
        department_id=user.department_id,
        department=user.department.name if user.department else None,
        role=user.role,
        is_active=user.is_active,
        last_login=user.last_login,
        login_count=user.login_count,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


@router.delete("/{user_id}", response_model=MessageResponse, summary="删除用户")
async def delete_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    删除用户（软删除）
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账号"
        )

    # 软删除：设置为非活跃状态
    user.is_active = False
    db.commit()

    # 记录安全事件
    log_security_event(
        "user_deleted",
        current_user.id,
        {
            "deleted_user_id": user.id,
            "deleted_username": user.username
        },
        get_client_ip(request)
    )

    return MessageResponse(message=f"用户 {user.username} 已被删除")


@router.put("/{user_id}/role", response_model=UserResponse, summary="分配用户角色")
async def assign_user_role(
    user_id: int,
    request: Request,
    role: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    分配用户角色
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 验证角色
    valid_roles = ["admin", "manager", "user"]
    if role not in valid_roles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的角色，有效角色: {', '.join(valid_roles)}"
        )

    old_role = user.role
    user.role = role
    db.commit()

    # 记录安全事件
    log_security_event(
        "role_changed",
        current_user.id,
        {
            "target_user_id": user.id,
            "target_username": user.username,
            "old_role": old_role,
            "new_role": role
        },
        get_client_ip(request)
    )

    return UserResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        phone=user.phone,
        department_id=user.department_id,
        department=user.department.name if user.department else None,
        role=user.role,
        is_active=user.is_active,
        last_login=user.last_login,
        login_count=user.login_count,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


@router.put("/{user_id}/status", response_model=UserResponse, summary="更新用户状态")
async def update_user_status(
    user_id: int,
    request: Request,
    is_active: bool,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    更新用户状态（启用/禁用）
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能禁用自己
    if user.id == current_user.id and not is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能禁用自己的账号"
        )

    old_status = user.is_active
    user.is_active = is_active
    db.commit()

    # 记录安全事件
    log_security_event(
        "user_status_changed",
        current_user.id,
        {
            "target_user_id": user.id,
            "target_username": user.username,
            "old_status": old_status,
            "new_status": is_active
        },
        get_client_ip(request)
    )

    return UserResponse(
        id=user.id,
        username=user.username,
        full_name=user.full_name,
        email=user.email,
        phone=user.phone,
        department_id=user.department_id,
        department=user.department.name if user.department else None,
        role=user.role,
        is_active=user.is_active,
        last_login=user.last_login,
        login_count=user.login_count,
        created_at=user.created_at,
        updated_at=user.updated_at
    )


@router.post("/{user_id}/reset-password", summary="重置用户密码")
async def reset_user_password(
    user_id: int,
    request: Request,
    reset_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    管理员重置用户密码
    """
    new_password = reset_data.get('new_password')
    if not new_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="新密码不能为空"
        )

    # 查找目标用户
    target_user = db.query(User).filter(User.id == user_id).first()
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 验证新密码强度
    is_valid, errors = validate_password_strength(new_password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"密码强度不足: {', '.join(errors)}"
        )

    # 更新密码
    target_user.password_hash = get_password_hash(new_password)
    db.commit()

    # 记录安全事件
    log_security_event(
        "password_reset_by_admin",
        current_user.id,
        {
            "target_user_id": target_user.id,
            "target_username": target_user.username
        },
        get_client_ip(request)
    )

    return {
        "message": f"用户 {target_user.username} 的密码已重置",
        "user_id": target_user.id,
        "username": target_user.username
    }


@router.post("/change-password", response_model=MessageResponse, summary="修改密码")
async def change_password(
    request: Request,
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    用户修改自己的密码
    """
    # 验证当前密码
    if not verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )

    # 验证新密码强度
    is_valid, errors = validate_password_strength(password_data.new_password)
    if not is_valid:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"新密码强度不足: {', '.join(errors)}"
        )

    # 更新密码
    current_user.password_hash = get_password_hash(password_data.new_password)
    db.commit()

    # 记录安全事件
    log_security_event(
        SecurityEventType.PASSWORD_CHANGE,
        current_user.id,
        {"username": current_user.username},
        get_client_ip(request)
    )

    return MessageResponse(message="密码修改成功")


@router.get("/stats/overview", response_model=UserStats, summary="获取用户统计")
async def get_user_stats(
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取用户统计信息
    """
    from datetime import datetime, timedelta

    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    admin_count = db.query(User).filter(User.role == "admin").count()
    manager_count = db.query(User).filter(User.role == "manager").count()
    user_count = db.query(User).filter(User.role == "user").count()

    # 最近7天登录的用户数
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_logins = db.query(User).filter(User.last_login >= week_ago).count()

    return UserStats(
        total_users=total_users,
        active_users=active_users,
        admin_count=admin_count,
        manager_count=manager_count,
        user_count=user_count,
        recent_logins=recent_logins
    )