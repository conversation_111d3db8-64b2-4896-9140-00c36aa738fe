<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限管理 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .management-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e0e0e0;
        }

        .management-tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            background: none;
            border: none;
            font-size: 16px;
        }

        .management-tab:hover {
            background-color: #f8f9fa;
            color: #1976d2;
        }

        .management-tab.active {
            color: #1976d2;
            border-bottom-color: #1976d2;
            background-color: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .role-admin {
            background: #e3f2fd;
            color: #1976d2;
        }

        .role-manager {
            background: #fff3e0;
            color: #f57c00;
        }

        .role-user {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .tag-management {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .tag-category-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }

        .tag-category-title {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tag-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .tag-item-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
        }

        .tag-name {
            font-weight: 500;
        }

        .tag-actions {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 3px;
        }

        .add-tag-form {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 8px;
            width: 500px;
            max-width: 90%;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
        }

        .close {
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #666;
        }

        .close:hover {
            color: #333;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .permission-matrix {
            margin-top: 20px;
        }

        .permission-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .permission-table th,
        .permission-table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .permission-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .permission-check {
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>

    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab active">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-container">
        <h2 class="page-title">权限管理页面</h2>

        <!-- 管理标签页 -->
        <div class="management-tabs">
            <button class="management-tab active" onclick="showTab('users')">用户管理</button>
            <button class="management-tab" onclick="showTab('tags')">标签管理</button>
            <button class="management-tab" onclick="showTab('permissions')">权限矩阵</button>
        </div>

        <!-- 用户管理标签页 -->
        <div id="users-tab" class="tab-content active">
            <div class="action-bar">
                <div class="search-box">
                    <input type="text" placeholder="搜索用户姓名或部门..." class="search-input">
                    <button class="btn btn-secondary">搜索</button>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="openModal('user-modal')">添加用户</button>
                    <button class="btn btn-secondary">批量导入</button>
                    <button class="btn btn-secondary">导出用户列表</button>
                </div>
            </div>

            <table class="data-table" id="users-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" class="permission-check" onchange="toggleAllCheckboxes(this, 'user-checkbox')">
                        </th>
                        <th>姓名</th>
                        <th>部门</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox" class="user-checkbox"></td>
                        <td>张三</td>
                        <td>综合办</td>
                        <td><span class="role-badge role-admin">主管</span></td>
                        <td><span class="status-active">启用</span></td>
                        <td>2025-06-26 09:30</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="editUser(1)">编辑</button>
                            <button class="btn btn-small btn-warning">重置密码</button>
                            <button class="btn btn-small btn-danger">停用</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="user-checkbox"></td>
                        <td>李四</td>
                        <td>综合办</td>
                        <td><span class="role-badge role-manager">管理部门</span></td>
                        <td><span class="status-active">启用</span></td>
                        <td>2025-06-26 08:45</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="editUser(2)">编辑</button>
                            <button class="btn btn-small btn-warning">重置密码</button>
                            <button class="btn btn-small btn-danger">停用</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="user-checkbox"></td>
                        <td>王五</td>
                        <td>人力</td>
                        <td><span class="role-badge role-user">基层用户</span></td>
                        <td><span class="status-active">启用</span></td>
                        <td>2025-06-25 16:20</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="editUser(3)">编辑</button>
                            <button class="btn btn-small btn-warning">重置密码</button>
                            <button class="btn btn-small btn-danger">停用</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="user-checkbox"></td>
                        <td>赵六</td>
                        <td>财务</td>
                        <td><span class="role-badge role-user">基层用户</span></td>
                        <td><span class="status-inactive">停用</span></td>
                        <td>2025-06-20 14:30</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="editUser(4)">编辑</button>
                            <button class="btn btn-small btn-warning">重置密码</button>
                            <button class="btn btn-small btn-success">启用</button>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="user-checkbox"></td>
                        <td>孙七</td>
                        <td>党建部</td>
                        <td><span class="role-badge role-manager">管理部门</span></td>
                        <td><span class="status-active">启用</span></td>
                        <td>2025-06-26 10:15</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="editUser(5)">编辑</button>
                            <button class="btn btn-small btn-warning">重置密码</button>
                            <button class="btn btn-small btn-danger">停用</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="pagination">
                <button class="page-btn">◀ 上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">下一页 ▶</button>
            </div>
        </div>

        <!-- 标签管理标签页 -->
        <div id="tags-tab" class="tab-content">
            <div class="action-bar">
                <h3 style="margin: 0; color: #1976d2;">标签管理 (管理部门权限)</h3>
                <div class="alert alert-info" style="margin: 0; padding: 10px;">
                    <strong>权限说明：</strong>管理部门可以增删修订关键词标签，下属单位只能在已有标签中勾选
                </div>
            </div>

            <div class="tag-management">
                <!-- 工作类型标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        工作类型标签
                        <button class="btn btn-small btn-primary" onclick="addTag('work-type')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">重点工作</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">大监督</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">专项工作</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>

                <!-- 时间周期标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        时间周期标签
                        <button class="btn btn-small btn-primary" onclick="addTag('time-cycle')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">季度报送</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">半年报送</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限矩阵标签页 -->
        <div id="permissions-tab" class="tab-content">
            <div class="action-bar">
                <h3 style="margin: 0; color: #1976d2;">权限矩阵</h3>
            </div>

            <div class="permission-matrix">
                <table class="permission-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">功能模块</th>
                            <th style="width: 120px;">主管</th>
                            <th style="width: 120px;">管理部门</th>
                            <th style="width: 120px;">基层用户</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>序号编辑</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可编辑台账序号</td>
                        </tr>
                        <tr>
                            <td><strong>台账录入</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td>所有用户都可录入本部门台账</td>
                        </tr>
                        <tr>
                            <td><strong>批注功能</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>管理部门可批注，不能修改其他内容</td>
                        </tr>
                        <tr>
                            <td><strong>标签管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>管理部门可增删改标签，基层只能勾选</td>
                        </tr>
                        <tr>
                            <td><strong>部门管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可修改部门名称和架构</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div id="user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加/编辑用户</h3>
                <span class="close" onclick="closeModal('user-modal')">&times;</span>
            </div>
            <form id="user-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">姓名<span class="required">*</span>：</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门<span class="required">*</span>：</label>
                        <select class="form-control" required>
                            <option value="">请选择部门</option>
                            <option value="综合办">综合办</option>
                            <option value="人力">人力</option>
                            <option value="财务">财务</option>
                            <option value="党建部">党建部</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色<span class="required">*</span>：</label>
                        <select class="form-control" required>
                            <option value="">请选择角色</option>
                            <option value="主管">主管</option>
                            <option value="管理部门">管理部门</option>
                            <option value="基层用户">基层用户</option>
                        </select>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('user-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');

            document.querySelectorAll('.management-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function editUser(userId) {
            openModal('user-modal');
            showMessage('加载用户信息...', 'info');
        }

        function addTag(category) {
            const tagName = prompt('请输入新标签名称：');
            if (tagName && tagName.trim()) {
                showMessage(`标签"${tagName}"添加成功！`, 'success');
            }
        }

        document.getElementById('user-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showMessage('用户信息保存成功！', 'success');
            closeModal('user-modal');
        });
    </script>
</body>
</html>

        <!-- 标签管理标签页 -->
        <div id="tags-tab" class="tab-content">
            <div class="action-bar">
                <h3 style="margin: 0; color: #1976d2;">标签管理 (管理部门权限)</h3>
                <div class="alert alert-info" style="margin: 0; padding: 10px;">
                    <strong>权限说明：</strong>管理部门可以增删修订关键词标签，下属单位只能在已有标签中勾选
                </div>
            </div>

            <div class="tag-management">
                <!-- 工作类型标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        工作类型标签
                        <button class="btn btn-small btn-primary" onclick="addTag('work-type')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">重点工作</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">大监督</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">专项工作</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">安全生产</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>

                <!-- 时间周期标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        时间周期标签
                        <button class="btn btn-small btn-primary" onclick="addTag('time-cycle')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">季度报送</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">半年报送</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">年度报送</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>

                <!-- 重要程度标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        重要程度标签
                        <button class="btn btn-small btn-primary" onclick="addTag('priority')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">高优先级</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">中优先级</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">低优先级</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>

                <!-- 部门归属标签 -->
                <div class="tag-category-card">
                    <div class="tag-category-title">
                        部门归属标签
                        <button class="btn btn-small btn-primary" onclick="addTag('department')">+ 添加</button>
                    </div>
                    <div class="tag-list">
                        <div class="tag-item-row">
                            <span class="tag-name">综合办相关</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">人力相关</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                        <div class="tag-item-row">
                            <span class="tag-name">财务相关</span>
                            <div class="tag-actions">
                                <button class="btn btn-small btn-secondary">编辑</button>
                                <button class="btn btn-small btn-danger">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-tag-form">
                        <input type="text" placeholder="输入新标签名称" class="form-control" style="flex: 1;">
                        <button class="btn btn-primary">添加标签</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限矩阵标签页 -->
        <div id="permissions-tab" class="tab-content">
            <div class="action-bar">
                <h3 style="margin: 0; color: #1976d2;">权限矩阵</h3>
                <div class="alert alert-info" style="margin: 0; padding: 10px;">
                    <strong>说明：</strong>以下是三级权限体系的详细权限分配表
                </div>
            </div>

            <div class="permission-matrix">
                <table class="permission-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">功能模块</th>
                            <th style="width: 120px;">主管</th>
                            <th style="width: 120px;">管理部门</th>
                            <th style="width: 120px;">基层用户</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>序号编辑</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可编辑台账序号</td>
                        </tr>
                        <tr>
                            <td><strong>台账录入</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td>所有用户都可录入本部门台账</td>
                        </tr>
                        <tr>
                            <td><strong>台账查看</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td>查看权限根据标签和部门限制</td>
                        </tr>
                        <tr>
                            <td><strong>批注功能</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>管理部门可批注，不能修改其他内容</td>
                        </tr>
                        <tr>
                            <td><strong>导出功能</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>管理部门可导出相关标签内容</td>
                        </tr>
                        <tr>
                            <td><strong>标签管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>管理部门可增删改标签，基层只能勾选</td>
                        </tr>
                        <tr>
                            <td><strong>用户管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可管理用户和权限</td>
                        </tr>
                        <tr>
                            <td><strong>部门管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可修改部门名称和架构</td>
                        </tr>
                        <tr>
                            <td><strong>年度管理</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>仅主管可创建年度台账和归档</td>
                        </tr>
                        <tr>
                            <td><strong>归属修订</strong></td>
                            <td><input type="checkbox" checked disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td><input type="checkbox" disabled class="permission-check"></td>
                            <td>主管可修订其他部门管理员归属</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div id="user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">添加/编辑用户</h3>
                <span class="close" onclick="closeModal('user-modal')">&times;</span>
            </div>
            <form id="user-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">姓名<span class="required">*</span>：</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">用户名<span class="required">*</span>：</label>
                        <input type="text" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">部门<span class="required">*</span>：</label>
                        <select class="form-control" required>
                            <option value="">请选择部门</option>
                            <option value="综合办">综合办</option>
                            <option value="人力">人力</option>
                            <option value="财务">财务</option>
                            <option value="党建部">党建部</option>
                            <option value="纪委办">纪委办</option>
                            <option value="工会">工会</option>
                            <option value="管控部">管控部</option>
                            <option value="生产中心A">生产中心A</option>
                            <option value="生产中心B">生产中心B</option>
                            <option value="运输中心">运输中心</option>
                            <option value="运维中心">运维中心</option>
                            <option value="团委">团委</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">角色<span class="required">*</span>：</label>
                        <select class="form-control" required>
                            <option value="">请选择角色</option>
                            <option value="主管">主管</option>
                            <option value="管理部门">管理部门</option>
                            <option value="基层用户">基层用户</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱：</label>
                    <input type="email" class="form-control">
                </div>
                <div class="form-group">
                    <label class="form-label">手机号：</label>
                    <input type="tel" class="form-control">
                </div>
                <div class="form-group">
                    <label class="form-label">状态：</label>
                    <select class="form-control">
                        <option value="启用">启用</option>
                        <option value="停用">停用</option>
                    </select>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('user-modal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 显示目标标签页
            document.getElementById(tabName + '-tab').classList.add('active');

            // 更新标签页状态
            document.querySelectorAll('.management-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // 模态框操作
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 编辑用户
        function editUser(userId) {
            openModal('user-modal');
            // 这里可以加载用户数据到表单
            showMessage('加载用户信息...', 'info');
        }

        // 添加标签
        function addTag(category) {
            const tagName = prompt('请输入新标签名称：');
            if (tagName && tagName.trim()) {
                showMessage(`标签"${tagName}"添加成功！`, 'success');
                // 这里可以添加实际的标签添加逻辑
            }
        }

        // 用户表单提交
        document.getElementById('user-form').addEventListener('submit', function(e) {
            e.preventDefault();
            showMessage('用户信息保存成功！', 'success');
            closeModal('user-modal');
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>