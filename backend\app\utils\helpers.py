"""
通用工具函数
"""
from typing import Any, Dict, List
import json


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    安全的JSON解析，失败时返回默认值
    """
    try:
        return json.loads(json_str) if json_str else default
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any) -> str:
    """
    安全的JSON序列化
    """
    try:
        return json.dumps(obj, ensure_ascii=False)
    except (TypeError, ValueError):
        return "[]"


def paginate_query(query, page: int, limit: int) -> Dict[str, Any]:
    """
    通用分页函数
    """
    total = query.count()
    offset = (page - 1) * limit
    items = query.offset(offset).limit(limit).all()
    
    import math
    pages = math.ceil(total / limit) if total > 0 else 1
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": pages
    }
