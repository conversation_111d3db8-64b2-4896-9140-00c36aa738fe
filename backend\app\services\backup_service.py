"""
备份管理服务
"""
import os
import json
import hashlib
import shutil
import zipfile
from datetime import datetime
from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc

from ..models.year import Backup, Year
from ..models.ledger import Ledger
from ..models.department import Department
from ..models.tag import Tag
from ..models.user import User
from ..schemas.year import (
    BackupCreate, BackupResponse, BackupListResponse, MessageResponse
)


class BackupService:
    """备份管理服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.backup_dir = os.path.join("data", "backups")
        self._ensure_backup_dir()

    def _ensure_backup_dir(self):
        """确保备份目录存在"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(os.path.join(self.backup_dir, "manual"), exist_ok=True)
        os.makedirs(os.path.join(self.backup_dir, "auto"), exist_ok=True)

    def get_backups(self, skip: int = 0, limit: int = 20) -> BackupListResponse:
        """获取备份列表"""
        query = self.db.query(Backup).order_by(desc(Backup.created_at))
        total = query.count()
        backups = query.offset(skip).limit(limit).all()

        return BackupListResponse(
            data=[BackupResponse.from_orm(backup) for backup in backups],
            total=total,
            page=(skip // limit) + 1,
            limit=limit
        )

    def get_backup_by_id(self, backup_id: int) -> Optional[BackupResponse]:
        """根据ID获取备份信息"""
        backup = self.db.query(Backup).filter(Backup.id == backup_id).first()
        if backup:
            return BackupResponse.from_orm(backup)
        return None

    def create_backup(self, backup_data: BackupCreate, current_user_id: int) -> BackupResponse:
        """创建备份"""
        # 创建备份记录
        backup = Backup(
            backup_name=backup_data.backup_name,
            backup_type=backup_data.backup_type,
            backup_scope=backup_data.backup_scope,
            target_year=backup_data.target_year,
            description=backup_data.description,
            status="creating",
            created_by=current_user_id
        )
        self.db.add(backup)
        self.db.flush()  # 获取ID

        try:
            # 执行备份操作
            backup_info = self._perform_backup(backup)

            # 更新备份记录
            backup.file_path = backup_info["file_path"]
            backup.file_size = backup_info["file_size"]
            backup.checksum = backup_info["checksum"]
            backup.status = "completed"

            self.db.commit()

        except Exception as e:
            # 备份失败，更新状态
            backup.status = "failed"
            backup.description = f"备份失败: {str(e)}"
            self.db.commit()
            raise e

        return BackupResponse.from_orm(backup)

    def delete_backup(self, backup_id: int) -> bool:
        """删除备份"""
        backup = self.db.query(Backup).filter(Backup.id == backup_id).first()
        if not backup:
            return False

        # 删除备份文件
        if backup.file_path and os.path.exists(backup.file_path):
            try:
                os.remove(backup.file_path)
            except Exception as e:
                print(f"删除备份文件失败: {e}")

        # 删除数据库记录
        self.db.delete(backup)
        self.db.commit()
        return True

    def download_backup(self, backup_id: int) -> Optional[str]:
        """获取备份文件路径用于下载"""
        backup = self.db.query(Backup).filter(Backup.id == backup_id).first()
        if backup and backup.file_path and os.path.exists(backup.file_path):
            return backup.file_path
        return None

    def _perform_backup(self, backup: Backup) -> Dict[str, Any]:
        """执行备份操作"""
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_{backup.id}_{timestamp}.zip"

        # 确定备份目录
        backup_subdir = backup.backup_type  # manual 或 auto
        backup_path = os.path.join(self.backup_dir, backup_subdir, backup_filename)

        # 创建临时目录
        temp_dir = os.path.join(self.backup_dir, "temp", f"backup_{backup.id}")
        os.makedirs(temp_dir, exist_ok=True)

        try:
            # 根据备份范围收集数据
            if backup.backup_scope == "full":
                self._backup_full_data(temp_dir)
            elif backup.backup_scope == "year" and backup.target_year:
                self._backup_year_data(temp_dir, backup.target_year)
            else:
                raise ValueError(f"不支持的备份范围: {backup.backup_scope}")

            # 创建ZIP文件
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arcname)

            # 计算文件大小和校验和
            file_size = os.path.getsize(backup_path)
            checksum = self._calculate_checksum(backup_path)

            return {
                "file_path": backup_path,
                "file_size": file_size,
                "checksum": checksum
            }

        finally:
            # 清理临时目录
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    def _backup_full_data(self, temp_dir: str):
        """备份全部数据"""
        # 备份台账数据
        self._backup_ledgers(temp_dir)

        # 备份配置数据
        self._backup_departments(temp_dir)
        self._backup_tags(temp_dir)
        self._backup_users(temp_dir)

        # 备份年度数据
        self._backup_years(temp_dir)

    def _backup_year_data(self, temp_dir: str, year: int):
        """备份指定年度数据"""
        # 备份指定年度的台账数据
        self._backup_ledgers(temp_dir, year)

        # 备份年度信息
        self._backup_years(temp_dir, year)

    def _backup_ledgers(self, temp_dir: str, year: Optional[int] = None):
        """备份台账数据"""
        query = self.db.query(Ledger)
        if year:
            query = query.filter(Ledger.year == year)

        ledgers = query.all()

        ledger_data = []
        for ledger in ledgers:
            ledger_dict = {
                "id": ledger.id,
                "sequence_number": ledger.sequence_number,
                "work_item": ledger.work_item,
                "measures": ledger.measures,
                "progress": ledger.progress,
                "effectiveness": ledger.effectiveness,
                "responsible_dept": ledger.responsible_dept,
                "counterpart_depts": json.loads(ledger.counterpart_depts) if ledger.counterpart_depts else [],
                "tags": json.loads(ledger.tags) if ledger.tags else [],
                "remarks": ledger.remarks,
                "status": ledger.status,
                "year": ledger.year,
                "created_at": ledger.created_at.isoformat() if ledger.created_at else None,
                "updated_at": ledger.updated_at.isoformat() if ledger.updated_at else None,
                "created_by": ledger.created_by
            }
            ledger_data.append(ledger_dict)

        # 写入文件
        ledgers_file = os.path.join(temp_dir, "ledgers.json")
        with open(ledgers_file, 'w', encoding='utf-8') as f:
            json.dump(ledger_data, f, ensure_ascii=False, indent=2)

    def _backup_departments(self, temp_dir: str):
        """备份部门数据"""
        departments = self.db.query(Department).all()

        dept_data = []
        for dept in departments:
            dept_dict = {
                "id": dept.id,
                "name": dept.name,
                "type": dept.type,
                "status": dept.status,
                "created_at": dept.created_at.isoformat() if dept.created_at else None
            }
            dept_data.append(dept_dict)

        # 写入文件
        depts_file = os.path.join(temp_dir, "departments.json")
        with open(depts_file, 'w', encoding='utf-8') as f:
            json.dump(dept_data, f, ensure_ascii=False, indent=2)

    def _backup_tags(self, temp_dir: str):
        """备份标签数据"""
        tags = self.db.query(Tag).all()

        tag_data = []
        for tag in tags:
            tag_dict = {
                "id": tag.id,
                "name": tag.name,
                "category": tag.category,
                "created_at": tag.created_at.isoformat() if tag.created_at else None
            }
            tag_data.append(tag_dict)

        # 写入文件
        tags_file = os.path.join(temp_dir, "tags.json")
        with open(tags_file, 'w', encoding='utf-8') as f:
            json.dump(tag_data, f, ensure_ascii=False, indent=2)

    def _backup_users(self, temp_dir: str):
        """备份用户数据（不包含密码）"""
        users = self.db.query(User).all()

        user_data = []
        for user in users:
            user_dict = {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "phone": user.phone,
                "department_id": user.department_id,
                "role": user.role,
                "is_active": user.is_active,
                "created_at": user.created_at.isoformat() if user.created_at else None
            }
            user_data.append(user_dict)

        # 写入文件
        users_file = os.path.join(temp_dir, "users.json")
        with open(users_file, 'w', encoding='utf-8') as f:
            json.dump(user_data, f, ensure_ascii=False, indent=2)

    def _backup_years(self, temp_dir: str, year: Optional[int] = None):
        """备份年度数据"""
        query = self.db.query(Year)
        if year:
            query = query.filter(Year.year == year)

        years = query.all()

        year_data = []
        for year_obj in years:
            year_dict = {
                "id": year_obj.id,
                "year": year_obj.year,
                "status": year_obj.status,
                "total_ledgers": year_obj.total_ledgers,
                "completed_ledgers": year_obj.completed_ledgers,
                "in_progress_ledgers": year_obj.in_progress_ledgers,
                "draft_ledgers": year_obj.draft_ledgers,
                "created_at": year_obj.created_at.isoformat() if year_obj.created_at else None,
                "archived_at": year_obj.archived_at.isoformat() if year_obj.archived_at else None,
                "created_by": year_obj.created_by
            }
            year_data.append(year_dict)

        # 写入文件
        years_file = os.path.join(temp_dir, "years.json")
        with open(years_file, 'w', encoding='utf-8') as f:
            json.dump(year_data, f, ensure_ascii=False, indent=2)

    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
