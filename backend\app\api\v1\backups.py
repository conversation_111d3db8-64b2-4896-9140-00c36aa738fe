"""
备份管理API路由
"""
import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from ...api.deps import get_db, get_current_user
from ...models.user import User
from ...services.backup_service import BackupService
from ...schemas.year import (
    BackupCreate, BackupResponse, BackupListResponse, MessageResponse
)

router = APIRouter(tags=["备份管理"])


@router.get("", response_model=BackupListResponse, summary="获取备份列表")
async def get_backups(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取备份列表

    - **page**: 页码，从1开始
    - **size**: 每页记录数，最大100条

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    backup_service = BackupService(db)
    skip = (page - 1) * size
    return backup_service.get_backups(skip=skip, limit=size)


@router.post("", response_model=BackupResponse, summary="创建备份")
async def create_backup(
    backup_data: BackupCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新备份

    - **backup_name**: 备份名称
    - **backup_type**: 备份类型 (manual/auto)
    - **backup_scope**: 备份范围 (full/year/partial)
    - **target_year**: 目标年度 (当backup_scope为year时必填)
    - **description**: 备份描述

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    # 验证参数
    if backup_data.backup_scope == "year" and not backup_data.target_year:
        raise HTTPException(status_code=400, detail="年度备份必须指定目标年度")

    backup_service = BackupService(db)
    try:
        return backup_service.create_backup(backup_data, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建备份失败: {str(e)}")


@router.get("/{backup_id}", response_model=BackupResponse, summary="获取备份详情")
async def get_backup(
    backup_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取指定备份的详细信息

    - **backup_id**: 备份ID

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    backup_service = BackupService(db)
    backup = backup_service.get_backup_by_id(backup_id)

    if not backup:
        raise HTTPException(status_code=404, detail=f"备份 {backup_id} 不存在")

    return backup


@router.delete("/{backup_id}", response_model=MessageResponse, summary="删除备份")
async def delete_backup(
    backup_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除备份

    - **backup_id**: 备份ID

    需要管理员权限，会同时删除备份文件和数据库记录
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    backup_service = BackupService(db)
    success = backup_service.delete_backup(backup_id)

    if not success:
        raise HTTPException(status_code=404, detail=f"备份 {backup_id} 不存在")

    return MessageResponse(message=f"备份 {backup_id} 删除成功")


@router.get("/{backup_id}/download", summary="下载备份文件")
async def download_backup(
    backup_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    下载备份文件

    - **backup_id**: 备份ID

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    backup_service = BackupService(db)

    # 获取备份信息
    backup = backup_service.get_backup_by_id(backup_id)
    if not backup:
        raise HTTPException(status_code=404, detail=f"备份 {backup_id} 不存在")

    # 获取文件路径
    file_path = backup_service.download_backup(backup_id)
    if not file_path:
        raise HTTPException(status_code=404, detail="备份文件不存在")

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="备份文件已丢失")

    # 生成下载文件名
    filename = f"{backup.backup_name}_{backup.id}.zip"

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/zip"
    )


@router.post("/quick", response_model=BackupResponse, summary="快速备份")
async def quick_backup(
    backup_type: str = Query("manual", description="备份类型"),
    target_year: int = Query(None, description="目标年度（可选）"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    快速创建备份

    - **backup_type**: 备份类型 (manual/auto)
    - **target_year**: 目标年度（可选，如果指定则只备份该年度）

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    from datetime import datetime

    # 生成备份名称
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if target_year:
        backup_name = f"{target_year}年度备份_{timestamp}"
        backup_scope = "year"
    else:
        backup_name = f"完整备份_{timestamp}"
        backup_scope = "full"

    # 创建备份数据
    backup_data = BackupCreate(
        backup_name=backup_name,
        backup_type=backup_type,
        backup_scope=backup_scope,
        target_year=target_year,
        description=f"快速{backup_scope}备份"
    )

    backup_service = BackupService(db)
    try:
        return backup_service.create_backup(backup_data, current_user.id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"快速备份失败: {str(e)}")


@router.get("/stats/summary", summary="获取备份统计")
async def get_backup_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取备份统计信息

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    backup_service = BackupService(db)

    # 获取所有备份
    all_backups = backup_service.get_backups(skip=0, limit=1000)

    # 统计信息
    total_backups = all_backups.total
    manual_backups = len([b for b in all_backups.data if b.backup_type == "manual"])
    auto_backups = len([b for b in all_backups.data if b.backup_type == "auto"])
    completed_backups = len([b for b in all_backups.data if b.status == "completed"])
    failed_backups = len([b for b in all_backups.data if b.status == "failed"])

    # 计算总大小
    total_size = sum([b.file_size or 0 for b in all_backups.data])
    total_size_mb = round(total_size / (1024 * 1024), 2)

    return {
        "total_backups": total_backups,
        "manual_backups": manual_backups,
        "auto_backups": auto_backups,
        "completed_backups": completed_backups,
        "failed_backups": failed_backups,
        "success_rate": round((completed_backups / total_backups * 100), 1) if total_backups > 0 else 0,
        "total_size_bytes": total_size,
        "total_size_mb": total_size_mb,
        "recent_backups": all_backups.data[:5]  # 最近5个备份
    }
