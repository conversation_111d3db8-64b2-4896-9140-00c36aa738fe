"""
部门变更历史数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .base import Base


class DepartmentChange(Base):
    """部门变更历史表模型"""

    __tablename__ = "department_changes"

    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # 关联字段
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=True, index=True, comment="部门ID")
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="操作人ID")

    # 变更信息
    change_type = Column(String(50), nullable=False, index=True, comment="变更类型: create/update/delete/merge/split")
    old_name = Column(String(100), nullable=True, comment="原部门名称")
    new_name = Column(String(100), nullable=True, comment="新部门名称")
    old_type = Column(String(50), nullable=True, comment="原部门类型")
    new_type = Column(String(50), nullable=True, comment="新部门类型")
    reason = Column(Text, nullable=True, comment="变更原因")

    # 影响统计
    affected_ledgers = Column(Integer, default=0, comment="影响的台账数量")
    affected_users = Column(Integer, default=0, comment="影响的用户数量")

    # 状态管理
    status = Column(String(20), default="completed", nullable=False, comment="变更状态: pending/completed/failed/rollback")

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True, comment="创建时间")

    # 关联关系
    department = relationship("Department", back_populates="changes")
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<DepartmentChange(id={self.id}, type='{self.change_type}', dept_id={self.department_id})>"

    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == "completed"

    @property
    def is_pending(self) -> bool:
        """是否待处理"""
        return self.status == "pending"

    @property
    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == "failed"

    @property
    def change_summary(self) -> str:
        """变更摘要"""
        if self.change_type == "create":
            return f"创建部门: {self.new_name}"
        elif self.change_type == "update":
            if self.old_name != self.new_name:
                return f"重命名部门: {self.old_name} → {self.new_name}"
            elif self.old_type != self.new_type:
                return f"变更类型: {self.old_type} → {self.new_type}"
            else:
                return f"更新部门: {self.new_name}"
        elif self.change_type == "delete":
            return f"删除部门: {self.old_name}"
        elif self.change_type == "merge":
            return f"合并部门到: {self.new_name}"
        elif self.change_type == "split":
            return f"拆分部门: {self.old_name}"
        else:
            return f"未知操作: {self.change_type}"
