<template>
  <div class="department-changes">
    <el-card>
      <template #header>
        <span>部门变更历史</span>
      </template>
      
      <el-table :data="changes" v-loading="loading" border>
        <el-table-column prop="change_summary" label="变更摘要" min-width="200" />
        <el-table-column prop="change_type" label="变更类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getChangeTypeTag(row.change_type)">{{ getChangeTypeText(row.change_type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="变更时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const changes = ref([])
const loading = ref(false)

const loadChanges = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/v1/departments/changes', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      changes.value = data.data || []
    }
  } catch (error) {
    console.error('获取变更历史失败:', error)
  } finally {
    loading.value = false
  }
}

const getChangeTypeTag = (type) => {
  switch (type) {
    case 'create': return 'success'
    case 'update': return 'primary'
    case 'delete': return 'danger'
    default: return 'info'
  }
}

const getChangeTypeText = (type) => {
  switch (type) {
    case 'create': return '创建'
    case 'update': return '更新'
    case 'delete': return '删除'
    default: return '未知'
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  loadChanges()
})
</script>
