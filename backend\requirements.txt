# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1

# 数据验证 - 使用兼容版本
pydantic==2.4.2
pydantic-settings==2.0.3

# 认证相关
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 测试框架
pytest==7.4.3
httpx==0.25.2
pytest-asyncio==0.21.1

# 文件处理（后续模块需要）
openpyxl==3.1.2
python-docx==0.8.11
reportlab==4.0.4

# 工具库
python-dateutil==2.8.2
typing-extensions==4.8.0

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
