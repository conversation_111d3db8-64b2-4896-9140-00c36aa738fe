"""
应用启动脚本
"""
import uvicorn
from app.main import app
from app.core.config import settings

if __name__ == "__main__":
    print(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    print(f"📖 API文档: http://localhost:{settings.backend_port}/docs")
    print(f"🔧 调试模式: {settings.debug}")
    print(f"🌐 服务端口: {settings.backend_port}")

    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.backend_port,
        reload=settings.debug,
        log_level=settings.log_level
    )
