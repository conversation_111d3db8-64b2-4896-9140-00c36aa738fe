/**
 * 认证状态管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, tokenManager, userManager, type UserInfo, type UserPermissions } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const permissions = ref<UserPermissions | null>(null)
  const isLoggedIn = ref(false)
  const loading = ref(false)

  // 计算属性 - 基于角色
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isManager = computed(() => user.value?.role === 'manager')
  const isUser = computed(() => user.value?.role === 'user')

  // 计算属性 - 基于后端权限数据
  const canManageUsers = computed(() => {
    return permissions.value?.can_manage_users ?? (user.value?.role === 'admin')
  })
  const canManageTags = computed(() => {
    return permissions.value?.can_manage_tags ?? ['admin', 'manager'].includes(user.value?.role || '')
  })
  const canEditSequence = computed(() => {
    return permissions.value?.can_edit_sequence ?? (user.value?.role === 'admin')
  })
  const canExportData = computed(() => {
    return permissions.value?.can_export_data ?? ['admin', 'manager'].includes(user.value?.role || '')
  })
  const canEditAllLedgers = computed(() => {
    return permissions.value?.can_edit_all_ledgers ?? (user.value?.role === 'admin')
  })
  const canDeleteLedgers = computed(() => {
    return hasPermission('delete_ledgers')
  })
  const canAddComments = computed(() => {
    return hasPermission('add_comments')
  })
  const canManageReminders = computed(() => {
    return permissions.value?.can_manage_reminders ?? ['admin', 'manager'].includes(user.value?.role || '')
  })

  // 初始化认证状态
  const initAuth = async () => {
    const token = tokenManager.getToken()
    const userInfo = userManager.getUserInfo()

    if (token && userInfo) {
      user.value = userInfo
      isLoggedIn.value = true
      // 获取最新的用户权限
      try {
        await getUserPermissions()
      } catch (error) {
        console.error('Failed to get user permissions:', error)
        // 如果获取权限失败，可能token已过期，清除认证信息
        userManager.clearAuth()
        user.value = null
        isLoggedIn.value = false
      }
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    try {
      loading.value = true
      const response = await authAPI.login({ username, password })

      // 保存认证信息
      tokenManager.setToken(response.access_token)
      userManager.setUserInfo(response.user)

      // 更新状态
      user.value = response.user
      isLoggedIn.value = true

      // 获取用户权限
      await getUserPermissions()

      ElMessage.success('登录成功')
      return true
    } catch (error: any) {
      console.error('Login error:', error)
      ElMessage.error(error.response?.data?.detail || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除认证信息
      userManager.clearAuth()
      user.value = null
      permissions.value = null
      isLoggedIn.value = false
      ElMessage.success('已退出登录')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      // 检查是否有有效的token
      const token = tokenManager.getToken()
      if (!token) {
        throw new Error('No token available')
      }

      // 暂时跳过API调用，直接使用存储的用户信息
      const storedUserInfo = userManager.getUserInfo()
      if (storedUserInfo) {
        user.value = storedUserInfo
        return storedUserInfo
      }

      // 如果没有存储的用户信息，尝试API调用
      try {
        const userInfo = await authAPI.getCurrentUser()
        user.value = userInfo
        userManager.setUserInfo(userInfo)
        return userInfo
      } catch (apiError) {
        console.warn('API调用失败，使用Token中的用户信息:', apiError)
        // 从Token中解析用户信息作为备用方案
        const payload = JSON.parse(atob(token.split('.')[1]))
        const fallbackUser = {
          id: parseInt(payload.sub),
          username: payload.username,
          full_name: payload.username,
          role: payload.role,
          is_active: true,
          login_count: 0
        }
        user.value = fallbackUser
        return fallbackUser
      }
    } catch (error: any) {
      console.error('Get current user error:', error)

      // 如果是401错误，清除认证信息
      if (error.response?.status === 401) {
        logout()
      }

      throw error
    }
  }

  // 获取用户权限
  const getUserPermissions = async () => {
    try {
      const userPermissions = await authAPI.getUserPermissions()
      permissions.value = userPermissions
      localStorage.setItem('user_permissions', JSON.stringify(userPermissions))
      return userPermissions
    } catch (error) {
      console.error('Get user permissions error:', error)
      throw error
    }
  }

  // 检查权限
  const checkPermission = async (permission: string) => {
    try {
      const result = await authAPI.checkPermission(permission)
      return result.has_permission
    } catch (error) {
      console.error('Check permission error:', error)
      return false
    }
  }

  // 验证操作权限
  const validateAction = async (action: string, resourceType: string, resourceId?: number, data?: any) => {
    try {
      const result = await authAPI.validateAction({
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        data
      })
      return result
    } catch (error) {
      console.error('Validate action error:', error)
      return { allowed: false, reason: '权限验证失败' }
    }
  }

  // 修改密码
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      loading.value = true
      await authAPI.changePassword({
        current_password: currentPassword,
        new_password: newPassword
      })
      ElMessage.success('密码修改成功')
      return true
    } catch (error: any) {
      console.error('Change password error:', error)
      ElMessage.error(error.response?.data?.detail || '密码修改失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 刷新用户信息
  const refreshUser = async () => {
    try {
      await getCurrentUser()
      await getUserPermissions()
    } catch (error) {
      console.error('Refresh user error:', error)
      // 如果刷新失败，可能是token过期，执行登出
      logout()
    }
  }

  // 检查是否有特定权限
  const hasPermission = (permission: string): boolean => {
    if (!permissions.value) return false
    return permissions.value.permissions.includes(permission)
  }

  // 检查是否可以访问路由
  const canAccessRoute = (routeName: string): boolean => {
    if (!isLoggedIn.value) return false

    switch (routeName) {
      case 'user-management':
        return canManageUsers.value
      case 'tag-management':
        return canManageTags.value
      case 'permission-management':
        return isAdmin.value
      case 'reminders':
        return canManageReminders.value
      default:
        return true
    }
  }

  return {
    // 状态
    user,
    permissions,
    isLoggedIn,
    loading,

    // 计算属性
    isAdmin,
    isManager,
    isUser,
    canManageUsers,
    canManageTags,
    canEditSequence,
    canExportData,
    canEditAllLedgers,
    canDeleteLedgers,
    canAddComments,
    canManageReminders,

    // 方法
    initAuth,
    login,
    logout,
    getCurrentUser,
    getUserPermissions,
    checkPermission,
    validateAction,
    changePassword,
    refreshUser,
    hasPermission,
    canAccessRoute
  }
})
