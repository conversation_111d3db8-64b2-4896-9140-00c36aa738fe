"""
仪表板API路由
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from ...api.deps import get_db, get_current_user
from ...models.user import User

router = APIRouter(tags=["仪表板"])


@router.get("/stats", summary="获取仪表板统计数据")
async def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取仪表板统计数据
    """
    # 返回仪表板统计数据
    return {
        "totalLedgers": 4,
        "completedLedgers": 1,
        "pendingLedgers": 2,
        "overdueReminders": 1,
        "activeUsers": 13,
        "totalDepartments": 8,
        "recentActivities": [
            {
                "id": 1,
                "type": "ledger_created",
                "title": "新建台账：年度工作计划",
                "user": "系统管理员",
                "time": "2025-07-27 10:30:00"
            },
            {
                "id": 2,
                "type": "ledger_updated",
                "title": "更新台账：季度报告",
                "user": "张三",
                "time": "2025-07-27 09:15:00"
            },
            {
                "id": 3,
                "type": "reminder_sent",
                "title": "发送提醒：月度总结",
                "user": "系统",
                "time": "2025-07-27 08:00:00"
            }
        ],
        "monthlyStats": [
            {"month": "1月", "created": 12, "completed": 10},
            {"month": "2月", "created": 15, "completed": 13},
            {"month": "3月", "created": 18, "completed": 16},
            {"month": "4月", "created": 22, "completed": 20},
            {"month": "5月", "created": 25, "completed": 23},
            {"month": "6月", "created": 28, "completed": 26},
            {"month": "7月", "created": 32, "completed": 28}
        ]
    }


@router.get("/summary", summary="获取仪表板概览")
async def get_dashboard_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取仪表板概览信息
    """
    try:
        user_name = current_user.username
        user_role = current_user.role

        return {
            "user": {
                "name": user_name,
                "role": user_role,
                "department": "未分配",
                "lastLogin": "2025-07-27 08:30:00"
            },
            "todayTasks": [
                {"id": 1, "title": "完成月度报告", "priority": "high", "deadline": "2025-07-27 18:00:00"},
                {"id": 2, "title": "审核台账数据", "priority": "medium", "deadline": "2025-07-28 12:00:00"},
                {"id": 3, "title": "部门会议准备", "priority": "low", "deadline": "2025-07-29 10:00:00"}
            ],
            "notifications": [
                {"id": 1, "type": "reminder", "title": "台账提醒", "content": "有3个台账即将到期", "time": "10分钟前"},
                {"id": 2, "type": "system", "title": "系统通知", "content": "系统将于今晚进行维护", "time": "1小时前"}
            ],
            "quickStats": {
                "myLedgers": 8,
                "pendingApprovals": 2,
                "overdueItems": 1,
                "completionRate": 85.5
            }
        }
    except Exception as e:
        # 如果出错，返回基础数据
        return {
            "user": {
                "name": "用户",
                "role": "user",
                "department": "未分配",
                "lastLogin": "2025-07-27 08:30:00"
            },
            "todayTasks": [],
            "notifications": [],
            "quickStats": {
                "myLedgers": 0,
                "pendingApprovals": 0,
                "overdueItems": 0,
                "completionRate": 0.0
            }
        }
