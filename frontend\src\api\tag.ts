/**
 * 标签管理相关API调用
 */
import { request } from './index'

// 标签接口类型定义
export interface TagCreateRequest {
  name: string
  category: string
}

export interface TagUpdateRequest {
  name?: string
  category?: string
}

export interface TagResponse {
  id: number
  name: string
  category: string
  created_at: string
}

export interface TagPermissions {
  can_create: boolean
  can_update: boolean
  can_delete: boolean
  can_view: boolean
  role: string
  message: string
}

// 兼容旧接口
export interface Tag {
  id: number
  name: string
  category: 'work_type' | 'time_cycle' | 'priority' | 'department'
}

// 标签API
export const tagAPI = {
  // 获取标签列表
  getTags: (category?: string): Promise<TagResponse[]> => {
    const params = category ? { category } : {}
    return request.get('/tags', { params })
  },

  // 创建标签
  createTag: (data: TagCreateRequest): Promise<TagResponse> => {
    return request.post('/tags', data)
  },

  // 更新标签
  updateTag: (tagId: number, data: TagUpdateRequest): Promise<TagResponse> => {
    return request.put(`/tags/${tagId}`, data)
  },

  // 删除标签
  deleteTag: (tagId: number): Promise<{ message: string }> => {
    return request.delete(`/tags/${tagId}`)
  },

  // 获取标签分类
  getTagCategories: (): Promise<{
    categories: string[]
    descriptions: Record<string, string>
  }> => {
    return request.get('/tags/categories')
  },

  // 获取标签权限
  getTagPermissions: (): Promise<TagPermissions> => {
    return request.get('/tags/permissions')
  }
}

// 兼容旧API
export const tagApi = {
  // 获取标签列表
  async getList(): Promise<Tag[]> {
    try {
      const response = await tagAPI.getTags()

      // 处理后端返回的多种数据格式
      let tags: any[] = []
      if (Array.isArray(response)) {
        tags = response
      } else if (response && response.data && Array.isArray(response.data)) {
        tags = response.data
      } else if (response && response.items && Array.isArray(response.items)) {
        tags = response.items
      } else if (response && response.tags && Array.isArray(response.tags)) {
        tags = response.tags
      } else {
        console.warn('标签API返回格式异常:', response)
        return []
      }

      return tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        category: tag.category as Tag['category']
      }))
    } catch (error) {
      console.error('获取标签列表失败:', error)
      return []
    }
  },

  // 按类别获取标签
  async getByCategory(category: Tag['category']): Promise<Tag[]> {
    try {
      const response = await tagAPI.getTags(category)

      // 处理后端返回的多种数据格式
      let tags: any[] = []
      if (Array.isArray(response)) {
        tags = response
      } else if (response && response.data && Array.isArray(response.data)) {
        tags = response.data
      } else if (response && response.items && Array.isArray(response.items)) {
        tags = response.items
      } else if (response && response.tags && Array.isArray(response.tags)) {
        tags = response.tags
      } else {
        console.warn('标签API返回格式异常:', response)
        return []
      }

      return tags.map(tag => ({
        id: tag.id,
        name: tag.name,
        category: tag.category as Tag['category']
      }))
    } catch (error) {
      console.error('获取标签列表失败:', error)
      return []
    }
  },

  // 获取工作类型标签
  async getWorkTypeTags(): Promise<Tag[]> {
    return this.getByCategory('work_type')
  },

  // 获取时间周期标签
  async getTimeCycleTags(): Promise<Tag[]> {
    return this.getByCategory('time_cycle')
  },

  // 获取优先级标签
  async getPriorityTags(): Promise<Tag[]> {
    return this.getByCategory('priority')
  },

  // 获取部门相关标签
  async getDepartmentTags(): Promise<Tag[]> {
    return this.getByCategory('department')
  }
}

// 标签分类常量
export const TAG_CATEGORIES = {
  WORK_TYPE: 'work_type',
  TIME_CYCLE: 'time_cycle',
  PRIORITY: 'priority',
  DEPARTMENT: 'department'
} as const

export const CATEGORY_LABELS = {
  [TAG_CATEGORIES.WORK_TYPE]: '工作类型',
  [TAG_CATEGORIES.TIME_CYCLE]: '时间周期',
  [TAG_CATEGORIES.PRIORITY]: '优先级',
  [TAG_CATEGORIES.DEPARTMENT]: '部门归属'
} as const
