<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建新年度"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="年度" prop="year">
        <el-input-number
          v-model="form.year"
          :min="2020"
          :max="2030"
          :step="1"
          placeholder="请输入年度"
          style="width: 100%"
        />
        <div class="form-tip">建议创建当前年度或下一年度</div>
      </el-form-item>

      <el-form-item label="配置继承">
        <el-switch
          v-model="form.inherit_config"
          active-text="继承现有配置"
          inactive-text="不继承配置"
        />
        <div class="form-tip">
          继承配置将复制当前的部门结构、标签分类和用户权限设置
        </div>
      </el-form-item>

      <el-form-item label="归档上一年度" v-if="form.year > 2020">
        <el-switch
          v-model="form.archive_previous"
          active-text="自动归档"
          inactive-text="保持活跃"
        />
        <div class="form-tip">
          自动归档将把{{ form.year - 1 }}年度的数据移动到归档存储中
        </div>
      </el-form-item>

      <el-form-item label="创建说明">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入创建说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 预览信息 -->
      <el-card class="preview-card" shadow="never" v-if="form.year">
        <template #header>
          <span>创建预览</span>
        </template>
        
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">目标年度：</span>
            <span class="preview-value">{{ form.year }}年</span>
          </div>
          
          <div class="preview-item">
            <span class="preview-label">配置继承：</span>
            <span class="preview-value">
              <el-tag :type="form.inherit_config ? 'success' : 'info'">
                {{ form.inherit_config ? '是' : '否' }}
              </el-tag>
            </span>
          </div>
          
          <div class="preview-item" v-if="form.year > 2020">
            <span class="preview-label">归档{{ form.year - 1 }}年：</span>
            <span class="preview-value">
              <el-tag :type="form.archive_previous ? 'warning' : 'info'">
                {{ form.archive_previous ? '是' : '否' }}
              </el-tag>
            </span>
          </div>

          <div class="preview-item" v-if="form.inherit_config">
            <span class="preview-label">预计继承：</span>
            <span class="preview-value">部门、标签、用户权限配置</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.year"
        >
          {{ loading ? '创建中...' : '创建年度' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()

const form = reactive({
  year: new Date().getFullYear() + 1,
  inherit_config: true,
  archive_previous: false,
  description: ''
})

const rules = {
  year: [
    { required: true, message: '请输入年度', trigger: 'blur' },
    { type: 'number', min: 2020, max: 2030, message: '年度范围为2020-2030', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  form.year = new Date().getFullYear() + 1
  form.inherit_config = true
  form.archive_previous = false
  form.description = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const response = await fetch('/api/v1/years', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({
        year: form.year,
        inherit_config: form.inherit_config,
        archive_previous: form.archive_previous
      })
    })
    
    if (response.ok) {
      const result = await response.json()
      
      ElMessage.success({
        message: result.message || '年度创建成功',
        duration: 3000
      })
      
      // 显示继承信息
      if (result.inherited_items) {
        const inheritedInfo = Object.entries(result.inherited_items)
          .map(([key, count]) => `${getInheritedItemName(key)}: ${count}个`)
          .join(', ')
        
        ElMessage.info({
          message: `已继承配置 - ${inheritedInfo}`,
          duration: 5000
        })
      }
      
      emit('success', result)
      handleClose()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '年度创建失败')
    }
  } catch (error) {
    console.error('年度创建失败:', error)
    ElMessage.error('年度创建失败')
  } finally {
    loading.value = false
  }
}

const getInheritedItemName = (key) => {
  const names = {
    departments: '部门',
    tags: '标签',
    users: '用户'
  }
  return names[key] || key
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.preview-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
}

.preview-value {
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
