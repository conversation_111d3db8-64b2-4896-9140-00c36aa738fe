<template>
  <div class="ledger-page">
    <!-- 固定头部 -->
    <div class="page-header">
      <el-button @click="handleBack" type="primary" size="small">返回列表</el-button>
      <h2>{{ isEdit ? '编辑台账' : '新建台账' }}</h2>
      <el-tag v-if="userPermissions.role" type="info">
        {{ userPermissions.role }} | {{ userPermissions.department }}
      </el-tag>
    </div>

    <!-- 可滚动的表单容器 -->
    <div class="scrollable-container">
      <div class="form-content">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="main-form"
        >
        <!-- 基本信息 - 强制可见 -->
        <div class="form-section basic-info">
          <h3 class="section-title">📋 基本信息</h3>

          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item label="序号" prop="sequence_number">
                <el-input
                  v-model="form.sequence_number"
                  placeholder="请输入序号"
                  :disabled="!canEditSequence"
                  @blur="validateSequence"
                >
                  <template #append>
                    <el-button @click="getNextSequence" :loading="isGettingSequence" size="small">
                      获取
                    </el-button>
                  </template>
                </el-input>
                <div v-if="!canEditSequence" class="field-tip">
                  仅管理员可编辑
                </div>
                <div v-if="sequenceValidation.message"
                     :class="['validation-tip', sequenceValidation.is_valid ? 'success' : 'error']">
                  {{ sequenceValidation.message }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="年度" prop="year">
                <el-select v-model="form.year" placeholder="选择年度" style="width: 100%">
                  <el-option label="2023年" :value="2023" />
                  <el-option label="2024年" :value="2024" />
                  <el-option label="2025年" :value="2025" />
                  <el-option label="2026年" :value="2026" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="*状态" prop="status" required>
                <el-select v-model="form.status" placeholder="选择状态" style="width: 100%">
                  <el-option label="草稿" value="draft" />
                  <el-option label="进行中" value="in_progress" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已完成并持续推进" value="completed_ongoing" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="责任单位" prop="responsible_dept">
                <el-select
                  v-model="form.responsible_dept"
                  placeholder="选择责任单位"
                  style="width: 100%"
                  data-testid="responsible-dept-select"
                  filterable
                  clearable
                  :loading="departmentLoading"
                  @focus="loadDepartments"
                >
                  <el-option
                    v-for="dept in departments.reporting"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.name"
                    :data-testid="`dept-option-${dept.name}`"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="*填报单位或部门" prop="reporting_unit" required>
                <el-select
                  v-model="form.reporting_unit"
                  placeholder="选择填报单位或部门"
                  style="width: 100%"
                  data-testid="reporting-unit-select"
                  filterable
                  clearable
                  :loading="departmentLoading"
                  @focus="loadDepartments"
                >
                  <el-option
                    v-for="dept in departments.reporting"
                    :key="dept.id"
                    :label="dept.name"
                    :value="dept.name"
                    :data-testid="`reporting-unit-option-${dept.name}`"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 工作内容 -->
        <div class="form-section">
          <h3 class="section-title">📝 工作内容</h3>

          <el-form-item label="*重点工作事项" prop="work_item" required>
            <el-input
              v-model="form.work_item"
              placeholder="请输入重点工作事项"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="工作目标" prop="work_objectives">
            <el-input
              v-model="form.work_objectives"
              type="textarea"
              :rows="2"
              placeholder="请描述工作目标（选填）"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="工作措施或举措" prop="measures">
            <el-input
              v-model="form.measures"
              type="textarea"
              :rows="3"
              placeholder="请详细描述工作措施或举措"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="*工作进展" prop="progress" required>
            <el-input
              v-model="form.progress"
              type="textarea"
              :rows="3"
              placeholder="请详细描述当前工作进展"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="工作成效" prop="effectiveness">
            <el-input
              v-model="form.effectiveness"
              type="textarea"
              :rows="2"
              placeholder="请描述取得的工作成效（选填）"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="下一步措施或计划" prop="next_steps">
            <el-input
              v-model="form.next_steps"
              type="textarea"
              :rows="2"
              placeholder="请描述下一步措施或计划（选填）"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="遇到的困难或问题" prop="difficulties">
            <el-input
              v-model="form.difficulties"
              type="textarea"
              :rows="2"
              placeholder="请描述遇到的困难或问题（选填）"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>


        <!-- 关联信息 -->
        <div class="form-section">
          <h3 class="section-title">🔗 关联信息</h3>

          <el-form-item label="*对口部门" prop="counterpart_depts" required>
            <div class="dept-selection" data-testid="counterpart-depts-section">
              <div class="dept-group">
                <span class="dept-label">本单位：</span>
                <el-checkbox-group v-model="form.counterpart_depts" data-testid="local-depts-group">
                  <el-checkbox
                    v-for="dept in departments.local"
                    :key="dept.id"
                    :label="dept.name"
                    :data-testid="`local-dept-${dept.name}`"
                  >
                    {{ dept.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>

              <div class="dept-group">
                <span class="dept-label">省分公司：</span>
                <el-checkbox-group v-model="form.counterpart_depts" data-testid="provincial-depts-group">
                  <el-checkbox
                    v-for="dept in departments.provincial"
                    :key="dept.id"
                    :label="dept.name"
                    :data-testid="`provincial-dept-${dept.name}`"
                  >
                    {{ dept.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="*关键词勾选" prop="tags" required>
            <div class="tags-selection" data-testid="tags-section">
              <div class="tag-group">
                <span class="tag-label">工作类型：</span>
                <el-checkbox-group v-model="form.tags" data-testid="work-type-tags-group">
                  <el-checkbox
                    v-for="tag in tags.work_type"
                    :key="tag.id"
                    :label="tag.name"
                    :data-testid="`work-type-tag-${tag.name}`"
                  >
                    {{ tag.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>

              <div class="tag-group">
                <span class="tag-label">时间周期：</span>
                <el-checkbox-group v-model="form.tags" data-testid="time-cycle-tags-group">
                  <el-checkbox
                    v-for="tag in tags.time_cycle"
                    :key="tag.id"
                    :label="tag.name"
                    :data-testid="`time-cycle-tag-${tag.name}`"
                  >
                    {{ tag.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>

        </div>

        <!-- 监督管理 -->
        <div class="form-section">
          <h3 class="section-title">📋 监督管理</h3>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="问责情况" prop="accountability_status">
                <el-input
                  v-model="form.accountability_status"
                  placeholder="请描述问责情况（选填）"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="要求填报日期" prop="required_submit_date">
                <el-date-picker
                  v-model="form.required_submit_date"
                  type="date"
                  placeholder="选择要求填报日期"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="*填报（更新）日期" prop="updated_at">
                <el-input
                  :value="formatDate(new Date())"
                  disabled
                  placeholder="自动生成"
                  style="width: 100%"
                />
                <div style="font-size: 12px; color: #999; margin-top: 4px;">
                  此字段自动更新为当前日期
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="备注/对口部门批注" prop="remarks">
            <el-input
              v-model="form.remarks"
              placeholder="备注信息或对口部门批注（选填）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="handleReset">重置表单</el-button>
          <el-button type="warning" @click="handleSaveDraft" :loading="isDraftSaving">
            保存草稿
          </el-button>
          <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">
            {{ isEdit ? '更新台账' : '提交台账' }}
          </el-button>
          <el-button @click="handleAutoFill" :loading="isAutoFilling">
            智能填充
          </el-button>
        </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
// 图标已移除，使用简化版本
import { useLedgerStore } from '@/stores/counter'
import { ledgerApi } from '@/api/ledger'

const router = useRouter()
const route = useRoute()
const store = useLedgerStore()

// 响应式数据
const formRef = ref()
const isSubmitting = ref(false)
const isDraftSaving = ref(false)
const isGettingSequence = ref(false)
const isAutoFilling = ref(false)
const canEditSequence = ref(true)
const departmentLoading = ref(false)
const isEdit = computed(() => !!route.params.id)
const ledgerId = computed(() => route.params.id ? parseInt(route.params.id) : null)

// 用户权限信息
const userPermissions = reactive({
  can_edit_sequence: true,
  can_edit_all: true,
  department: '综合办',
  role: '管理员'
})

// 表单数据
const form = reactive({
  sequence_number: '',
  work_item: '',
  measures: '',
  progress: '',
  effectiveness: '',
  responsible_dept: '',
  reporting_unit: '',
  counterpart_depts: [],
  tags: [],
  work_objectives: '',
  next_steps: '',
  difficulties: '',
  accountability_status: '',
  required_submit_date: '',
  remarks: '',
  status: 'draft',
  year: 2025
})

// 序号验证状态
const sequenceValidation = reactive({
  is_valid: false,
  message: ''
})

// 基础数据
const departments = reactive({
  reporting: [],
  local: [],
  provincial: []
})

const tags = reactive({
  work_type: [
    { id: 1, name: '重点工作' },
    { id: 2, name: '大监督' },
    { id: 3, name: '专项工作' },
    { id: 4, name: '安全生产' },
    { id: 5, name: '质量管控' },
    { id: 6, name: '党建工作' },
    { id: 7, name: '业务发展' }
  ],
  time_cycle: [
    { id: 8, name: '季度报送' },
    { id: 9, name: '半年报送' },
    { id: 10, name: '年度报送' },
    { id: 11, name: '临时任务' },
    { id: 12, name: '长期跟踪' }
  ]
})

// 表单验证规则 - 优化为渐进式验证
const rules = {
  // 核心必填字段
  sequence_number: [
    { required: true, message: '请输入序号', trigger: 'blur' },
    { pattern: /^\d{1,3}$/, message: '序号必须是1-3位数字', trigger: 'blur' }
  ],
  work_item: [
    { required: true, message: '请输入工作事项', trigger: 'blur' },
    { min: 1, max: 500, message: '工作事项长度在1-500个字符', trigger: 'blur' }
  ],
  measures: [
    { required: true, message: '请输入具体措施', trigger: 'blur' },
    { min: 1, message: '请详细描述工作措施', trigger: 'blur' }
  ],
  progress: [
    { required: true, message: '请输入进展情况', trigger: 'blur' },
    { min: 1, message: '请详细描述工作进展', trigger: 'blur' }
  ],
  // 推荐填写字段 - 降低验证严格度
  responsible_dept: [
    { required: false, message: '建议选择责任单位', trigger: 'change' }
  ],
  reporting_unit: [
    { required: true, message: '请选择填报单位或部门', trigger: 'change' }
  ],
  counterpart_depts: [
    { type: 'array', required: false, message: '建议选择至少一个对口部门', trigger: 'change' }
  ],
  tags: [
    { type: 'array', required: false, message: '建议选择至少一个关键词标签', trigger: 'change' }
  ],
  year: [
    { required: true, message: '请选择年度', trigger: 'change' }
  ]
}

// 日期格式化函数
const formatDate = (date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 方法
const handleBack = () => {
  router.push('/ledgers')
}

// 加载部门数据
const loadDepartments = async () => {
  if (departmentLoading.value || departments.reporting.length > 0) return

  try {
    departmentLoading.value = true

    // 模拟API调用加载部门数据
    await new Promise(resolve => setTimeout(resolve, 500))

    // 填充部门数据
    departments.reporting = [
      { id: 1, name: '综合办' },
      { id: 2, name: '技术部' },
      { id: 3, name: '财务部' },
      { id: 4, name: '人力资源部' }
    ]

    departments.local = [
      { id: 5, name: '综合办(党委办)' },
      { id: 6, name: '人力资源部' },
      { id: 7, name: '财务部' }
    ]

    departments.provincial = [
      { id: 8, name: '省公司综合办' },
      { id: 9, name: '省公司人力资源部' },
      { id: 10, name: '省公司财务部' },
      { id: 11, name: '省公司业务部' }
    ]
  } catch (error) {
    ElMessage.error('加载部门数据失败')
  } finally {
    departmentLoading.value = false
  }
}

const getPermissionTagType = () => {
  if (userPermissions.role === '管理员') return 'danger'
  if (userPermissions.role === '主管') return 'warning'
  return 'info'
}

const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置表单吗？所有填写的内容将丢失。',
      '重置确认',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 重置表单数据
    Object.assign(form, {
      sequence_number: '',
      work_item: '',
      measures: '',
      progress: '',
      effectiveness: '',
      responsible_dept: '',
      counterpart_depts: [],
      tags: [],
      remarks: '',
      status: 'draft',
      year: 2025
    })

    // 重置验证状态
    sequenceValidation.is_valid = false
    sequenceValidation.message = ''

    // 重置表单验证
    if (formRef.value) {
      formRef.value.resetFields()
    }

    ElMessage.success('表单已重置！')
  } catch {
    // 用户取消重置
  }
}

const handleAutoFill = async () => {
  try {
    isAutoFilling.value = true

    // 模拟智能填充
    await new Promise(resolve => setTimeout(resolve, 1000))

    form.work_item = '示例工作事项：提升服务质量'
    form.measures = '1. 建立服务标准体系\n2. 加强员工培训\n3. 完善监督机制'
    form.progress = '已完成服务标准制定，正在进行员工培训，预计下月完成全部工作'
    form.effectiveness = '服务满意度提升15%，客户投诉减少30%'
    form.responsible_dept = departments.reporting[0]?.name || ''
    form.counterpart_depts = [departments.local[0]?.name || '']
    form.tags = ['重点工作', '季度报送']

    ElNotification({
      title: '智能填充完成',
      message: '已为您填充示例数据，请根据实际情况修改',
      type: 'success',
      duration: 3000
    })
  } catch (error) {
    ElMessage.error('智能填充失败')
  } finally {
    isAutoFilling.value = false
  }
}

const handlePreview = () => {
  ElMessageBox.alert(
    `序号：${form.sequence_number || '未填写'}\n工作事项：${form.work_item || '未填写'}\n具体措施：${form.measures || '未填写'}\n进展情况：${form.progress || '未填写'}`,
    '台账预览',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  )
}

const toggleTag = (tagName) => {
  const index = form.tags.indexOf(tagName)
  if (index > -1) {
    form.tags.splice(index, 1)
  } else {
    form.tags.push(tagName)
  }
}

// 删除不需要的富文本编辑器方法，使用Element Plus的textarea组件

const handleSubmit = async () => {
  try {
    // 优化的表单验证 - 只验证核心必填字段
    const coreFields = ['sequence_number', 'work_item', 'measures', 'progress', 'reporting_unit', 'year']
    let hasError = false
    let errorMessage = ''

    for (const field of coreFields) {
      if (!form[field] || (Array.isArray(form[field]) && form[field].length === 0)) {
        hasError = true
        const fieldNames = {
          sequence_number: '序号',
          work_item: '工作事项',
          measures: '工作措施',
          progress: '工作进展',
          reporting_unit: '填报单位',
          year: '年度'
        }
        errorMessage = `请填写${fieldNames[field]}`
        break
      }
    }

    if (hasError) {
      ElMessage.warning(errorMessage)
      return
    }

    isSubmitting.value = true

    // 准备提交数据
    const submitData = {
      sequence_number: form.sequence_number,
      work_item: form.work_item,
      measures: form.measures,
      progress: form.progress,
      effectiveness: form.effectiveness || '',
      responsible_dept: form.responsible_dept || form.reporting_unit, // 如果没选择责任单位，使用填报单位
      reporting_unit: form.reporting_unit,
      counterpart_depts: form.counterpart_depts.length > 0 ? form.counterpart_depts : [],
      tags: form.tags.length > 0 ? form.tags : ['其他'],
      remarks: form.remarks || '',
      status: form.status,
      year: form.year
    }

    if (isEdit.value) {
      // 更新台账
      await ledgerApi.update(ledgerId.value, submitData)
      ElMessage.success('台账更新成功')
    } else {
      // 创建台账
      await ledgerApi.create(submitData)
      ElMessage.success('台账创建成功')
    }

    // 返回列表页
    router.push('/ledgers')

  } catch (error) {
    console.error('提交失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else {
      ElMessage.error(error.message || '提交失败，请重试')
    }
  } finally {
    isSubmitting.value = false
  }
}

const handleSaveDraft = async () => {
  try {
    isDraftSaving.value = true

    // 准备草稿数据
    const draftData = {
      sequence_number: form.sequence_number || null,
      work_item: form.work_item || null,
      measures: form.measures || null,
      progress: form.progress || null,
      effectiveness: form.effectiveness || null,
      responsible_dept: form.responsible_dept || null,
      counterpart_depts: form.counterpart_depts.length > 0 ? form.counterpart_depts : null,
      tags: form.tags.length > 0 ? form.tags : null,
      remarks: form.remarks || null,
      year: form.year || null,
      draft_id: `draft_${Date.now()}`
    }

    // 调用保存草稿API
    const response = await fetch('/api/v1/ledgers/draft', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify(draftData)
    })

    if (response.ok) {
      const result = await response.json()
      ElMessage.success('草稿保存成功')
      console.log('草稿保存结果:', result)
    } else {
      throw new Error('保存草稿失败')
    }

  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败，请重试')
  } finally {
    isDraftSaving.value = false
  }
}

const validateSequence = async () => {
  if (!form.sequence_number || !form.year) {
    sequenceValidation.is_valid = false
    sequenceValidation.message = ''
    return
  }

  try {
    const response = await fetch(
      `/api/v1/ledgers/validate-sequence/${form.sequence_number}?year=${form.year}${isEdit.value ? `&exclude_id=${ledgerId.value}` : ''}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      }
    )

    if (response.ok) {
      const result = await response.json()
      sequenceValidation.is_valid = result.is_valid
      sequenceValidation.message = result.message
    } else {
      sequenceValidation.is_valid = false
      sequenceValidation.message = '验证失败'
    }
  } catch (error) {
    console.error('序号验证失败:', error)
    sequenceValidation.is_valid = false
    sequenceValidation.message = '验证失败'
  }
}

const getNextSequence = async () => {
  try {
    isGettingSequence.value = true

    const response = await fetch(
      `/api/v1/ledgers/next-sequence?year=${form.year}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      }
    )

    if (response.ok) {
      const result = await response.json()
      form.sequence_number = result.next_sequence
      ElMessage.success(`获取到下一个序号: ${result.next_sequence}`)

      // 自动验证新序号
      await validateSequence()
    } else {
      throw new Error('获取序号失败')
    }
  } catch (error) {
    console.error('获取下一个序号失败:', error)
    ElMessage.error('获取下一个序号失败，请重试')
  } finally {
    isGettingSequence.value = false
  }
}

// 数据加载方法
const loadUserPermissions = async () => {
  try {
    const response = await fetch('/api/v1/auth/permissions', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    if (response.ok) {
      const permissions = await response.json()
      Object.assign(userPermissions, permissions)
      canEditSequence.value = permissions.can_edit_sequence

      // 如果用户不能编辑序号，自动获取下一个序号
      if (!canEditSequence.value && !isEdit.value) {
        await getNextSequence()
      }
    }
  } catch (error) {
    console.error('加载用户权限失败:', error)
    ElMessage.error('加载用户权限失败')
  }
}

const loadBasicData = async () => {
  try {
    // 加载部门数据
    await store.fetchDepartments()

    // 安全访问store.departments，添加空值检查
    const storeDepartments = store.departments || {}

    departments.reporting = storeDepartments.reporting || [
      { id: 1, name: '综合办' },
      { id: 2, name: '人力资源部' },
      { id: 3, name: '财务部' },
      { id: 4, name: '业务部' }
    ]
    departments.local = storeDepartments.local || [
      { id: 5, name: '综合办' },
      { id: 6, name: '综合办(党委办)' },
      { id: 7, name: '人力资源部' },
      { id: 8, name: '财务部' }
    ]
    departments.provincial = storeDepartments.provincial || [
      { id: 9, name: '省公司综合办' },
      { id: 10, name: '省公司人力资源部' },
      { id: 11, name: '省公司财务部' },
      { id: 12, name: '省公司业务部' }
    ]

    // 标签数据已在reactive中定义
  } catch (error) {
    console.error('加载基础数据失败:', error)
    ElMessage.error('加载基础数据失败')
  }
}

const loadLedgerData = async () => {
  if (!isEdit.value || !ledgerId.value) {
    console.log('不是编辑模式或没有台账ID:', { isEdit: isEdit.value, ledgerId: ledgerId.value })
    return
  }

  try {
    console.log('开始加载台账数据，ID:', ledgerId.value)
    const ledger = await store.fetchLedgerById(ledgerId.value)
    console.log('获取到台账数据:', ledger)

    if (ledger) {
      // 填充表单数据
      form.sequence_number = ledger.sequence_number
      form.work_item = ledger.work_item
      form.measures = ledger.measures
      form.progress = ledger.progress
      form.effectiveness = ledger.effectiveness || ''
      form.responsible_dept = ledger.responsible_dept
      form.reporting_unit = ledger.reporting_unit || ''
      form.counterpart_depts = ledger.counterpart_depts || []
      form.tags = ledger.tags || []
      form.work_objectives = ledger.work_objectives || ''
      form.next_steps = ledger.next_steps || ''
      form.difficulties = ledger.difficulties || ''
      form.accountability_status = ledger.accountability_status || ''
      form.required_submit_date = ledger.required_submit_date || ''
      form.remarks = ledger.remarks || ''
      form.status = ledger.status
      form.year = ledger.year

      console.log('表单数据填充完成:', form)
    } else {
      console.error('未获取到台账数据')
    }
  } catch (error) {
    console.error('加载台账数据失败:', error)
    ElMessage.error('加载台账数据失败')
    router.push('/ledgers')
  }
}

// 初始化
onMounted(async () => {
  try {
    // 并行加载基础数据、用户权限和部门数据
    await Promise.all([
      loadBasicData(),
      loadUserPermissions(),
      loadDepartments()
    ])

    // 如果是编辑模式，加载台账数据
    if (isEdit.value) {
      await loadLedgerData()
    }

    console.log('台账录入页面初始化完成')
  } catch (error) {
    console.error('页面初始化失败:', error)
    ElMessage.error('页面初始化失败')
  }
})
</script>

<style scoped>
/* 页面布局 - 固定头部 + 可滚动内容 */
.ledger-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.page-header {
  flex-shrink: 0;
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  z-index: 1000;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.scrollable-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

.form-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-form {
  width: 100%;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 强制可见的基本信息分组 */
.basic-info {
  background: #e3f2fd !important;
  border: 2px solid #2196f3 !important;
  position: relative !important;
  z-index: 999 !important;
  margin-top: 0 !important;
}

.section-title {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #ddd;
}

.dept-selection,
.tags-selection {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dept-group,
.tag-group {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.dept-label,
.tag-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-top: 5px;
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.validation-tip {
  font-size: 12px;
  margin-top: 4px;
}

.validation-tip.success {
  color: #67c23a;
}

.validation-tip.error {
  color: #f56c6c;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #ddd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-content {
    margin: 10px;
    padding: 15px;
  }

  .form-section {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
  }
}

</style>
