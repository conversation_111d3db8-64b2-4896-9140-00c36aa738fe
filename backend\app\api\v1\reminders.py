"""
提醒系统API路由
"""
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...api.deps import get_current_user
from ...models.user import User
from ...services.reminder_service import ReminderService
from ...schemas.reminder import (
    ReminderRuleCreate, ReminderRuleUpdate, ReminderRuleResponse, ReminderRuleListResponse,
    ReminderCreate, ReminderUpdate, ReminderResponse, ReminderListResponse,
    NotificationResponse, NotificationListResponse, NotificationUpdate,
    ReminderStats, NotificationStats, BatchReminderOperation, BatchNotificationOperation
)

router = APIRouter(tags=["提醒系统"])


# ==================== 提醒规则管理 ====================

@router.post("/rules", response_model=ReminderRuleResponse, summary="创建提醒规则")
async def create_reminder_rule(
    rule_data: ReminderRuleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建提醒规则
    
    需要管理员或管理部门权限
    """
    if not (current_user.is_admin or current_user.role == "manager"):
        raise HTTPException(status_code=403, detail="需要管理员或管理部门权限")
    
    service = ReminderService(db)
    return service.create_reminder_rule(rule_data, current_user.id)


@router.get("/rules", response_model=ReminderRuleListResponse, summary="获取提醒规则列表")
async def get_reminder_rules(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提醒规则列表"""
    service = ReminderService(db)
    skip = (page - 1) * limit
    return service.get_reminder_rules(skip=skip, limit=limit, is_active=is_active)


@router.get("/rules/{rule_id}", response_model=ReminderRuleResponse, summary="获取提醒规则详情")
async def get_reminder_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提醒规则详情"""
    service = ReminderService(db)
    rule = service.get_reminder_rule(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="提醒规则不存在")
    return rule


@router.put("/rules/{rule_id}", response_model=ReminderRuleResponse, summary="更新提醒规则")
async def update_reminder_rule(
    rule_id: int,
    rule_data: ReminderRuleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新提醒规则
    
    需要管理员或管理部门权限
    """
    if not (current_user.is_admin or current_user.role == "manager"):
        raise HTTPException(status_code=403, detail="需要管理员或管理部门权限")
    
    service = ReminderService(db)
    rule = service.update_reminder_rule(rule_id, rule_data)
    if not rule:
        raise HTTPException(status_code=404, detail="提醒规则不存在")
    return rule


@router.delete("/rules/{rule_id}", summary="删除提醒规则")
async def delete_reminder_rule(
    rule_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除提醒规则
    
    需要管理员权限
    """
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    service = ReminderService(db)
    success = service.delete_reminder_rule(rule_id)
    if not success:
        raise HTTPException(status_code=404, detail="提醒规则不存在")
    
    return {"message": "提醒规则删除成功"}


# ==================== 提醒记录管理 ====================

@router.post("/", response_model=ReminderResponse, summary="创建提醒记录")
async def create_reminder(
    reminder_data: ReminderCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建提醒记录
    
    需要管理员或管理部门权限
    """
    if not (current_user.is_admin or current_user.role == "manager"):
        raise HTTPException(status_code=403, detail="需要管理员或管理部门权限")
    
    service = ReminderService(db)
    return service.create_reminder(reminder_data)


@router.get("/", response_model=ReminderListResponse, summary="获取提醒记录列表")
async def get_reminders(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    status: Optional[str] = Query(None, description="状态筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提醒记录列表"""
    service = ReminderService(db)
    skip = (page - 1) * limit
    
    # 普通用户只能查看自己的提醒
    user_id = None if (current_user.is_admin or current_user.role == "manager") else current_user.id
    
    return service.get_reminders(
        skip=skip, 
        limit=limit, 
        user_id=user_id,
        status=status,
        priority=priority
    )


@router.get("/my", response_model=ReminderListResponse, summary="获取我的提醒")
async def get_my_reminders(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的提醒记录"""
    service = ReminderService(db)
    skip = (page - 1) * limit
    return service.get_user_reminders(current_user.id, skip=skip, limit=limit)


@router.put("/{reminder_id}/read", summary="标记提醒为已读")
async def mark_reminder_read(
    reminder_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """标记提醒为已读"""
    service = ReminderService(db)
    success = service.mark_reminder_read(reminder_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="提醒不存在或无权限")
    
    return {"message": "提醒已标记为已读"}


@router.put("/{reminder_id}/dismiss", summary="忽略提醒")
async def dismiss_reminder(
    reminder_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """忽略提醒"""
    service = ReminderService(db)
    success = service.dismiss_reminder(reminder_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="提醒不存在或无权限")
    
    return {"message": "提醒已忽略"}


# ==================== 通知中心管理 ====================

@router.get("/notifications", response_model=NotificationListResponse, summary="获取通知列表")
async def get_notifications(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    unread_only: bool = Query(False, description="仅显示未读"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取当前用户的通知列表"""
    service = ReminderService(db)
    skip = (page - 1) * limit
    return service.get_user_notifications(
        current_user.id, 
        skip=skip, 
        limit=limit, 
        unread_only=unread_only
    )


@router.put("/notifications/{notification_id}/read", summary="标记通知为已读")
async def mark_notification_read(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """标记通知为已读"""
    service = ReminderService(db)
    success = service.mark_notification_read(notification_id, current_user.id)
    if not success:
        raise HTTPException(status_code=404, detail="通知不存在或无权限")
    
    return {"message": "通知已标记为已读"}


@router.put("/notifications/read-all", summary="标记所有通知为已读")
async def mark_all_notifications_read(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """标记所有通知为已读"""
    service = ReminderService(db)
    count = service.mark_all_notifications_read(current_user.id)
    return {"message": f"已标记 {count} 条通知为已读"}


# ==================== 自动提醒生成 ====================

@router.post("/generate/{ledger_id}", response_model=List[ReminderResponse], summary="为台账生成提醒")
async def generate_reminders_for_ledger(
    ledger_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    为指定台账生成提醒
    
    需要管理员或管理部门权限
    """
    if not (current_user.is_admin or current_user.role == "manager"):
        raise HTTPException(status_code=403, detail="需要管理员或管理部门权限")
    
    service = ReminderService(db)
    reminders = service.generate_reminders_for_ledger(ledger_id)
    return reminders


@router.post("/process-scheduled", summary="处理计划中的提醒")
async def process_scheduled_reminders(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    处理计划中的提醒
    
    需要管理员权限
    """
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    service = ReminderService(db)
    
    # 在后台处理
    def process_reminders():
        return service.process_scheduled_reminders()
    
    background_tasks.add_task(process_reminders)
    
    return {"message": "提醒处理任务已启动"}


# ==================== 统计功能 ====================

@router.get("/stats", response_model=ReminderStats, summary="获取提醒统计")
async def get_reminder_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取提醒统计信息"""
    service = ReminderService(db)
    
    # 普通用户只能查看自己的统计
    user_id = None if (current_user.is_admin or current_user.role == "manager") else current_user.id
    
    return service.get_reminder_stats(user_id=user_id)


@router.get("/notifications/stats", response_model=NotificationStats, summary="获取通知统计")
async def get_notification_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取通知统计信息"""
    service = ReminderService(db)
    return service.get_notification_stats(current_user.id)
