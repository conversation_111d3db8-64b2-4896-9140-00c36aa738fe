<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑部门' : '新增部门'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="部门名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入部门名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="部门类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择部门类型" style="width: 100%" :disabled="isEdit">
          <el-option label="填报单位" value="reporting" v-if="departmentType === 'reporting'" />
          <el-option label="本单位对口部门" value="local" v-if="departmentType === 'counterpart'" />
          <el-option label="省分公司对口部门" value="provincial" v-if="departmentType === 'counterpart'" />
        </el-select>
      </el-form-item>

      <el-form-item label="部门描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入部门描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="排序顺序" prop="sort_order">
        <el-input-number
          v-model="form.sort_order"
          :min="0"
          :max="9999"
          placeholder="排序顺序"
          style="width: 100%"
        />
        <div class="form-tip">数字越小排序越靠前</div>
      </el-form-item>

      <el-form-item label="上级部门" v-if="departmentType === 'counterpart'">
        <el-select v-model="form.parent_id" placeholder="请选择上级部门（可选）" style="width: 100%" clearable>
          <el-option
            v-for="dept in parentDepartments"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" v-if="isEdit">
        <el-radio-group v-model="form.status">
          <el-radio value="active">启用</el-radio>
          <el-radio value="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 预览信息 -->
      <el-card class="preview-card" shadow="never" v-if="form.name">
        <template #header>
          <span>预览信息</span>
        </template>
        
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">部门名称：</span>
            <span class="preview-value">{{ form.name }}</span>
          </div>
          
          <div class="preview-item">
            <span class="preview-label">部门类型：</span>
            <span class="preview-value">
              <el-tag :type="getTypeTag(form.type)">
                {{ getTypeText(form.type) }}
              </el-tag>
            </span>
          </div>
          
          <div class="preview-item" v-if="form.description">
            <span class="preview-label">描述：</span>
            <span class="preview-value">{{ form.description }}</span>
          </div>

          <div class="preview-item">
            <span class="preview-label">排序：</span>
            <span class="preview-value">{{ form.sort_order }}</span>
          </div>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="loading"
          :disabled="!form.name"
        >
          {{ loading ? '保存中...' : (isEdit ? '更新' : '创建') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  department: {
    type: Object,
    default: null
  },
  departmentType: {
    type: String,
    default: 'reporting' // reporting 或 counterpart
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const parentDepartments = ref([])

const form = reactive({
  name: '',
  type: '',
  description: '',
  sort_order: 0,
  parent_id: null,
  status: 'active'
})

const rules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 100, message: '部门名称长度为2-100个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择部门类型', trigger: 'change' }
  ],
  sort_order: [
    { required: true, message: '请输入排序顺序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序顺序范围为0-9999', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.department?.id)

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
    if (props.department) {
      Object.assign(form, {
        name: props.department.name || '',
        type: props.department.type || (props.departmentType === 'reporting' ? 'reporting' : 'local'),
        description: props.department.description || '',
        sort_order: props.department.sort_order || 0,
        parent_id: props.department.parent_id || null,
        status: props.department.status || 'active'
      })
    } else {
      form.type = props.departmentType === 'reporting' ? 'reporting' : 'local'
    }
    
    if (props.departmentType === 'counterpart') {
      loadParentDepartments()
    }
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const loadParentDepartments = async () => {
  try {
    const response = await fetch('/api/v1/departments/counterpart', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      parentDepartments.value = data.data || []
    }
  } catch (error) {
    console.error('获取上级部门列表失败:', error)
  }
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    type: props.departmentType === 'reporting' ? 'reporting' : 'local',
    description: '',
    sort_order: 0,
    parent_id: null,
    status: 'active'
  })
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const url = isEdit.value
      ? `/api/v1/department-management/${props.departmentType}/${props.department.id}`
      : `/api/v1/department-management/${props.departmentType}`
    
    const method = isEdit.value ? 'PUT' : 'POST'
    
    const requestData = {
      name: form.name,
      type: form.type,
      description: form.description || null,
      sort_order: form.sort_order,
      parent_id: form.parent_id || null
    }
    
    if (isEdit.value) {
      requestData.status = form.status
    }
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify(requestData)
    })
    
    if (response.ok) {
      const result = await response.json()
      
      ElMessage.success({
        message: isEdit.value ? '部门更新成功' : '部门创建成功',
        duration: 3000
      })
      
      emit('success', result)
      handleClose()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || (isEdit.value ? '部门更新失败' : '部门创建失败'))
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error(isEdit.value ? '部门更新失败' : '部门创建失败')
  } finally {
    loading.value = false
  }
}

const getTypeTag = (type) => {
  switch (type) {
    case 'reporting': return 'primary'
    case 'local': return 'success'
    case 'provincial': return 'warning'
    default: return 'info'
  }
}

const getTypeText = (type) => {
  switch (type) {
    case 'reporting': return '填报单位'
    case 'local': return '本单位对口部门'
    case 'provincial': return '省分公司对口部门'
    default: return '未知类型'
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.preview-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.preview-value {
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
