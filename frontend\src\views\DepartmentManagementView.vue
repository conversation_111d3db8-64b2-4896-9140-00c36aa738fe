<template>
  <div class="department-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-title">
        <el-button @click="goBack" style="margin-right: 12px;">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div>
          <h1>部门管理</h1>
          <p>管理填报单位、对口部门、用户归属和部门变更</p>
        </div>
      </div>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" class="management-tabs">
      <!-- 填报单位管理 -->
      <el-tab-pane label="填报单位管理" name="reporting">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>填报单位列表</span>
              <el-button type="primary" @click="showCreateDialog = true" v-if="authStore.user?.role === 'admin'">
                新增填报单位
              </el-button>
            </div>
          </template>

          <el-table :data="reportingDepartments" v-loading="loading" stripe>
            <el-table-column prop="name" label="部门名称" />
            <el-table-column prop="type" label="部门类型" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'info'">
                  {{ row.status === 'active' ? '活跃' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="user_count" label="用户数量" />
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="editDepartment(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteDepartment(row)" v-if="authStore.user?.role === 'admin'">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 对口部门管理 -->
      <el-tab-pane label="对口部门管理" name="counterpart">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>对口部门配置</span>
              <el-button type="primary" @click="showCounterpartDialog = true" v-if="authStore.user?.role === 'admin'">
                新增对口部门
              </el-button>
            </div>
          </template>

          <el-table :data="counterpartDepartments" v-loading="loading" stripe>
            <el-table-column prop="name" label="部门名称" />
            <el-table-column prop="dept_type" label="部门类型">
              <template #default="{ row }">
                <el-tag :type="row.dept_type === 'local' ? 'primary' : 'success'">
                  {{ row.dept_type === 'local' ? '本单位' : '省分公司' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="editCounterpart(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteCounterpart(row)" v-if="authStore.user?.role === 'admin'">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 部门统计 -->
      <el-tab-pane label="统计分析" name="stats">
        <el-card shadow="hover">
          <template #header>
            <span>部门统计信息</span>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalDepartments || 0 }}</div>
                <div class="stat-label">总部门数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.activeDepartments || 0 }}</div>
                <div class="stat-label">活跃部门</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalUsers || 0 }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-number">{{ stats.recentChanges || 0 }}</div>
                <div class="stat-label">近期变更</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建填报单位对话框 -->
    <el-dialog v-model="showCreateDialog" title="新增填报单位" width="500px">
      <el-form :model="createForm" label-width="120px">
        <el-form-item label="部门名称">
          <el-input v-model="createForm.name" />
        </el-form-item>
        <el-form-item label="部门类型">
          <el-select v-model="createForm.type">
            <el-option label="填报单位" value="reporting" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="createForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateDepartment" :loading="createLoading">创建</el-button>
      </template>
    </el-dialog>

    <!-- 创建对口部门对话框 -->
    <el-dialog v-model="showCounterpartDialog" title="新增对口部门" width="500px">
      <el-form :model="counterpartForm" label-width="120px">
        <el-form-item label="部门名称">
          <el-input v-model="counterpartForm.name" />
        </el-form-item>
        <el-form-item label="部门类型">
          <el-select v-model="counterpartForm.dept_type">
            <el-option label="本单位" value="local" />
            <el-option label="省分公司" value="provincial" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="counterpartForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCounterpartDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateCounterpart" :loading="createLoading">创建</el-button>
      </template>
    </el-dialog>

    <!-- 编辑填报单位对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑填报单位" width="500px">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="部门名称">
          <el-input v-model="editForm.name" />
        </el-form-item>
        <el-form-item label="部门类型">
          <el-select v-model="editForm.type" disabled>
            <el-option label="填报单位" value="reporting" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditDepartment" :loading="createLoading">更新</el-button>
      </template>
    </el-dialog>

    <!-- 编辑对口部门对话框 -->
    <el-dialog v-model="showEditCounterpartDialog" title="编辑对口部门" width="500px">
      <el-form :model="editCounterpartForm" label-width="120px">
        <el-form-item label="部门名称">
          <el-input v-model="editCounterpartForm.name" />
        </el-form-item>
        <el-form-item label="部门类型">
          <el-select v-model="editCounterpartForm.dept_type">
            <el-option label="本单位" value="local" />
            <el-option label="省分公司" value="provincial" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="editCounterpartForm.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditCounterpartDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditCounterpart" :loading="createLoading">更新</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import api from '../api'

// Store
const authStore = useAuthStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('reporting')
const reportingDepartments = ref([])
const counterpartDepartments = ref([])
const stats = ref({})
const loading = ref(false)
const createLoading = ref(false)

// 对话框状态
const showCreateDialog = ref(false)
const showCounterpartDialog = ref(false)
const showEditDialog = ref(false)
const showEditCounterpartDialog = ref(false)

// 表单数据
const createForm = reactive({
  name: '',
  type: 'reporting',
  description: ''
})

const counterpartForm = reactive({
  name: '',
  dept_type: 'local',
  description: ''
})

const editForm = reactive({
  id: null,
  name: '',
  type: 'reporting',
  description: ''
})

const editCounterpartForm = reactive({
  id: null,
  name: '',
  dept_type: 'local',
  description: ''
})

// 方法
const loadReportingDepartments = async () => {
  try {
    loading.value = true
    // 使用正确的API路径
    const response = await api.get('/department-management/reporting')

    if (response && response.data) {
      reportingDepartments.value = response.data || []
    } else if (Array.isArray(response)) {
      reportingDepartments.value = response
    } else {
      reportingDepartments.value = []
    }
  } catch (error) {
    console.error('获取填报单位列表失败:', error)
    if (error.message !== 'Token expired') {
      ElMessage.error('获取填报单位列表失败')
    }
    reportingDepartments.value = []
  } finally {
    loading.value = false
  }
}

const loadCounterpartDepartments = async () => {
  try {
    // 使用正确的API路径
    const response = await api.get('/department-management/counterpart')

    if (response && response.data) {
      counterpartDepartments.value = response.data || []
    } else if (Array.isArray(response)) {
      counterpartDepartments.value = response
    } else {
      counterpartDepartments.value = []
    }
  } catch (error) {
    console.error('获取对口部门列表失败:', error)
    if (error.message !== 'Token expired') {
      ElMessage.error('获取对口部门列表失败')
    }
    counterpartDepartments.value = []
  }
}

const loadStats = async () => {
  try {
    // 修复API路径
    const response = await api.get('/department-management/stats')

    if (Array.isArray(response)) {
      // 计算统计数据
      const totalDepartments = response.length
      const activeDepartments = response.filter(dept => dept.department_name).length
      const totalUsers = response.reduce((sum, dept) => sum + (dept.user_count || 0), 0)
      const recentChanges = response.filter(dept => {
        if (!dept.last_activity) return false
        const lastActivity = new Date(dept.last_activity)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        return lastActivity > thirtyDaysAgo
      }).length

      stats.value = {
        totalDepartments,
        activeDepartments,
        totalUsers,
        recentChanges
      }
    } else {
      // 如果API返回格式不同，设置默认值
      stats.value = {
        totalDepartments: reportingDepartments.value.length + counterpartDepartments.value.length,
        activeDepartments: reportingDepartments.value.filter(d => d.status === 'active').length +
                          counterpartDepartments.value.filter(d => d.status === 'active').length,
        totalUsers: 0,
        recentChanges: 0
      }
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    // 设置默认统计值
    stats.value = {
      totalDepartments: reportingDepartments.value.length + counterpartDepartments.value.length,
      activeDepartments: reportingDepartments.value.filter(d => d.status === 'active').length +
                        counterpartDepartments.value.filter(d => d.status === 'active').length,
      totalUsers: 0,
      recentChanges: 0
    }
  }
}

const handleCreateDepartment = async () => {
  try {
    createLoading.value = true

    // 使用正确的API路径
    await api.post('/department-management/reporting', createForm)

    ElMessage.success('部门创建成功')
    showCreateDialog.value = false
    Object.assign(createForm, { name: '', type: 'reporting', description: '' })
    loadReportingDepartments()
    loadStats()
  } catch (error) {
    console.error('部门创建失败:', error)
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法创建部门')
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || '部门创建失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    createLoading.value = false
  }
}

const handleCreateCounterpart = async () => {
  try {
    createLoading.value = true

    // 修复数据格式，将dept_type改为type
    const requestData = {
      name: counterpartForm.name,
      type: counterpartForm.dept_type, // 后端期望的是type字段
      description: counterpartForm.description
    }

    // 使用统一的api实例
    await api.post('/department-management/counterpart', requestData)

    ElMessage.success('对口部门创建成功')
    showCounterpartDialog.value = false
    Object.assign(counterpartForm, { name: '', dept_type: 'local', description: '' })
    loadCounterpartDepartments()
    loadStats()
  } catch (error) {
    console.error('对口部门创建失败:', error)
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法创建对口部门')
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || '对口部门创建失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    createLoading.value = false
  }
}

const editDepartment = (department) => {
  // 设置编辑表单数据
  Object.assign(editForm, {
    id: department.id,
    name: department.name,
    type: department.type,
    description: department.description || ''
  })
  showEditDialog.value = true
}

const deleteDepartment = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除填报单位"${department.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 使用正确的API路径
    await api.delete(`/department-management/reporting/${department.id}`)

    ElMessage.success('填报单位删除成功')
    loadReportingDepartments()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除填报单位失败:', error)
      if (error.response?.status === 403) {
        ElMessage.error('权限不足，无法删除填报单位')
      } else if (error.message !== 'Token expired') {
        const errorMessage = error.response?.data?.detail || '删除失败'
        ElMessage.error(errorMessage)
      }
    }
  }
}

const editCounterpart = (department) => {
  // 设置编辑表单数据
  Object.assign(editCounterpartForm, {
    id: department.id,
    name: department.name,
    dept_type: department.dept_type,
    description: department.description || ''
  })
  showEditCounterpartDialog.value = true
}

const deleteCounterpart = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除对口部门"${department.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 使用正确的API路径
    await api.delete(`/department-management/counterpart/${department.id}`)

    ElMessage.success('对口部门删除成功')
    loadCounterpartDepartments()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除对口部门失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleEditDepartment = async () => {
  try {
    createLoading.value = true

    // 使用正确的API路径
    await api.put(`/department-management/reporting/${editForm.id}`, {
      name: editForm.name,
      description: editForm.description
    })

    ElMessage.success('填报单位更新成功')
    showEditDialog.value = false
    loadReportingDepartments()
    loadStats()
  } catch (error) {
    console.error('更新填报单位失败:', error)
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法更新填报单位')
    } else if (error.message !== 'Token expired') {
      const errorMessage = error.response?.data?.detail || '更新失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    createLoading.value = false
  }
}

const handleEditCounterpart = async () => {
  try {
    createLoading.value = true
    // 使用正确的API路径和数据格式
    await api.put(`/department-management/counterpart/${editCounterpartForm.id}`, {
      name: editCounterpartForm.name,
      type: editCounterpartForm.dept_type, // 后端期望的是type字段
      description: editCounterpartForm.description
    })

    ElMessage.success('对口部门更新成功')
    showEditCounterpartDialog.value = false
    loadCounterpartDepartments()
    loadStats()
  } catch (error) {
    console.error('更新对口部门失败:', error)
    ElMessage.error('更新失败')
  } finally {
    createLoading.value = false
  }
}

// 返回函数
const goBack = () => {
  router.back()
}

// 生命周期
onMounted(() => {
  loadReportingDepartments()
  loadCounterpartDepartments()
  loadStats()
})
</script>

<style scoped>
.department-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-title {
  display: flex;
  align-items: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.management-tabs {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
