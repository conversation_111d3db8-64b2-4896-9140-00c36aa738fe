"""
部门管理API路由
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session

from ...api.deps import get_db, get_current_user
from ...models.user import User
from ...services.department_management_service import DepartmentManagementService
from ...services.department_change_service import DepartmentChangeService
from ...services.user_department_service import UserDepartmentService
from ...schemas.department_management import (
    DepartmentCreate, DepartmentUpdate, DepartmentResponse, DepartmentListResponse,
    DepartmentChangeResponse, UserTransferRequest, BatchUserTransferRequest,
    UserTransferResponse, DepartmentMergeRequest, DepartmentSplitRequest,
    ChangeImpactAnalysis, DepartmentStats, BatchOperationResult, MessageResponse
)

router = APIRouter(tags=["部门管理"])


def verify_admin_permission(current_user: User):
    """验证管理员权限"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )


# 填报单位管理API
@router.get("/reporting", response_model=DepartmentListResponse, summary="获取填报单位列表")
async def get_reporting_departments(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取填报单位列表"""
    service = DepartmentManagementService(db)
    result = service.get_departments(dept_type="reporting", status=status, skip=skip, limit=limit)
    return DepartmentListResponse(**result)


@router.post("/reporting", response_model=DepartmentResponse, summary="创建填报单位")
async def create_reporting_department(
    data: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建填报单位"""
    verify_admin_permission(current_user)
    
    # 强制设置为填报单位类型
    data.type = "reporting"
    
    service = DepartmentManagementService(db)
    try:
        department = service.create_department(data, current_user.id)
        return DepartmentResponse.from_orm(department)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/reporting/{dept_id}", response_model=DepartmentResponse, summary="获取填报单位详情")
async def get_reporting_department(
    dept_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取填报单位详情"""
    service = DepartmentManagementService(db)
    department = service.get_department_by_id(dept_id)
    
    if not department or department.type != "reporting":
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="填报单位不存在")
    
    return DepartmentResponse.from_orm(department)


@router.put("/reporting/{dept_id}", response_model=DepartmentResponse, summary="更新填报单位")
async def update_reporting_department(
    dept_id: int,
    data: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新填报单位"""
    verify_admin_permission(current_user)
    
    service = DepartmentManagementService(db)
    try:
        department = service.update_department(dept_id, data, current_user.id)
        return DepartmentResponse.from_orm(department)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/reporting/{dept_id}", response_model=MessageResponse, summary="删除填报单位")
async def delete_reporting_department(
    dept_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除填报单位"""
    verify_admin_permission(current_user)
    
    service = DepartmentManagementService(db)
    try:
        success = service.delete_department(dept_id, current_user.id)
        if success:
            return MessageResponse(message="填报单位删除成功")
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="删除失败")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# 对口部门管理API
@router.get("/counterpart", response_model=DepartmentListResponse, summary="获取对口部门列表")
async def get_counterpart_departments(
    dept_type: Optional[str] = Query(None, description="部门类型: local/provincial"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取对口部门列表"""
    # 限制类型为local或provincial
    if dept_type and dept_type not in ["local", "provincial"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无效的部门类型")
    
    service = DepartmentManagementService(db)
    result = service.get_departments(dept_type=dept_type, skip=skip, limit=limit)
    
    # 过滤掉reporting类型
    if not dept_type:
        result["data"] = [d for d in result["data"] if d.type in ["local", "provincial"]]
        result["total"] = len(result["data"])
    
    return DepartmentListResponse(**result)


@router.post("/counterpart", response_model=DepartmentResponse, summary="创建对口部门")
async def create_counterpart_department(
    data: DepartmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建对口部门"""
    verify_admin_permission(current_user)

    # 验证类型
    if data.type not in ["local", "provincial"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="对口部门类型必须是local或provincial")

    service = DepartmentManagementService(db)
    try:
        department = service.create_department(data, current_user.id)
        return DepartmentResponse.from_orm(department)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/counterpart/{dept_id}", response_model=DepartmentResponse, summary="更新对口部门")
async def update_counterpart_department(
    dept_id: int,
    data: DepartmentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新对口部门"""
    verify_admin_permission(current_user)

    # 验证类型
    if data.type and data.type not in ["local", "provincial"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="对口部门类型必须是local或provincial")

    service = DepartmentManagementService(db)
    try:
        department = service.update_department(dept_id, data, current_user.id)
        return DepartmentResponse.from_orm(department)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/counterpart/{dept_id}", response_model=MessageResponse, summary="删除对口部门")
async def delete_counterpart_department(
    dept_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除对口部门"""
    verify_admin_permission(current_user)

    service = DepartmentManagementService(db)
    try:
        success = service.delete_department(dept_id, current_user.id)
        if success:
            return MessageResponse(message="对口部门删除成功")
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="删除失败")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# 部门变更管理API
@router.get("/changes", response_model=Dict[str, Any], summary="获取部门变更历史")
async def get_department_changes(
    department_id: Optional[int] = Query(None, description="部门ID筛选"),
    change_type: Optional[str] = Query(None, description="变更类型筛选"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取部门变更历史"""
    service = DepartmentChangeService(db)
    return service.get_change_history(department_id, change_type, skip, limit)


@router.post("/changes/analyze", response_model=ChangeImpactAnalysis, summary="分析部门变更影响")
async def analyze_department_change_impact(
    dept_id: int,
    changes: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """分析部门变更影响"""
    verify_admin_permission(current_user)
    
    service = DepartmentManagementService(db)
    try:
        return service.analyze_change_impact(dept_id, changes)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/merge", response_model=DepartmentResponse, summary="合并部门")
async def merge_departments(
    request: DepartmentMergeRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """合并部门"""
    verify_admin_permission(current_user)
    
    service = DepartmentChangeService(db)
    try:
        department = service.merge_departments(request, current_user.id)
        return DepartmentResponse.from_orm(department)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/split", response_model=List[DepartmentResponse], summary="拆分部门")
async def split_department(
    request: DepartmentSplitRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """拆分部门"""
    verify_admin_permission(current_user)
    
    service = DepartmentChangeService(db)
    try:
        departments = service.split_department(request, current_user.id)
        return [DepartmentResponse.from_orm(dept) for dept in departments]
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


# 用户归属管理API
@router.get("/{dept_id}/users", response_model=List[Dict[str, Any]], summary="获取部门用户")
async def get_department_users(
    dept_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取部门用户列表"""
    service = UserDepartmentService(db)
    try:
        return service.get_department_users(dept_id)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.post("/users/transfer", response_model=UserTransferResponse, summary="用户调动")
async def transfer_user(
    request: UserTransferRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """用户调动"""
    verify_admin_permission(current_user)
    
    service = UserDepartmentService(db)
    try:
        return service.transfer_user(request, current_user.id)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/users/batch-transfer", response_model=BatchOperationResult, summary="批量用户调动")
async def batch_transfer_users(
    request: BatchUserTransferRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量用户调动"""
    verify_admin_permission(current_user)
    
    service = UserDepartmentService(db)
    try:
        return service.batch_transfer_users(request, current_user.id)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/users/history", response_model=Dict[str, Any], summary="获取用户调动历史")
async def get_user_transfer_history(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    department_id: Optional[int] = Query(None, description="部门ID筛选"),
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户调动历史"""
    service = UserDepartmentService(db)
    return service.get_user_department_history(user_id, department_id, skip, limit)


# 部门统计API
@router.get("/stats", response_model=List[DepartmentStats], summary="获取所有部门统计")
async def get_all_departments_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有部门统计信息"""
    # 返回模拟的部门统计数据
    return [
        DepartmentStats(
            department_id=1,
            department_name="综合办",
            user_count=5,
            active_user_count=5,
            ledger_count=12,
            completed_ledger_count=8,
            completion_rate=66.7,
            last_activity=None
        ),
        DepartmentStats(
            department_id=2,
            department_name="技术部",
            user_count=8,
            active_user_count=7,
            ledger_count=15,
            completed_ledger_count=12,
            completion_rate=80.0,
            last_activity=None
        ),
        DepartmentStats(
            department_id=3,
            department_name="财务部",
            user_count=3,
            active_user_count=3,
            ledger_count=8,
            completed_ledger_count=6,
            completion_rate=75.0,
            last_activity=None
        )
    ]


@router.get("/{dept_id}/stats", response_model=DepartmentStats, summary="获取部门统计")
async def get_department_stats(
    dept_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个部门统计信息"""
    # 返回模拟的单个部门统计数据
    dept_stats_map = {
        1: DepartmentStats(
            department_id=1,
            department_name="综合办",
            user_count=5,
            active_user_count=5,
            ledger_count=12,
            completed_ledger_count=8,
            completion_rate=66.7,
            last_activity=None
        ),
        2: DepartmentStats(
            department_id=2,
            department_name="技术部",
            user_count=8,
            active_user_count=7,
            ledger_count=15,
            completed_ledger_count=12,
            completion_rate=80.0,
            last_activity=None
        ),
        3: DepartmentStats(
            department_id=3,
            department_name="财务部",
            user_count=3,
            active_user_count=3,
            ledger_count=8,
            completed_ledger_count=6,
            completion_rate=75.0,
            last_activity=None
        )
    }

    if dept_id not in dept_stats_map:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="部门不存在")

    return dept_stats_map[dept_id]
