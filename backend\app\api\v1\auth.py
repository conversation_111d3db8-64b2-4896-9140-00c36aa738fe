"""
认证授权API路由
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from ...api.deps import get_db, get_current_user, require_admin, require_permission
from ...core.auth import (
    authenticate_user, create_access_token, create_user_session,
    invalidate_user_session, update_user_login_info, check_user_permission,
    get_password_hash
)
from ...core.config import settings
from ...core.security import get_client_ip, log_security_event, SecurityEventType
from ...models.user import User, UserLoginLog, get_user_permissions, ROLE_PERMISSIONS
from ...schemas.auth import (
    LoginRequest, LoginResponse, UserResponse, MessageResponse,
    PermissionCheckRequest, PermissionCheckResponse,
    ActionValidationRequest, ActionValidationResponse,
    PermissionMatrix, RolePermissions, UserPermissions
)

router = APIRouter()
security = HTTPBearer()


@router.post("/login", summary="用户登录")
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """
    用户登录接口 - 超简化版
    """
    try:
        # 直接查询用户
        from ...core.auth import verify_password

        user = db.query(User).filter(User.username == login_data.username).first()

        if not user or not verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        # 创建简单的访问令牌
        access_token = create_access_token(
            data={"sub": str(user.id), "username": user.username, "role": user.role}
        )

        # 构建最简响应，避免日期字段问题
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.access_token_expire_minutes * 60,
            "user": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email or "",
                "phone": user.phone or "",
                "department_id": user.department_id,
                "role": user.role,
                "is_active": user.is_active,
                "login_count": user.login_count or 0
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"登录错误: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/logout", response_model=MessageResponse, summary="用户登出")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    用户登出接口
    """
    # 获取当前令牌
    authorization = request.headers.get("Authorization")
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        # 使令牌失效
        invalidate_user_session(db, token)
    
    # 记录登出事件
    log_security_event(
        SecurityEventType.LOGOUT,
        current_user.id,
        {"username": current_user.username},
        get_client_ip(request)
    )
    
    return MessageResponse(message="登出成功")


@router.get("/me", summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息 - 简化版，避免日期序列化问题
    """
    return {
        "id": current_user.id,
        "username": current_user.username,
        "full_name": current_user.full_name,
        "email": current_user.email or "",
        "phone": current_user.phone or "",
        "department_id": current_user.department_id,
        "department": current_user.department.name if current_user.department else None,
        "role": current_user.role,
        "is_active": current_user.is_active,
        "login_count": current_user.login_count or 0
    }


@router.get("/permissions", response_model=UserPermissions, summary="获取用户权限")
async def get_user_permissions_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取用户权限信息
    """
    permissions = get_user_permissions(current_user.role)
    
    return UserPermissions(
        user_id=current_user.id,
        role=current_user.role,
        permissions=permissions,
        can_edit_sequence=current_user.can_edit_sequence,
        can_edit_all_ledgers=current_user.role == "admin",
        can_manage_users=current_user.can_manage_users,
        can_manage_tags=current_user.can_manage_tags,
        can_export_data=current_user.can_export_data
    )


@router.post("/check-permission", response_model=PermissionCheckResponse, summary="检查用户权限")
async def check_permission(
    permission_request: PermissionCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """
    检查用户是否拥有指定权限
    """
    has_permission = check_user_permission(current_user, permission_request.permission)
    
    return PermissionCheckResponse(
        has_permission=has_permission,
        reason=None if has_permission else f"用户角色 '{current_user.role}' 缺少权限 '{permission_request.permission}'"
    )


@router.post("/validate-action", response_model=ActionValidationResponse, summary="验证操作权限")
async def validate_action(
    action_request: ActionValidationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    验证用户是否可以执行指定操作
    """
    allowed = True
    reason = None
    warnings = []
    
    # 根据操作类型和资源类型进行权限验证
    if action_request.action == "edit" and action_request.resource_type == "ledger":
        if current_user.role == "admin":
            allowed = True
        elif action_request.resource_id:
            # 检查是否为本部门数据
            from ...models.ledger import Ledger
            ledger = db.query(Ledger).filter(Ledger.id == action_request.resource_id).first()
            if ledger and ledger.responsible_dept != current_user.department.name:
                allowed = False
                reason = "只能编辑本部门的台账数据"
    
    elif action_request.action == "delete" and action_request.resource_type == "ledger":
        if current_user.role != "admin":
            allowed = False
            reason = "只有管理员可以删除台账"
    
    elif action_request.action == "manage" and action_request.resource_type == "tags":
        if not current_user.can_manage_tags:
            allowed = False
            reason = "需要管理部门或管理员权限才能管理标签"
    
    elif action_request.action == "manage" and action_request.resource_type == "users":
        if not current_user.can_manage_users:
            allowed = False
            reason = "只有管理员可以管理用户"
    
    return ActionValidationResponse(
        allowed=allowed,
        reason=reason,
        warnings=warnings
    )


@router.get("/permission-matrix", response_model=PermissionMatrix, summary="获取权限矩阵")
async def get_permission_matrix():
    """
    获取系统权限矩阵
    """
    modules = [
        {
            "name": "序号编辑",
            "permissions": {
                "admin": True,
                "manager": False,
                "user": False
            },
            "description": "仅主管可编辑台账序号"
        },
        {
            "name": "台账录入",
            "permissions": {
                "admin": True,
                "manager": True,
                "user": True
            },
            "description": "所有用户都可录入本部门台账"
        },
        {
            "name": "台账查看",
            "permissions": {
                "admin": True,
                "manager": True,
                "user": True
            },
            "description": "查看权限根据标签和部门限制"
        },
        {
            "name": "批注功能",
            "permissions": {
                "admin": True,
                "manager": True,
                "user": False
            },
            "description": "管理部门可批注，不能修改其他内容"
        },
        {
            "name": "导出功能",
            "permissions": {
                "admin": True,
                "manager": True,
                "user": False
            },
            "description": "管理部门可导出相关标签内容"
        },
        {
            "name": "标签管理",
            "permissions": {
                "admin": True,
                "manager": True,
                "user": False
            },
            "description": "管理部门可增删改标签，基层只能勾选"
        },
        {
            "name": "用户管理",
            "permissions": {
                "admin": True,
                "manager": False,
                "user": False
            },
            "description": "仅主管可管理用户和权限"
        },
        {
            "name": "部门管理",
            "permissions": {
                "admin": True,
                "manager": False,
                "user": False
            },
            "description": "仅主管可修改部门名称和架构"
        }
    ]
    
    return PermissionMatrix(modules=modules)


@router.get("/role-permissions/{role}", response_model=RolePermissions, summary="获取角色权限")
async def get_role_permissions(role: str):
    """
    获取指定角色的权限列表
    """
    if role not in ROLE_PERMISSIONS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"角色 '{role}' 不存在"
        )

    role_descriptions = {
        "admin": "系统管理员，拥有所有权限",
        "manager": "管理部门，可管理标签和批注",
        "user": "基层用户，只能操作本部门数据"
    }

    return RolePermissions(
        role=role,
        permissions=ROLE_PERMISSIONS[role],
        description=role_descriptions.get(role, "")
    )


# 添加用户管理相关的API端点，以兼容前端调用
@router.get("/users", summary="获取用户列表 (兼容路由)")
async def get_users_compat(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    department: Optional[str] = Query(None, description="部门筛选"),
    role: Optional[str] = Query(None, description="角色筛选"),
    is_active: Optional[bool] = Query(None, description="状态筛选"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户列表 - 兼容前端调用的路由
    重定向到实际的用户管理API
    """
    # 检查权限
    if current_user.role not in ['admin', 'manager']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员或管理部门权限"
        )

    # 构建查询
    query = db.query(User)

    # 搜索条件
    if search:
        from sqlalchemy import or_
        search_filter = or_(
            User.username.contains(search),
            User.full_name.contains(search),
            User.email.contains(search)
        )
        query = query.filter(search_filter)

    # 筛选条件
    if department:
        query = query.filter(User.department_id == department)

    if role:
        query = query.filter(User.role == role)

    if is_active is not None:
        query = query.filter(User.is_active == is_active)

    # 总数统计
    total = query.count()

    # 分页
    offset = (page - 1) * limit
    users = query.offset(offset).limit(limit).all()

    # 构建响应数据
    user_responses = []
    for user in users:
        user_responses.append({
            "id": user.id,
            "username": user.username,
            "full_name": user.full_name,
            "email": user.email,
            "phone": user.phone,
            "department_id": user.department_id,
            "department": user.department.name if user.department else None,
            "role": user.role,
            "is_active": user.is_active,
            "last_login": user.last_login,
            "login_count": user.login_count,
            "created_at": user.created_at,
            "updated_at": user.updated_at
        })

    pagination = {
        "page": page,
        "limit": limit,
        "total": total,
        "pages": (total + limit - 1) // limit
    }

    # 返回与前端期望格式一致的数据
    return {
        "data": user_responses,  # 前端期望的是 data 字段
        "users": user_responses,  # 同时保留 users 字段以兼容
        "pagination": pagination
    }


@router.get("/stats", summary="获取统计信息")
async def get_auth_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取认证相关的统计信息
    """
    # 只有管理员可以查看统计信息
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )

    # 统计用户数量
    total_users = db.query(User).count()
    active_users = db.query(User).filter(User.is_active == True).count()
    admin_users = db.query(User).filter(User.role == "admin").count()
    manager_users = db.query(User).filter(User.role == "manager").count()
    regular_users = db.query(User).filter(User.role == "user").count()

    return {
        "total_users": total_users,
        "active_users": active_users,
        "inactive_users": total_users - active_users,
        "admin_users": admin_users,
        "manager_users": manager_users,
        "regular_users": regular_users
    }


@router.post("/register", summary="注册新用户")
async def register_user(
    user_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    注册新用户（仅管理员可操作）
    """
    # 只有管理员可以注册新用户
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )

    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == user_data["username"]).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    if user_data.get("email"):
        existing_email = db.query(User).filter(User.email == user_data["email"]).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

    # 创建新用户
    hashed_password = get_password_hash(user_data["password"])
    new_user = User(
        username=user_data["username"],
        password_hash=hashed_password,
        full_name=user_data.get("real_name", user_data["username"]),
        email=user_data.get("email"),
        role=user_data.get("role", "user"),
        is_active=user_data.get("is_active", True)
    )

    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    return {
        "id": new_user.id,
        "username": new_user.username,
        "full_name": new_user.full_name,
        "email": new_user.email,
        "role": new_user.role,
        "is_active": new_user.is_active,
        "message": "用户创建成功"
    }
