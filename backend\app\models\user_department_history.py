"""
用户部门历史数据模型
"""
from sqlalchemy import Column, Integer, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from .base import Base


class UserDepartmentHistory(Base):
    """用户部门历史表模型"""

    __tablename__ = "user_department_history"

    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # 关联字段
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    old_department_id = Column(Integer, ForeignKey("departments.id"), nullable=True, comment="原部门ID")
    new_department_id = Column(Integer, ForeignKey("departments.id"), nullable=True, comment="新部门ID")
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="操作人ID")

    # 变更信息
    change_reason = Column(Text, nullable=True, comment="调动原因")

    # 时间戳
    effective_date = Column(DateTime(timezone=True), server_default=func.now(), comment="生效时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True, comment="创建时间")

    # 关联关系
    user = relationship("User", foreign_keys=[user_id], back_populates="department_history")
    old_department = relationship("Department", foreign_keys=[old_department_id])
    new_department = relationship("Department", foreign_keys=[new_department_id])
    operator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<UserDepartmentHistory(id={self.id}, user_id={self.user_id}, old_dept={self.old_department_id}, new_dept={self.new_department_id})>"

    @property
    def transfer_summary(self) -> str:
        """调动摘要"""
        old_name = self.old_department.name if self.old_department else "无"
        new_name = self.new_department.name if self.new_department else "无"
        return f"{old_name} → {new_name}"

    @property
    def is_new_employee(self) -> bool:
        """是否为新员工入职"""
        return self.old_department_id is None

    @property
    def is_departure(self) -> bool:
        """是否为离职"""
        return self.new_department_id is None
