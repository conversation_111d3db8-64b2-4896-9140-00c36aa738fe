# 台账管理系统 - 后端API

## 项目概述
台账管理系统后端API，基于FastAPI框架开发，提供台账查看、录入、筛选、导出等核心功能。

**当前状态**: 核心功能已完成，可投入使用
**完成模块**: 台账查看模块、台账录入模块
**API端口**: 8002 (避免与其他服务冲突)

## 技术栈
- **框架**: FastAPI 0.104.1
- **数据库**: SQLite3
- **ORM**: SQLAlchemy 2.0
- **数据验证**: Pydantic v2
- **部署**: Uvicorn

## 快速开始

### 1. 环境准备
```bash
# 确保Python版本 >= 3.9
python --version

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 根据需要修改配置
# 开发环境可以使用默认配置
```

### 4. 初始化数据库
```bash
# 创建数据库表并插入示例数据
python init_db.py
```

### 5. 启动应用
```bash
# 方式1：使用启动脚本（推荐）
python start.py

# 方式2：直接使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8002
```

### 6. 访问应用
- **API文档**: http://localhost:8002/docs
- **ReDoc文档**: http://localhost:8002/redoc
- **健康检查**: http://localhost:8002/health

## API接口说明

### 台账管理 ✅ 已完成
- `GET /api/v1/ledgers/` - 获取台账列表（支持筛选分页）
- `GET /api/v1/ledgers/{id}` - 获取台账详情
- `POST /api/v1/ledgers/` - 创建新台账
- `PUT /api/v1/ledgers/{id}` - 更新台账
- `DELETE /api/v1/ledgers/{id}` - 删除台账
- `GET /api/v1/ledgers/export` - 导出台账Excel
- `GET /api/v1/ledgers/next-sequence` - 获取下一个序号
- `POST /api/v1/ledgers/validate-sequence` - 验证序号

### 部门管理 ✅ 已完成
- `GET /api/v1/departments/` - 获取部门列表

### 标签管理 ✅ 已完成
- `GET /api/v1/tags/` - 获取标签列表

### 用户管理 ✅ 已完成
- `GET /api/v1/users/current` - 获取当前用户信息

## 项目结构
```
backend/
├── app/                    # 应用主目录
│   ├── api/               # API路由
│   ├── core/              # 核心配置
│   ├── models/            # 数据库模型
│   ├── schemas/           # Pydantic模型
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── init_db.py            # 数据库初始化
├── start.py              # 启动脚本
└── requirements.txt      # 依赖列表
```

## 开发指南

### 运行测试
```bash
# 安装测试依赖
pip install pytest pytest-asyncio httpx

# 运行测试
pytest tests/
```

### 代码格式化
```bash
# 安装格式化工具
pip install black isort flake8

# 格式化代码
black app/
isort app/

# 检查代码质量
flake8 app/
```

### 数据库操作
```bash
# 重新初始化数据库（会清空现有数据）
rm ledger.db
python init_db.py
```

## 配置说明

### 环境变量
- `APP_NAME`: 应用名称
- `DEBUG`: 调试模式
- `DATABASE_URL`: 数据库连接URL
- `SECRET_KEY`: 安全密钥
- `ALLOWED_ORIGINS`: 允许的跨域来源

### 数据库配置
默认使用SQLite数据库，数据库文件为 `ledger.db`。
生产环境建议使用PostgreSQL或MySQL。

## 部署说明

### 开发环境
```bash
python start.py
```

### 生产环境
```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 或使用Docker部署
# TODO: 添加Dockerfile
```

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查数据库文件权限
   - 确认SQLite3已安装

2. **端口占用**
   - 修改启动端口：`uvicorn app.main:app --port 8001`

3. **依赖安装失败**
   - 升级pip：`pip install --upgrade pip`
   - 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

### 日志查看
应用日志会输出到控制台，包含：
- API访问日志
- 数据库操作日志
- 错误信息

## 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证
MIT License
