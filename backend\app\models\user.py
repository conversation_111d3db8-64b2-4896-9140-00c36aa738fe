"""
用户相关数据模型
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from .base import Base


class User(Base):
    """用户模型"""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    full_name = Column(String(100), nullable=False, comment="真实姓名")
    email = Column(String(100), nullable=True, comment="邮箱")
    phone = Column(String(20), nullable=True, comment="手机号")

    # 部门关联
    department_id = Column(Integer, ForeignKey("departments.id"), nullable=True, comment="所属部门ID")
    department = relationship("Department", foreign_keys=[department_id], back_populates="users")

    # 角色和权限
    role = Column(String(20), default="user", nullable=False, comment="用户角色: admin/manager/user")
    is_active = Column(<PERSON><PERSON>an, default=True, nullable=False, comment="是否启用")

    # 登录信息
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    login_count = Column(Integer, default=0, comment="登录次数")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关联关系
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    created_years = relationship("Year", back_populates="creator", foreign_keys="Year.created_by")
    created_backups = relationship("Backup", back_populates="creator", foreign_keys="Backup.created_by")
    created_archives = relationship("Archive", back_populates="creator", foreign_keys="Archive.created_by")
    department_history = relationship("UserDepartmentHistory", foreign_keys="UserDepartmentHistory.user_id", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"

    @property
    def is_admin(self) -> bool:
        """是否为管理员"""
        return self.role == "admin"

    @property
    def is_manager(self) -> bool:
        """是否为管理部门"""
        return self.role == "manager"

    @property
    def is_user(self) -> bool:
        """是否为基层用户"""
        return self.role == "user"

    @property
    def can_manage_users(self) -> bool:
        """是否可以管理用户"""
        return self.role == "admin"

    @property
    def can_manage_tags(self) -> bool:
        """是否可以管理标签"""
        return self.role in ["admin", "manager"]

    @property
    def can_edit_sequence(self) -> bool:
        """是否可以编辑序号"""
        return self.role == "admin"

    @property
    def can_export_data(self) -> bool:
        """是否可以导出数据"""
        return self.role in ["admin", "manager"]


class UserSession(Base):
    """用户会话模型"""

    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    token_hash = Column(String(255), nullable=False, index=True, comment="Token哈希")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否有效")

    # 会话信息
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    last_used = Column(DateTime, default=datetime.utcnow, nullable=False, comment="最后使用时间")

    # 关联关系
    user = relationship("User", back_populates="sessions")

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, expires_at='{self.expires_at}')>"

    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        return datetime.utcnow() > self.expires_at


class UserLoginLog(Base):
    """用户登录日志模型"""

    __tablename__ = "user_login_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="用户ID")
    username = Column(String(50), nullable=False, comment="用户名")

    # 登录信息
    login_time = Column(DateTime, default=datetime.utcnow, nullable=False, comment="登录时间")
    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")

    # 登录结果
    success = Column(Boolean, nullable=False, comment="是否成功")
    failure_reason = Column(String(255), nullable=True, comment="失败原因")

    # 关联关系
    user = relationship("User")

    def __repr__(self):
        return f"<UserLoginLog(id={self.id}, username='{self.username}', success={self.success})>"


# 权限常量定义
class Permission:
    """权限常量类"""

    # 序号管理权限
    EDIT_SEQUENCE = "edit_sequence"

    # 台账管理权限
    EDIT_OWN_LEDGERS = "edit_own_ledgers"
    EDIT_ALL_LEDGERS = "edit_all_ledgers"
    DELETE_LEDGERS = "delete_ledgers"
    VIEW_OWN_DATA = "view_own_data"
    VIEW_ALL_DATA = "view_all_data"
    VIEW_RELATED_DATA = "view_related_data"

    # 标签管理权限
    MANAGE_TAGS = "manage_tags"
    VIEW_TAGS = "view_tags"

    # 用户管理权限
    MANAGE_USERS = "manage_users"
    VIEW_USERS = "view_users"

    # 部门管理权限
    MANAGE_DEPARTMENTS = "manage_departments"

    # 数据导出权限
    EXPORT_DATA = "export_data"

    # 批注权限
    ADD_COMMENTS = "add_comments"


# 角色权限映射
ROLE_PERMISSIONS = {
    "admin": [
        Permission.EDIT_SEQUENCE,
        Permission.EDIT_ALL_LEDGERS,
        Permission.DELETE_LEDGERS,
        Permission.VIEW_ALL_DATA,
        Permission.MANAGE_TAGS,
        Permission.MANAGE_USERS,
        Permission.MANAGE_DEPARTMENTS,
        Permission.EXPORT_DATA,
        Permission.ADD_COMMENTS,
    ],
    "manager": [
        Permission.EDIT_OWN_LEDGERS,
        Permission.VIEW_RELATED_DATA,
        Permission.MANAGE_TAGS,
        Permission.EXPORT_DATA,
        Permission.ADD_COMMENTS,
    ],
    "user": [
        Permission.EDIT_OWN_LEDGERS,
        Permission.VIEW_OWN_DATA,
        Permission.VIEW_TAGS,
    ]
}


def get_user_permissions(role: str) -> list[str]:
    """获取角色对应的权限列表"""
    return ROLE_PERMISSIONS.get(role, [])


def has_permission(user_role: str, permission: str) -> bool:
    """检查角色是否拥有指定权限"""
    return permission in get_user_permissions(user_role)
