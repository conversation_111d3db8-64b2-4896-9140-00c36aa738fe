"""
用户相关的Pydantic模型
"""
from typing import Optional
from pydantic import BaseModel, Field


class UserBase(BaseModel):
    """用户基础模型"""
    
    username: str = Field(..., description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    department: str = Field(..., description="所属部门")
    role: str = Field(default="user", description="用户角色")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """创建用户模型"""
    
    password: str = Field(..., description="密码")


class UserUpdate(BaseModel):
    """更新用户模型"""
    
    email: Optional[str] = Field(None, description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    department: Optional[str] = Field(None, description="所属部门")
    role: Optional[str] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否激活")


class UserResponse(UserBase):
    """用户响应模型"""
    
    id: int
    
    class Config:
        from_attributes = True


class UserPermissions(BaseModel):
    """用户权限模型"""
    
    user_id: int = Field(..., description="用户ID")
    can_edit_sequence: bool = Field(default=False, description="是否可以编辑序号")
    can_edit_all: bool = Field(default=False, description="是否可以编辑所有台账")
    can_delete: bool = Field(default=False, description="是否可以删除台账")
    can_export: bool = Field(default=True, description="是否可以导出数据")
    can_manage_reminders: bool = Field(default=False, description="是否可以管理提醒")
    
    class Config:
        from_attributes = True
