"""
提醒系统服务
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from ..models.reminder import ReminderRule, Reminder, NotificationCenter, ReminderLog
from ..models.ledger import Ledger
from ..models.user import User
from ..schemas.reminder import (
    ReminderRuleCreate, ReminderRuleUpdate, ReminderRuleResponse, ReminderRuleListResponse,
    ReminderCreate, ReminderUpdate, ReminderResponse, ReminderListResponse,
    NotificationCreate, NotificationUpdate, NotificationResponse, NotificationListResponse,
    ReminderStats, NotificationStats, BatchReminderOperation, BatchNotificationOperation
)


class ReminderService:
    """提醒服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # ==================== 提醒规则管理 ====================
    
    def create_reminder_rule(self, rule_data: Reminder<PERSON>ule<PERSON><PERSON>, created_by: int) -> ReminderRuleResponse:
        """创建提醒规则"""
        rule = ReminderRule(
            **rule_data.dict(),
            created_by=created_by
        )
        self.db.add(rule)
        self.db.commit()
        self.db.refresh(rule)
        
        # 记录日志
        self._log_reminder_action(None, "rule_created", f"创建提醒规则: {rule.name}")
        
        return ReminderRuleResponse.from_orm(rule)
    
    def get_reminder_rules(self, skip: int = 0, limit: int = 20, 
                          is_active: Optional[bool] = None) -> ReminderRuleListResponse:
        """获取提醒规则列表"""
        query = self.db.query(ReminderRule)
        
        if is_active is not None:
            query = query.filter(ReminderRule.is_active == is_active)
        
        total = query.count()
        rules = query.offset(skip).limit(limit).all()
        
        return ReminderRuleListResponse(
            items=[ReminderRuleResponse.from_orm(rule) for rule in rules],
            total=total,
            page=(skip // limit) + 1,
            limit=limit,
            has_next=skip + limit < total
        )
    
    def get_reminder_rule(self, rule_id: int) -> Optional[ReminderRuleResponse]:
        """获取单个提醒规则"""
        rule = self.db.query(ReminderRule).filter(ReminderRule.id == rule_id).first()
        return ReminderRuleResponse.from_orm(rule) if rule else None
    
    def update_reminder_rule(self, rule_id: int, rule_data: ReminderRuleUpdate) -> Optional[ReminderRuleResponse]:
        """更新提醒规则"""
        rule = self.db.query(ReminderRule).filter(ReminderRule.id == rule_id).first()
        if not rule:
            return None
        
        for field, value in rule_data.dict(exclude_unset=True).items():
            setattr(rule, field, value)
        
        rule.updated_at = datetime.now()
        self.db.commit()
        self.db.refresh(rule)
        
        return ReminderRuleResponse.from_orm(rule)
    
    def delete_reminder_rule(self, rule_id: int) -> bool:
        """删除提醒规则"""
        rule = self.db.query(ReminderRule).filter(ReminderRule.id == rule_id).first()
        if not rule:
            return False
        
        # 软删除：设置为非活跃状态
        rule.is_active = False
        rule.updated_at = datetime.now()
        self.db.commit()
        
        return True
    
    # ==================== 提醒记录管理 ====================
    
    def create_reminder(self, reminder_data: ReminderCreate) -> ReminderResponse:
        """创建提醒记录"""
        reminder = Reminder(**reminder_data.dict())
        self.db.add(reminder)
        self.db.commit()
        self.db.refresh(reminder)
        
        # 创建对应的通知
        self._create_notification_from_reminder(reminder)
        
        # 记录日志
        self._log_reminder_action(reminder.id, "created", f"创建提醒: {reminder.title}")
        
        return self._build_reminder_response(reminder)
    
    def get_reminders(self, skip: int = 0, limit: int = 20, 
                     user_id: Optional[int] = None, 
                     status: Optional[str] = None,
                     priority: Optional[str] = None) -> ReminderListResponse:
        """获取提醒记录列表"""
        query = self.db.query(Reminder)
        
        if user_id:
            query = query.filter(Reminder.user_id == user_id)
        if status:
            query = query.filter(Reminder.status == status)
        if priority:
            query = query.filter(Reminder.priority == priority)
        
        query = query.order_by(Reminder.scheduled_time.desc())
        
        total = query.count()
        reminders = query.offset(skip).limit(limit).all()
        
        return ReminderListResponse(
            items=[self._build_reminder_response(reminder) for reminder in reminders],
            total=total,
            page=(skip // limit) + 1,
            limit=limit,
            has_next=skip + limit < total
        )
    
    def get_user_reminders(self, user_id: int, skip: int = 0, limit: int = 20) -> ReminderListResponse:
        """获取用户的提醒记录"""
        return self.get_reminders(skip=skip, limit=limit, user_id=user_id)
    
    def mark_reminder_read(self, reminder_id: int, user_id: int) -> bool:
        """标记提醒为已读"""
        reminder = self.db.query(Reminder).filter(
            and_(Reminder.id == reminder_id, Reminder.user_id == user_id)
        ).first()
        
        if not reminder:
            return False
        
        reminder.is_read = True
        reminder.read_time = datetime.now()
        reminder.status = "read"
        self.db.commit()
        
        # 同时更新通知状态
        notification = self.db.query(NotificationCenter).filter(
            NotificationCenter.reminder_id == reminder_id
        ).first()
        if notification:
            notification.is_read = True
            notification.read_at = datetime.now()
            self.db.commit()
        
        # 记录日志
        self._log_reminder_action(reminder_id, "read", "用户已读提醒")
        
        return True
    
    def dismiss_reminder(self, reminder_id: int, user_id: int) -> bool:
        """忽略提醒"""
        reminder = self.db.query(Reminder).filter(
            and_(Reminder.id == reminder_id, Reminder.user_id == user_id)
        ).first()
        
        if not reminder:
            return False
        
        reminder.is_dismissed = True
        reminder.status = "dismissed"
        self.db.commit()
        
        # 记录日志
        self._log_reminder_action(reminder_id, "dismissed", "用户忽略提醒")
        
        return True
    
    # ==================== 通知中心管理 ====================
    
    def get_user_notifications(self, user_id: int, skip: int = 0, limit: int = 20,
                              unread_only: bool = False) -> NotificationListResponse:
        """获取用户通知"""
        query = self.db.query(NotificationCenter).filter(
            and_(
                NotificationCenter.user_id == user_id,
                NotificationCenter.is_deleted == False
            )
        )
        
        if unread_only:
            query = query.filter(NotificationCenter.is_read == False)
        
        query = query.order_by(
            NotificationCenter.is_pinned.desc(),
            NotificationCenter.created_at.desc()
        )
        
        total = query.count()
        notifications = query.offset(skip).limit(limit).all()
        
        # 计算未读数量
        unread_count = self.db.query(NotificationCenter).filter(
            and_(
                NotificationCenter.user_id == user_id,
                NotificationCenter.is_read == False,
                NotificationCenter.is_deleted == False
            )
        ).count()
        
        return NotificationListResponse(
            items=[NotificationResponse.from_orm(notification) for notification in notifications],
            total=total,
            page=(skip // limit) + 1,
            limit=limit,
            has_next=skip + limit < total,
            unread_count=unread_count
        )
    
    def mark_notification_read(self, notification_id: int, user_id: int) -> bool:
        """标记通知为已读"""
        notification = self.db.query(NotificationCenter).filter(
            and_(
                NotificationCenter.id == notification_id,
                NotificationCenter.user_id == user_id
            )
        ).first()
        
        if not notification:
            return False
        
        notification.is_read = True
        notification.read_at = datetime.now()
        self.db.commit()
        
        return True
    
    def mark_all_notifications_read(self, user_id: int) -> int:
        """标记所有通知为已读"""
        count = self.db.query(NotificationCenter).filter(
            and_(
                NotificationCenter.user_id == user_id,
                NotificationCenter.is_read == False,
                NotificationCenter.is_deleted == False
            )
        ).update({
            "is_read": True,
            "read_at": datetime.now()
        })
        
        self.db.commit()
        return count
    
    # ==================== 自动提醒生成 ====================
    
    def generate_reminders_for_ledger(self, ledger_id: int) -> List[ReminderResponse]:
        """为台账生成提醒"""
        ledger = self.db.query(Ledger).filter(Ledger.id == ledger_id).first()
        if not ledger:
            return []
        
        # 获取适用的提醒规则
        applicable_rules = self._get_applicable_rules(ledger)
        
        created_reminders = []
        for rule in applicable_rules:
            reminder = self._create_reminder_from_rule(rule, ledger)
            if reminder:
                created_reminders.append(reminder)
        
        return created_reminders
    
    def process_scheduled_reminders(self) -> Dict[str, int]:
        """处理计划中的提醒"""
        now = datetime.now()
        
        # 获取需要发送的提醒
        pending_reminders = self.db.query(Reminder).filter(
            and_(
                Reminder.status == "pending",
                Reminder.scheduled_time <= now
            )
        ).all()
        
        sent_count = 0
        failed_count = 0
        
        for reminder in pending_reminders:
            try:
                self._send_reminder(reminder)
                sent_count += 1
            except Exception as e:
                self._log_reminder_action(reminder.id, "failed", f"发送失败: {str(e)}")
                failed_count += 1
        
        return {
            "sent": sent_count,
            "failed": failed_count,
            "total": len(pending_reminders)
        }
    
    # ==================== 统计功能 ====================
    
    def get_reminder_stats(self, user_id: Optional[int] = None) -> ReminderStats:
        """获取提醒统计"""
        base_query = self.db.query(Reminder)
        if user_id:
            base_query = base_query.filter(Reminder.user_id == user_id)
        
        total_reminders = base_query.count()
        pending_reminders = base_query.filter(Reminder.status == "pending").count()
        sent_reminders = base_query.filter(Reminder.status == "sent").count()
        read_reminders = base_query.filter(Reminder.is_read == True).count()
        
        # 过期提醒
        overdue_reminders = base_query.filter(
            and_(
                Reminder.status == "pending",
                Reminder.scheduled_time < datetime.now()
            )
        ).count()
        
        # 规则统计
        total_rules = self.db.query(ReminderRule).count()
        active_rules = self.db.query(ReminderRule).filter(ReminderRule.is_active == True).count()
        
        return ReminderStats(
            total_rules=total_rules,
            active_rules=active_rules,
            total_reminders=total_reminders,
            pending_reminders=pending_reminders,
            sent_reminders=sent_reminders,
            read_reminders=read_reminders,
            overdue_reminders=overdue_reminders
        )
    
    def get_notification_stats(self, user_id: int) -> NotificationStats:
        """获取通知统计"""
        base_query = self.db.query(NotificationCenter).filter(
            and_(
                NotificationCenter.user_id == user_id,
                NotificationCenter.is_deleted == False
            )
        )
        
        total_notifications = base_query.count()
        unread_notifications = base_query.filter(NotificationCenter.is_read == False).count()
        pinned_notifications = base_query.filter(NotificationCenter.is_pinned == True).count()
        
        # 今日通知
        today = datetime.now().date()
        today_notifications = base_query.filter(
            func.date(NotificationCenter.created_at) == today
        ).count()
        
        return NotificationStats(
            total_notifications=total_notifications,
            unread_notifications=unread_notifications,
            pinned_notifications=pinned_notifications,
            today_notifications=today_notifications
        )
    
    # ==================== 私有方法 ====================
    
    def _build_reminder_response(self, reminder: Reminder) -> ReminderResponse:
        """构建提醒响应"""
        response_data = ReminderResponse.from_orm(reminder)
        
        # 添加关联数据
        if reminder.ledger:
            response_data.ledger_title = reminder.ledger.work_item
        if reminder.user:
            response_data.user_name = reminder.user.full_name
        if reminder.rule:
            response_data.rule_name = reminder.rule.name
        
        return response_data
    
    def _create_notification_from_reminder(self, reminder: Reminder):
        """从提醒创建通知"""
        notification = NotificationCenter(
            user_id=reminder.user_id,
            reminder_id=reminder.id,
            title=reminder.title,
            content=reminder.content,
            notification_type="reminder",
            icon="bell",
            color=self._get_priority_color(reminder.priority),
            action_url=f"/ledgers/{reminder.ledger_id}",
            action_text="查看台账"
        )
        
        self.db.add(notification)
        self.db.commit()
    
    def _get_priority_color(self, priority: str) -> str:
        """获取优先级颜色"""
        color_map = {
            "low": "info",
            "normal": "primary", 
            "high": "warning",
            "urgent": "danger"
        }
        return color_map.get(priority, "primary")
    
    def _get_applicable_rules(self, ledger: Ledger) -> List[ReminderRule]:
        """获取适用于台账的提醒规则"""
        rules = self.db.query(ReminderRule).filter(ReminderRule.is_active == True).all()
        
        applicable_rules = []
        for rule in rules:
            if self._rule_applies_to_ledger(rule, ledger):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    def _rule_applies_to_ledger(self, rule: ReminderRule, ledger: Ledger) -> bool:
        """检查规则是否适用于台账"""
        # 检查部门匹配
        if rule.department and rule.department != ledger.responsible_dept:
            return False
        
        # 检查标签匹配
        if rule.tags:
            ledger_tags = ledger.tags or []
            if not any(tag in ledger_tags for tag in rule.tags):
                return False
        
        # 检查工作类型匹配
        if rule.work_type:
            # 这里可以根据实际业务逻辑判断工作类型
            pass
        
        return True
    
    def _create_reminder_from_rule(self, rule: ReminderRule, ledger: Ledger) -> Optional[ReminderResponse]:
        """根据规则为台账创建提醒"""
        # 计算提醒时间
        scheduled_time = datetime.now() + timedelta(days=rule.advance_days)

        # 获取责任人 - 修正查询逻辑
        user = self.db.query(User).filter(User.department == ledger.responsible_dept).first()
        if not user:
            # 如果找不到对应部门用户，使用第一个用户作为默认
            user = self.db.query(User).first()
            if not user:
                return None

        reminder_data = ReminderCreate(
            rule_id=rule.id,
            ledger_id=ledger.id,
            user_id=user.id,
            title=f"台账提醒：{ledger.work_item}",
            content=f"您的台账「{ledger.work_item}」需要关注，请及时处理。",
            reminder_type="deadline",
            priority="normal",
            scheduled_time=scheduled_time
        )

        return self.create_reminder(reminder_data)
    
    def _send_reminder(self, reminder: Reminder):
        """发送提醒"""
        # 更新提醒状态
        reminder.status = "sent"
        reminder.sent_time = datetime.now()
        self.db.commit()
        
        # 记录日志
        self._log_reminder_action(reminder.id, "sent", "提醒已发送")
    
    def _log_reminder_action(self, reminder_id: Optional[int], action: str, details: str):
        """记录提醒操作日志"""
        log = ReminderLog(
            reminder_id=reminder_id,
            action=action,
            details=details
        )
        self.db.add(log)
        self.db.commit()
