# 问题修复计划需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-28
- **负责人**: Emma (产品经理)
- **项目**: 台账管理系统CRUD测试问题修复

## 2. 背景与问题陈述

### 2.1 测试发现的问题总结
基于CRUD自动化测试报告，发现以下关键问题需要修复：

#### 2.1.1 UI交互问题
1. **Element Plus下拉选择器交互困难**
   - 自动化测试中无法正确选择下拉选项
   - 选择器在某些情况下不响应点击事件
   - 影响台账创建、用户管理等核心功能

2. **对话框层级遮挡问题**
   - 多层对话框导致底层元素无法点击
   - 编辑对话框遮挡删除按钮
   - 影响用户操作流程的连续性

#### 2.1.2 表单验证问题
1. **验证过于严格**
   - 表单要求填写所有字段才能提交
   - 缺少渐进式验证机制
   - 用户体验不够友好

2. **错误提示不够清晰**
   - 验证失败时提示信息不够具体
   - 缺少字段级别的实时验证反馈

#### 2.1.3 功能完整性问题
1. **台账创建功能不完整**
   - 序号重复验证阻止正常创建
   - 责任单位选择器无法正常工作
   - 必填字段验证逻辑需要优化

2. **用户管理功能限制**
   - 部门选择器存在交互问题
   - 用户创建流程不够顺畅

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **交互优化目标**: 解决所有UI交互问题，提升用户操作体验
2. **功能完善目标**: 确保所有CRUD功能正常工作
3. **验证优化目标**: 改进表单验证机制，提供更好的用户反馈
4. **测试兼容目标**: 提升自动化测试的兼容性和稳定性

### 3.2 关键结果 (Key Results)
- UI交互问题修复率达到100%
- CRUD功能测试成功率提升到95%以上
- 表单验证用户体验评分提升50%
- 自动化测试稳定性提升到90%以上

## 4. 功能规格详述

### 4.1 UI交互问题修复

#### 4.1.1 Element Plus下拉选择器优化
**修复范围**:
- 台账创建页面的责任单位选择器
- 台账创建页面的填报单位选择器
- 用户管理页面的部门选择器
- 用户管理页面的角色选择器

**修复方案**:
1. **添加键盘导航支持**
   - 支持方向键选择选项
   - 支持Enter键确认选择
   - 支持Escape键取消选择

2. **优化选择器状态管理**
   - 改进选项加载逻辑
   - 添加加载状态指示器
   - 优化选项过滤和搜索功能

3. **增强自动化测试兼容性**
   - 添加稳定的测试选择器属性
   - 优化异步操作的等待机制
   - 提供测试友好的API接口

#### 4.1.2 对话框层级管理优化
**问题场景**:
- 编辑用户对话框遮挡删除按钮
- 快速编辑台账对话框层级问题
- 多层对话框的交互冲突

**修复方案**:
1. **对话框z-index管理**
   - 实现动态z-index分配
   - 确保最新打开的对话框在最上层
   - 添加对话框堆栈管理

2. **交互逻辑优化**
   - 优化对话框打开/关闭时机
   - 改进对话框外点击处理
   - 添加对话框状态同步机制

### 4.2 表单验证机制优化

#### 4.2.1 渐进式验证实现
**当前问题**:
- 表单要求所有字段填写完整才能提交
- 缺少草稿保存和分步验证机制

**优化方案**:
1. **分级验证策略**
   - 必填字段：序号、工作事项、工作措施、工作进展
   - 推荐字段：责任单位、对口部门、关键词标签
   - 可选字段：其他辅助信息

2. **实时验证反馈**
   - 字段失焦时进行验证
   - 实时显示验证状态图标
   - 提供具体的错误提示信息

3. **智能表单填充**
   - 基于用户角色自动填充部门信息
   - 提供常用选项的快速选择
   - 支持表单模板和预设值

#### 4.2.2 错误提示优化
**改进内容**:
1. **具体化错误信息**
   - 明确指出哪个字段存在问题
   - 提供修复建议和示例
   - 支持多语言错误提示

2. **视觉化反馈增强**
   - 错误字段高亮显示
   - 添加错误图标和颜色标识
   - 提供错误摘要面板

### 4.3 功能完整性修复

#### 4.3.1 台账创建功能完善
**修复内容**:
1. **序号管理优化**
   - 改进序号重复检测逻辑
   - 提供序号建议和自动生成
   - 支持序号范围验证

2. **选择器功能修复**
   - 修复责任单位选择器交互问题
   - 优化对口部门多选功能
   - 改进标签选择的用户体验

3. **表单提交流程优化**
   - 支持分步提交和草稿保存
   - 添加提交前确认机制
   - 优化提交成功后的页面跳转

#### 4.3.2 用户管理功能增强
**修复内容**:
1. **用户创建流程优化**
   - 简化必填字段要求
   - 优化部门选择器交互
   - 添加用户创建向导

2. **用户编辑功能改进**
   - 修复编辑对话框交互问题
   - 优化用户信息更新流程
   - 添加批量操作支持

## 5. 技术实现方案

### 5.1 前端修复方案

#### 5.1.1 Element Plus组件优化
```typescript
// 下拉选择器增强
const selectProps = {
  filterable: true,
  clearable: true,
  'data-testid': 'department-select', // 测试友好
  loading: departmentLoading.value,
  'no-data-text': '暂无数据',
  'loading-text': '加载中...'
}

// 键盘导航支持
const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowDown':
      // 处理向下导航
      break;
    case 'ArrowUp':
      // 处理向上导航
      break;
    case 'Enter':
      // 确认选择
      break;
    case 'Escape':
      // 取消选择
      break;
  }
}
```

#### 5.1.2 表单验证优化
```typescript
// 分级验证规则
const validationRules = {
  required: ['sequence_number', 'work_item', 'measures', 'progress'],
  recommended: ['responsible_dept', 'counterpart_depts', 'tags'],
  optional: ['work_objectives', 'effectiveness', 'remarks']
}

// 实时验证
const validateField = async (field: string, value: any) => {
  const rule = validationRules[field];
  if (rule) {
    const result = await rule.validator(value);
    updateFieldStatus(field, result);
  }
}
```

### 5.2 后端支持优化

#### 5.2.1 API响应优化
```python
# 改进错误响应格式
class ValidationErrorResponse(BaseModel):
    field: str
    message: str
    suggestion: Optional[str] = None
    
class APIResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    errors: List[ValidationErrorResponse] = []
    message: Optional[str] = None
```

## 6. 测试验证计划

### 6.1 功能测试
1. **CRUD操作测试**
   - 台账创建、编辑、删除功能
   - 用户管理完整流程测试
   - 表单验证机制测试

2. **UI交互测试**
   - 下拉选择器操作测试
   - 对话框层级管理测试
   - 键盘导航功能测试

### 6.2 自动化测试
1. **Playwright测试优化**
   - 添加稳定的元素选择器
   - 优化异步操作等待机制
   - 增加测试用例覆盖率

2. **回归测试**
   - 确保修复不影响现有功能
   - 验证所有CRUD操作正常
   - 检查用户体验改进效果

## 7. 实施优先级

### 7.1 高优先级 (立即修复)
1. Element Plus下拉选择器交互问题
2. 台账创建功能的表单验证优化
3. 对话框层级遮挡问题修复

### 7.2 中优先级 (本周完成)
1. 用户管理功能完善
2. 表单验证机制优化
3. 错误提示信息改进

### 7.3 低优先级 (后续优化)
1. 自动化测试兼容性提升
2. 用户体验细节优化
3. 性能优化和代码重构

## 8. 验收标准

### 8.1 功能验收
- 所有CRUD操作能够正常完成
- 表单验证提供清晰的用户反馈
- UI交互流畅，无阻塞问题

### 8.2 测试验收
- 自动化测试成功率达到95%以上
- 手动测试覆盖所有修复功能
- 回归测试确保无新增问题

### 8.3 用户体验验收
- 操作流程简化，用户友好
- 错误提示清晰具体
- 响应速度满足用户期望
