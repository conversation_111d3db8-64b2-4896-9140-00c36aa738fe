/* 台账管理系统 - 公共样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.user-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.logout-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 导航菜单 */
.nav-menu {
    background: white;
    padding: 0 20px;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.nav-tabs {
    display: flex;
    list-style: none;
}

.nav-tab {
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
    font-weight: 500;
    text-decoration: none;
    color: #333;
}

.nav-tab:hover {
    background-color: #f8f9fa;
    color: #1976d2;
}

.nav-tab.active {
    color: #1976d2;
    border-bottom-color: #1976d2;
    background-color: #f8f9fa;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 120px);
}

.page-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-height: calc(100vh - 160px);
}

.page-title {
    color: #1976d2;
    margin-bottom: 30px;
    text-align: center;
    border-bottom: 2px solid #1976d2;
    padding-bottom: 15px;
    font-size: 24px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #1976d2;
    color: white;
}

.btn-primary:hover {
    background: #1565c0;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e0e0e0;
}

.btn-success {
    background: #4caf50;
    color: white;
}

.btn-success:hover {
    background: #45a049;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #da190b;
}

.btn-warning {
    background: #ff9800;
    color: white;
}

.btn-warning:hover {
    background: #f57c00;
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-table th {
    background: linear-gradient(135deg, #1976d2, #42a5f5);
    color: white;
    padding: 12px 8px;
    text-align: left;
    font-weight: 500;
    border-bottom: 2px solid #1565c0;
}

.data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

.data-table tr:nth-child(even) {
    background-color: #fafafa;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: bold;
    margin-bottom: 8px;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.required {
    color: red;
}

/* 标签样式 */
.tag-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    border: 1px solid #bbdefb;
    margin-right: 4px;
    display: inline-block;
}

.tag-display {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

/* 状态标签 */
.status-active {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-inactive {
    background: #ffebee;
    color: #c62828;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.page-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
    text-decoration: none;
    color: #333;
}

.page-btn:hover {
    background: #f0f8ff;
    border-color: #1976d2;
}

.page-btn.active {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tab {
        padding: 10px 15px;
    }
    
    .page-container {
        margin: 10px;
        padding: 15px;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示 */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    border-left: 4px solid;
}

.alert-success {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

.alert-warning {
    background: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}

.alert-danger {
    background: #ffebee;
    border-color: #f44336;
    color: #c62828;
}

.alert-info {
    background: #e3f2fd;
    border-color: #2196f3;
    color: #1976d2;
}
