<template>
  <div class="change-password-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>修改密码</h1>
        <p>为了您的账户安全，请定期更换密码</p>
      </div>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 修改密码表单 -->
    <div class="password-container">
      <el-row justify="center">
        <el-col :span="12" :xs="20" :sm="16" :md="12">
          <el-card shadow="hover" class="password-card">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Lock /></el-icon>
                <span>修改密码</span>
              </div>
            </template>

            <el-form 
              :model="passwordForm" 
              :rules="formRules"
              ref="formRef"
              label-width="120px"
              @submit.prevent="handleSubmit"
            >
              <el-form-item label="当前密码" prop="currentPassword">
                <el-input 
                  v-model="passwordForm.currentPassword"
                  type="password"
                  placeholder="请输入当前密码"
                  show-password
                  clearable
                />
              </el-form-item>

              <el-form-item label="新密码" prop="newPassword">
                <el-input 
                  v-model="passwordForm.newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  show-password
                  clearable
                  @input="checkPasswordStrength"
                />
                <!-- 密码强度指示器 -->
                <div class="password-strength" v-if="passwordForm.newPassword">
                  <div class="strength-bar">
                    <div 
                      class="strength-fill"
                      :class="strengthClass"
                      :style="{ width: strengthPercent + '%' }"
                    ></div>
                  </div>
                  <span class="strength-text" :class="strengthClass">
                    {{ strengthText }}
                  </span>
                </div>
              </el-form-item>

              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input 
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  show-password
                  clearable
                />
              </el-form-item>

              <!-- 密码要求提示 -->
              <el-alert
                title="密码要求"
                type="info"
                :closable="false"
                class="password-requirements"
              >
                <ul>
                  <li>密码长度至少8位</li>
                  <li>包含至少一个数字</li>
                  <li>包含至少一个字母</li>
                  <li>建议包含特殊字符以提高安全性</li>
                </ul>
              </el-alert>

              <el-form-item class="submit-section">
                <el-button 
                  type="primary" 
                  @click="handleSubmit"
                  :loading="loading"
                  size="large"
                  style="width: 100%"
                >
                  修改密码
                </el-button>
                <el-button 
                  @click="resetForm"
                  size="large"
                  style="width: 100%; margin-top: 10px"
                >
                  重置表单
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 安全提示 -->
          <el-card shadow="hover" class="security-tips">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><InfoFilled /></el-icon>
                <span>安全提示</span>
              </div>
            </template>
            <div class="tips-content">
              <el-alert
                title="为了您的账户安全，请注意："
                type="warning"
                :closable="false"
              >
                <ul>
                  <li>不要使用与其他网站相同的密码</li>
                  <li>不要在公共场所输入密码</li>
                  <li>定期更换密码（建议3-6个月）</li>
                  <li>如发现异常登录，请立即修改密码</li>
                </ul>
              </el-alert>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Lock, InfoFilled } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 密码表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码强度
const passwordStrength = ref(0)

// 表单验证规则
const formRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: Function) => {
        if (!value) {
          callback()
          return
        }
        
        const hasNumber = /\d/.test(value)
        const hasLetter = /[a-zA-Z]/.test(value)
        
        if (!hasNumber) {
          callback(new Error('密码必须包含至少一个数字'))
          return
        }
        
        if (!hasLetter) {
          callback(new Error('密码必须包含至少一个字母'))
          return
        }
        
        if (value === passwordForm.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
          return
        }
        
        callback()
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 检查密码强度
const checkPasswordStrength = () => {
  const password = passwordForm.newPassword
  let strength = 0
  
  if (password.length >= 8) strength += 20
  if (password.length >= 12) strength += 10
  if (/[a-z]/.test(password)) strength += 20
  if (/[A-Z]/.test(password)) strength += 20
  if (/\d/.test(password)) strength += 20
  if (/[^a-zA-Z0-9]/.test(password)) strength += 10
  
  passwordStrength.value = Math.min(strength, 100)
}

// 密码强度样式类
const strengthClass = computed(() => {
  if (passwordStrength.value < 30) return 'weak'
  if (passwordStrength.value < 60) return 'medium'
  if (passwordStrength.value < 80) return 'strong'
  return 'very-strong'
})

// 密码强度百分比
const strengthPercent = computed(() => passwordStrength.value)

// 密码强度文本
const strengthText = computed(() => {
  if (passwordStrength.value < 30) return '弱'
  if (passwordStrength.value < 60) return '中等'
  if (passwordStrength.value < 80) return '强'
  return '很强'
})

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    const success = await authStore.changePassword(
      passwordForm.currentPassword,
      passwordForm.newPassword
    )
    
    if (success) {
      ElMessage.success('密码修改成功，请重新登录')
      // 延迟跳转到登录页
      setTimeout(() => {
        authStore.logout()
        router.push('/login')
      }, 1500)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改密码失败:', error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordStrength.value = 0
}
</script>

<style scoped>
.change-password-view {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
}

.password-container {
  max-width: 1200px;
}

.password-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background-color: #f56c6c;
}

.strength-fill.medium {
  background-color: #e6a23c;
}

.strength-fill.strong {
  background-color: #67c23a;
}

.strength-fill.very-strong {
  background-color: #409eff;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
}

.strength-text.weak {
  color: #f56c6c;
}

.strength-text.medium {
  color: #e6a23c;
}

.strength-text.strong {
  color: #67c23a;
}

.strength-text.very-strong {
  color: #409eff;
}

.password-requirements {
  margin: 15px 0;
}

.password-requirements ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.password-requirements li {
  margin: 4px 0;
  font-size: 13px;
}

.submit-section {
  margin-top: 30px;
}

.security-tips {
  margin-top: 20px;
}

.tips-content ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.tips-content li {
  margin: 6px 0;
  font-size: 13px;
}

@media (max-width: 768px) {
  .change-password-view {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
