"""
台账业务逻辑服务
"""
import math
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from ..models.ledger import Ledger
from ..schemas.ledger import (
    LedgerResponse, LedgerListResponse, StatsResponse, LedgerCreate,
    LedgerUpdate, LedgerDraft, SequenceValidationResponse, NextSequenceResponse
)
import uuid
import re


class LedgerService:
    """台账业务逻辑服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_ledgers_with_pagination(
        self,
        page: int = 1,
        limit: int = 20,
        filters: Optional[Dict[str, Any]] = None
    ) -> LedgerListResponse:
        """
        获取台账列表，支持分页和筛选
        """
        query = self.db.query(Ledger)

        # 应用筛选条件
        if filters:
            query = self._apply_filters(query, filters)

        # 计算总数
        total = query.count()

        # 计算分页
        offset = (page - 1) * limit
        pages = math.ceil(total / limit)

        # 获取分页数据
        ledgers = query.offset(offset).limit(limit).all()

        # 转换为响应模型
        ledger_responses = [self._convert_to_response(ledger) for ledger in ledgers]

        return LedgerListResponse(
            data=ledger_responses,
            total=total,
            page=page,
            limit=limit,
            pages=pages
        )

    def get_ledger_by_id(self, ledger_id: int) -> Optional[LedgerResponse]:
        """
        根据ID获取台账详情
        """
        ledger = self.db.query(Ledger).filter(Ledger.id == ledger_id).first()
        if ledger:
            return self._convert_to_response(ledger)
        return None

    def get_ledger_stats(self, filters: Optional[Dict[str, Any]] = None) -> StatsResponse:
        """
        获取台账统计数据
        """
        query = self.db.query(Ledger)

        # 应用筛选条件
        if filters:
            query = self._apply_filters(query, filters)

        # 基础统计
        total_count = query.count()
        completed_count = query.filter(Ledger.status.in_(["completed", "completed_ongoing"])).count()
        in_progress_count = query.filter(Ledger.status == "in_progress").count()
        draft_count = query.filter(Ledger.status == "draft").count()
        completed_ongoing_count = query.filter(Ledger.status == "completed_ongoing").count()

        # 按部门统计
        dept_stats = {}
        dept_query = query.with_entities(
            Ledger.responsible_dept,
            func.count(Ledger.id)
        ).group_by(Ledger.responsible_dept).all()

        for dept, count in dept_query:
            dept_stats[dept] = count

        # 按标签统计（需要处理JSON字段）
        tag_stats = {}
        ledgers_with_tags = query.filter(Ledger.tags.isnot(None)).all()

        for ledger in ledgers_with_tags:
            if ledger.tags:
                for tag in ledger.tags:
                    tag_stats[tag] = tag_stats.get(tag, 0) + 1

        return StatsResponse(
            total_count=total_count,
            completed_count=completed_count,
            in_progress_count=in_progress_count,
            draft_count=draft_count,
            completed_ongoing_count=completed_ongoing_count,
            by_department=dept_stats,
            by_tags=tag_stats
        )

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """
        应用筛选条件到查询
        """
        # 责任单位筛选 - 支持多选
        if filters.get("responsible_dept"):
            responsible_dept = filters["responsible_dept"]
            if isinstance(responsible_dept, list):
                query = query.filter(Ledger.responsible_dept.in_(responsible_dept))
            else:
                query = query.filter(Ledger.responsible_dept == responsible_dept)

        # 状态筛选 - 支持多选
        if filters.get("status"):
            status = filters["status"]
            if isinstance(status, list):
                query = query.filter(Ledger.status.in_(status))
            else:
                query = query.filter(Ledger.status == status)

        # 年度筛选
        if filters.get("year"):
            query = query.filter(Ledger.year == filters["year"])

        # 对口部门筛选 - JSON字段
        if filters.get("counterpart_depts"):
            counterpart_depts = filters["counterpart_depts"]
            if isinstance(counterpart_depts, list) and counterpart_depts:
                # 使用JSON_EXTRACT或LIKE进行模糊匹配
                conditions = []
                for dept in counterpart_depts:
                    conditions.append(Ledger.counterpart_depts.like(f'%"{dept}"%'))
                query = query.filter(or_(*conditions))

        # 标签筛选 - JSON字段
        if filters.get("tags"):
            tags = filters["tags"]
            if isinstance(tags, list) and tags:
                # 使用JSON_EXTRACT或LIKE进行模糊匹配
                conditions = []
                for tag in tags:
                    conditions.append(Ledger.tags.like(f'%"{tag}"%'))
                query = query.filter(or_(*conditions))

        return query

    def _convert_to_response(self, ledger: Ledger) -> LedgerResponse:
        """
        将数据库模型转换为响应模型
        """
        import json

        # 安全处理JSON字段
        counterpart_depts = []
        if ledger.counterpart_depts:
            try:
                if isinstance(ledger.counterpart_depts, str):
                    counterpart_depts = json.loads(ledger.counterpart_depts)
                elif isinstance(ledger.counterpart_depts, list):
                    counterpart_depts = ledger.counterpart_depts
            except (json.JSONDecodeError, TypeError):
                counterpart_depts = []

        tags = []
        if ledger.tags:
            try:
                if isinstance(ledger.tags, str):
                    tags = json.loads(ledger.tags)
                elif isinstance(ledger.tags, list):
                    tags = ledger.tags
            except (json.JSONDecodeError, TypeError):
                tags = []

        return LedgerResponse(
            id=ledger.id,
            sequence_number=ledger.sequence_number,
            work_item=ledger.work_item,
            measures=ledger.measures,
            progress=ledger.progress,
            effectiveness=ledger.effectiveness,
            responsible_dept=ledger.responsible_dept,
            counterpart_depts=counterpart_depts,
            tags=tags,
            remarks=ledger.remarks,
            status=ledger.status,
            created_at=ledger.created_at,
            updated_at=ledger.updated_at,
            year=ledger.year
        )

    def create_ledger(self, ledger_data: LedgerCreate) -> LedgerResponse:
        """
        创建新的台账记录
        """
        # 将列表字段转换为JSON字符串
        counterpart_depts_json = json.dumps(ledger_data.counterpart_depts, ensure_ascii=False)
        tags_json = json.dumps(ledger_data.tags, ensure_ascii=False)

        # 创建数据库模型
        db_ledger = Ledger(
            sequence_number=ledger_data.sequence_number,
            work_item=ledger_data.work_item,
            measures=ledger_data.measures,
            progress=ledger_data.progress,
            effectiveness=ledger_data.effectiveness,
            responsible_dept=ledger_data.responsible_dept,
            counterpart_depts=counterpart_depts_json,
            tags=tags_json,
            remarks=ledger_data.remarks,
            status=ledger_data.status,
            year=ledger_data.year,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # 保存到数据库
        self.db.add(db_ledger)
        self.db.commit()
        self.db.refresh(db_ledger)

        # 转换为响应模型
        return self._convert_to_response(db_ledger)

    def update_ledger(self, ledger_id: int, ledger_data: LedgerUpdate) -> Optional[LedgerResponse]:
        """
        更新台账记录
        """
        # 查找现有记录
        db_ledger = self.db.query(Ledger).filter(Ledger.id == ledger_id).first()
        if not db_ledger:
            return None

        # 更新字段
        update_data = ledger_data.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            if field in ['counterpart_depts', 'tags'] and value is not None:
                # 将列表字段转换为JSON字符串
                value = json.dumps(value, ensure_ascii=False)
            setattr(db_ledger, field, value)

        # 更新时间戳
        db_ledger.updated_at = datetime.now()

        # 保存更改
        self.db.commit()
        self.db.refresh(db_ledger)

        # 转换为响应模型
        return self._convert_to_response(db_ledger)

    def delete_ledger(self, ledger_id: int) -> bool:
        """
        删除台账记录
        """
        # 查找现有记录
        db_ledger = self.db.query(Ledger).filter(Ledger.id == ledger_id).first()
        if not db_ledger:
            return False

        # 删除记录
        self.db.delete(db_ledger)
        self.db.commit()

        return True

    def save_draft(self, draft_data: LedgerDraft) -> Dict[str, Any]:
        """
        保存台账草稿
        """
        # 生成草稿ID
        if not draft_data.draft_id:
            draft_data.draft_id = str(uuid.uuid4())

        # 这里可以将草稿保存到缓存或临时表
        # 为简化实现，我们返回草稿数据
        return {
            "draft_id": draft_data.draft_id,
            "data": draft_data.dict(),
            "saved_at": datetime.now().isoformat()
        }

    def validate_sequence_number(self, sequence_number: str, year: int, exclude_id: Optional[int] = None) -> SequenceValidationResponse:
        """
        验证序号唯一性
        """
        # 验证格式
        if not re.match(r'^\d{3}$', sequence_number):
            return SequenceValidationResponse(
                is_valid=False,
                message="序号必须是3位数字"
            )

        # 验证唯一性
        query = self.db.query(Ledger).filter(
            and_(
                Ledger.sequence_number == sequence_number,
                Ledger.year == year
            )
        )

        if exclude_id:
            query = query.filter(Ledger.id != exclude_id)

        existing = query.first()

        if existing:
            return SequenceValidationResponse(
                is_valid=False,
                message=f"序号 {sequence_number} 在 {year} 年已存在"
            )

        return SequenceValidationResponse(
            is_valid=True,
            message="序号可用"
        )

    def get_next_sequence_number(self, year: int) -> NextSequenceResponse:
        """
        获取下一个可用的序号
        """
        # 查询当前年度最大的序号
        max_sequence = self.db.query(func.max(Ledger.sequence_number)).filter(
            Ledger.year == year
        ).scalar()

        if max_sequence:
            try:
                next_num = int(max_sequence) + 1
                next_sequence = f"{next_num:03d}"
            except ValueError:
                # 如果序号不是数字，从001开始
                next_sequence = "001"
        else:
            next_sequence = "001"

        # 确保序号不超过999
        if int(next_sequence) > 999:
            next_sequence = "999"

        return NextSequenceResponse(next_sequence=next_sequence)

    def create_ledger_with_validation(self, ledger_data: LedgerCreate) -> Optional[LedgerResponse]:
        """
        创建台账（带验证）
        """
        # 验证序号唯一性
        validation = self.validate_sequence_number(
            ledger_data.sequence_number,
            ledger_data.year
        )

        if not validation.is_valid:
            raise ValueError(validation.message)

        # 创建台账
        return self.create_ledger(ledger_data)

    def update_ledger_with_validation(self, ledger_id: int, ledger_data: LedgerUpdate) -> Optional[LedgerResponse]:
        """
        更新台账（带验证）
        """
        # 如果更新序号，验证唯一性
        if ledger_data.sequence_number and ledger_data.year:
            validation = self.validate_sequence_number(
                ledger_data.sequence_number,
                ledger_data.year,
                exclude_id=ledger_id
            )

            if not validation.is_valid:
                raise ValueError(validation.message)

        # 更新台账
        return self.update_ledger(ledger_id, ledger_data)
