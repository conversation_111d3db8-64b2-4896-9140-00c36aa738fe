/**
 * 提醒系统API
 */
import api from './index'

export interface ReminderRule {
  id: number
  name: string
  description: string
  trigger_type: 'deadline' | 'schedule' | 'manual'
  trigger_config: Record<string, any>
  target_users: string[]
  message_template: string
  is_active: boolean
  created_by: number
  created_at: string
  updated_at: string
}

export interface ReminderRuleCreate {
  name: string
  description: string
  trigger_type: 'deadline' | 'schedule' | 'manual'
  trigger_config: Record<string, any>
  target_users: string[]
  message_template: string
  is_active?: boolean
}

export interface Reminder {
  id: number
  rule_id: number
  target_user_id: number
  message: string
  status: 'pending' | 'sent' | 'failed'
  scheduled_at: string
  sent_at?: string
  created_at: string
}

export interface Notification {
  id: number
  user_id: number
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  is_read: boolean
  created_at: string
  read_at?: string
}

export interface ReminderStats {
  total_rules: number
  active_rules: number
  total_reminders: number
  pending_reminders: number
  sent_reminders: number
  failed_reminders: number
}

export interface NotificationStats {
  total_notifications: number
  unread_notifications: number
  read_notifications: number
}

// 提醒规则管理
export const getReminderRules = async (params?: {
  page?: number
  limit?: number
  is_active?: boolean
}): Promise<{
  items: ReminderRule[]
  total: number
  page: number
  limit: number
}> => {
  const response = await api.get('/reminders/rules', { params })
  return response.data
}

export const createReminderRule = async (data: ReminderRuleCreate): Promise<ReminderRule> => {
  const response = await api.post('/reminders/rules', data)
  return response.data
}

export const updateReminderRule = async (id: number, data: Partial<ReminderRuleCreate>): Promise<ReminderRule> => {
  const response = await api.put(`/reminders/rules/${id}`, data)
  return response.data
}

export const deleteReminderRule = async (id: number): Promise<void> => {
  await api.delete(`/reminders/rules/${id}`)
}

export const toggleReminderRule = async (id: number, is_active: boolean): Promise<ReminderRule> => {
  const response = await api.patch(`/reminders/rules/${id}/toggle`, { is_active })
  return response.data
}

// 提醒记录管理
export const getReminders = async (params?: {
  page?: number
  limit?: number
  status?: string
  user_id?: number
}): Promise<{
  items: Reminder[]
  total: number
  page: number
  limit: number
}> => {
  const response = await api.get('/reminders', { params })
  return response.data
}

export const sendReminder = async (id: number): Promise<Reminder> => {
  const response = await api.post(`/reminders/${id}/send`)
  return response.data
}

export const batchSendReminders = async (ids: number[]): Promise<void> => {
  await api.post('/reminders/batch-send', { reminder_ids: ids })
}

// 通知中心管理
export const getNotifications = async (params?: {
  page?: number
  limit?: number
  is_read?: boolean
}): Promise<{
  items: Notification[]
  total: number
  page: number
  limit: number
}> => {
  const response = await api.get('/reminders/notifications', { params })
  return response.data
}

export const markNotificationAsRead = async (id: number): Promise<Notification> => {
  const response = await api.patch(`/reminders/notifications/${id}/read`)
  return response.data
}

export const markAllNotificationsAsRead = async (): Promise<void> => {
  await api.patch('/reminders/notifications/read-all')
}

export const deleteNotification = async (id: number): Promise<void> => {
  await api.delete(`/reminders/notifications/${id}`)
}

// 统计信息
export const getReminderStats = async (): Promise<ReminderStats> => {
  const response = await api.get('/reminders/stats')
  return response.data
}

export const getNotificationStats = async (): Promise<NotificationStats> => {
  const response = await api.get('/reminders/notifications/stats')
  return response.data
}

// 手动触发提醒
export const triggerReminder = async (ruleId: number, targetUsers?: string[]): Promise<void> => {
  await api.post(`/reminders/rules/${ruleId}/trigger`, { target_users: targetUsers })
}
