"""
年度管理API路由
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ...api.deps import get_db, get_current_user
from ...models.user import User
from ...services.year_service import YearService
from ...schemas.year import (
    YearCreate, YearUpdate, YearResponse, YearListResponse,
    YearStatsResponse, YearComparisonResponse, YearCreateResponse,
    MessageResponse
)

router = APIRouter(tags=["年度管理"])


@router.get("", response_model=YearListResponse, summary="获取年度列表")
async def get_years(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取年度列表

    - **skip**: 跳过的记录数，用于分页
    - **limit**: 返回的记录数，最大100条
    """
    year_service = YearService(db)
    return year_service.get_years(skip=skip, limit=limit)


@router.post("", response_model=YearCreateResponse, summary="创建新年度")
async def create_year(
    year_data: YearCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新年度

    - **year**: 年度数字 (2020-2030)
    - **inherit_config**: 是否继承配置信息
    - **archive_previous**: 是否归档上一年度

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    year_service = YearService(db)
    try:
        return year_service.create_year(year_data, current_user.id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建年度失败: {str(e)}")


@router.get("/{year}", response_model=YearResponse, summary="获取年度详情")
async def get_year(
    year: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取指定年度的详细信息

    - **year**: 年度数字
    """
    year_service = YearService(db)
    year_info = year_service.get_year_by_year(year)

    if not year_info:
        raise HTTPException(status_code=404, detail=f"年度 {year} 不存在")

    return year_info


@router.put("/{year}", response_model=YearResponse, summary="更新年度信息")
async def update_year(
    year: int,
    year_data: YearUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新年度信息

    - **year**: 年度数字
    - **year_data**: 更新的年度数据

    需要管理员权限
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    year_service = YearService(db)
    updated_year = year_service.update_year(year, year_data)

    if not updated_year:
        raise HTTPException(status_code=404, detail=f"年度 {year} 不存在")

    return updated_year


@router.delete("/{year}", response_model=MessageResponse, summary="删除年度")
async def delete_year(
    year: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除年度

    - **year**: 年度数字

    需要管理员权限，且年度不能有关联的台账数据
    """
    # 检查权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    year_service = YearService(db)
    try:
        success = year_service.delete_year(year)
        if not success:
            raise HTTPException(status_code=404, detail=f"年度 {year} 不存在")

        return MessageResponse(message=f"年度 {year} 删除成功")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除年度失败: {str(e)}")


@router.get("/{year}/stats", response_model=YearStatsResponse, summary="获取年度统计")
async def get_year_stats(
    year: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取年度统计信息

    - **year**: 年度数字

    包含台账统计、部门分布、月度进度等信息
    """
    year_service = YearService(db)
    stats = year_service.get_year_stats(year)

    if not stats:
        raise HTTPException(status_code=404, detail=f"年度 {year} 不存在")

    return stats


@router.get("/{year}/ledgers", summary="获取年度台账数据")
async def get_year_ledgers(
    year: int,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取指定年度的台账数据

    - **year**: 年度数字
    - **page**: 页码，从1开始
    - **size**: 每页记录数，最大100条

    返回历史台账数据（只读）
    """
    year_service = YearService(db)

    # 检查年度是否存在
    year_info = year_service.get_year_by_year(year)
    if not year_info:
        raise HTTPException(status_code=404, detail=f"年度 {year} 不存在")

    skip = (page - 1) * size
    return year_service.get_year_ledgers(year, skip=skip, limit=size)


@router.get("/compare", response_model=YearComparisonResponse, summary="年度数据对比")
async def compare_years(
    years: str = Query(..., description="对比年度，用逗号分隔，如：2024,2025"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    年度数据对比

    - **years**: 对比的年度列表，用逗号分隔

    支持2个年度的对比分析
    """
    try:
        year_list = [int(y.strip()) for y in years.split(",")]
        if len(year_list) < 2:
            raise HTTPException(status_code=400, detail="至少需要2个年度进行对比")
        if len(year_list) > 5:
            raise HTTPException(status_code=400, detail="最多支持5个年度对比")
    except ValueError:
        raise HTTPException(status_code=400, detail="年度格式错误")

    year_service = YearService(db)
    return year_service.compare_years(year_list)


# 年度管理相关的统计API
@router.get("/current/summary", summary="获取当前年度概览")
async def get_current_year_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取当前年度概览信息

    包含当前年度的基本统计和最近活动
    """
    from datetime import datetime
    current_year = datetime.now().year

    year_service = YearService(db)

    # 获取当前年度信息
    year_info = year_service.get_year_by_year(current_year)
    if not year_info:
        # 如果当前年度不存在，返回空数据
        return {
            "year": current_year,
            "exists": False,
            "message": f"{current_year}年度尚未创建"
        }

    # 获取统计信息
    stats = year_service.get_year_stats(current_year)

    return {
        "year": current_year,
        "exists": True,
        "year_info": year_info,
        "stats": stats
    }
