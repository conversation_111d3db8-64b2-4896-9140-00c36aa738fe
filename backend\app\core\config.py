"""
应用配置管理
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 服务端口配置
    backend_port: int = 8004

    # 应用基础配置
    app_name: str = "台账管理系统"
    app_version: str = "1.0.0"
    debug: bool = True

    # 数据库配置
    @property
    def database_url(self) -> str:
        # 获取当前文件的目录（backend/app/core）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 向上两级到backend目录，然后找到ledger.db
        db_path = os.path.join(current_dir, "..", "..", "ledger.db")
        db_path = os.path.abspath(db_path)
        return f"sqlite:///{db_path}"

    # 安全配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 120  # 2小时，提供更好的用户体验

    # API配置
    api_v1_prefix: str = "/api/v1"

    # CORS配置
    allowed_origins: str = "http://localhost:3000,http://localhost:5174"

    @property
    def allowed_origins_list(self) -> list:
        """将CORS配置转换为列表"""
        return [origin.strip() for origin in self.allowed_origins.split(',')]

    # 分页配置
    default_page_size: int = 20
    max_page_size: int = 100

    # 文件上传配置
    upload_dir: str = "uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB

    # 日志配置
    log_level: str = "debug"

    class Config:
        env_file = ".env"
        extra = "ignore"  # 忽略额外的环境变量


# 全局配置实例
settings = Settings()
