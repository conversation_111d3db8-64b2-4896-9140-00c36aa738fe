"""
部门管理相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


# 基础模型
class DepartmentBase(BaseModel):
    """部门基础模型"""
    name: str = Field(..., min_length=2, max_length=100, description="部门名称")
    type: str = Field(..., description="部门类型: reporting/local/provincial")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    sort_order: int = Field(default=0, description="排序顺序")
    parent_id: Optional[int] = Field(None, description="上级部门ID")

    @validator('type')
    def validate_type(cls, v):
        valid_types = ['reporting', 'local', 'provincial']
        if v not in valid_types:
            raise ValueError(f'部门类型必须是: {", ".join(valid_types)}')
        return v

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('部门名称不能为空')
        return v.strip()


class DepartmentCreate(DepartmentBase):
    """创建部门模型"""
    pass


class DepartmentUpdate(BaseModel):
    """更新部门模型"""
    name: Optional[str] = Field(None, min_length=2, max_length=100, description="部门名称")
    type: Optional[str] = Field(None, description="部门类型")
    description: Optional[str] = Field(None, max_length=500, description="部门描述")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    parent_id: Optional[int] = Field(None, description="上级部门ID")
    status: Optional[str] = Field(None, description="状态: active/inactive")

    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            valid_types = ['reporting', 'local', 'provincial']
            if v not in valid_types:
                raise ValueError(f'部门类型必须是: {", ".join(valid_types)}')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            valid_statuses = ['active', 'inactive']
            if v not in valid_statuses:
                raise ValueError(f'状态必须是: {", ".join(valid_statuses)}')
        return v


class DepartmentResponse(BaseModel):
    """部门响应模型"""
    id: int
    name: str
    type: str
    status: str
    description: Optional[str]
    sort_order: int
    parent_id: Optional[int]
    user_count: int
    has_children: bool
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class DepartmentListResponse(BaseModel):
    """部门列表响应模型"""
    data: List[DepartmentResponse]
    total: int
    page: int = 1
    limit: int = 20


# 部门变更相关模型
class DepartmentChangeCreate(BaseModel):
    """创建部门变更记录模型"""
    department_id: Optional[int] = Field(None, description="部门ID")
    change_type: str = Field(..., description="变更类型")
    old_name: Optional[str] = Field(None, description="原部门名称")
    new_name: Optional[str] = Field(None, description="新部门名称")
    old_type: Optional[str] = Field(None, description="原部门类型")
    new_type: Optional[str] = Field(None, description="新部门类型")
    reason: Optional[str] = Field(None, description="变更原因")

    @validator('change_type')
    def validate_change_type(cls, v):
        valid_types = ['create', 'update', 'delete', 'merge', 'split']
        if v not in valid_types:
            raise ValueError(f'变更类型必须是: {", ".join(valid_types)}')
        return v


class DepartmentChangeResponse(BaseModel):
    """部门变更响应模型"""
    id: int
    department_id: Optional[int]
    change_type: str
    old_name: Optional[str]
    new_name: Optional[str]
    old_type: Optional[str]
    new_type: Optional[str]
    reason: Optional[str]
    affected_ledgers: int
    affected_users: int
    status: str
    change_summary: str
    created_at: datetime
    creator_name: Optional[str] = None

    class Config:
        from_attributes = True


# 用户调动相关模型
class UserTransferRequest(BaseModel):
    """用户调动请求模型"""
    user_id: int = Field(..., description="用户ID")
    new_department_id: int = Field(..., description="新部门ID")
    change_reason: Optional[str] = Field(None, max_length=500, description="调动原因")
    effective_date: Optional[datetime] = Field(None, description="生效时间")


class BatchUserTransferRequest(BaseModel):
    """批量用户调动请求模型"""
    transfers: List[UserTransferRequest] = Field(..., description="调动列表")
    batch_reason: Optional[str] = Field(None, description="批量调动原因")


class UserTransferResponse(BaseModel):
    """用户调动响应模型"""
    id: int
    user_id: int
    user_name: str
    old_department_id: Optional[int]
    old_department_name: Optional[str]
    new_department_id: Optional[int]
    new_department_name: Optional[str]
    change_reason: Optional[str]
    transfer_summary: str
    effective_date: datetime
    created_at: datetime

    class Config:
        from_attributes = True


# 部门统计相关模型
class DepartmentStats(BaseModel):
    """部门统计模型"""
    department_id: int
    department_name: str
    user_count: int
    active_user_count: int
    ledger_count: int
    completed_ledger_count: int
    completion_rate: float
    last_activity: Optional[datetime]

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class DepartmentUsage(BaseModel):
    """部门使用情况模型"""
    department_id: int
    department_name: str
    total_operations: int
    recent_operations: int
    last_operation: Optional[datetime]
    active_users: int


# 部门合并拆分相关模型
class DepartmentMergeRequest(BaseModel):
    """部门合并请求模型"""
    source_department_ids: List[int] = Field(..., min_items=2, description="源部门ID列表")
    target_name: str = Field(..., min_length=2, max_length=100, description="目标部门名称")
    target_type: str = Field(..., description="目标部门类型")
    merge_reason: Optional[str] = Field(None, description="合并原因")


class DepartmentSplitRequest(BaseModel):
    """部门拆分请求模型"""
    source_department_id: int = Field(..., description="源部门ID")
    new_departments: List[DepartmentCreate] = Field(..., min_items=2, description="新部门列表")
    user_assignments: Dict[int, int] = Field(..., description="用户分配: {user_id: new_dept_id}")
    split_reason: Optional[str] = Field(None, description="拆分原因")


# 影响分析模型
class ChangeImpactAnalysis(BaseModel):
    """变更影响分析模型"""
    department_id: int
    department_name: str
    affected_users: int
    affected_ledgers: int
    affected_children: int
    risk_level: str  # low/medium/high
    warnings: List[str]
    recommendations: List[str]


# 批量操作结果模型
class BatchOperationResult(BaseModel):
    """批量操作结果模型"""
    total: int
    success: int
    failed: int
    results: List[Dict[str, Any]]
    errors: List[str]


# 消息响应模型
class MessageResponse(BaseModel):
    """消息响应模型"""
    message: str
    code: int = 200
    data: Optional[Any] = None
