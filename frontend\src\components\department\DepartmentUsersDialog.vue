<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${department?.name || '部门'} - 用户管理`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="users-management">
      <!-- 操作栏 -->
      <div class="action-bar">
        <el-button type="primary" @click="showTransferDialog = true" v-if="userStore.user?.role === 'admin'">
          <el-icon><Switch /></el-icon>
          用户调动
        </el-button>
        
        <el-button @click="refreshUsers" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>

        <div class="user-count">
          <el-tag type="info">总用户数: {{ users.length }}</el-tag>
          <el-tag type="success">活跃用户: {{ activeUsers.length }}</el-tag>
        </div>
      </div>

      <!-- 用户列表 -->
      <el-table 
        :data="users" 
        v-loading="loading"
        border
        style="width: 100%"
        max-height="400"
      >
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="full_name" label="姓名" width="120" />
        
        <el-table-column prop="email" label="邮箱" min-width="180" />
        
        <el-table-column prop="role" label="角色" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">{{ getRoleText(row.role) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="is_active" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '活跃' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="last_transfer_date" label="最近调动" width="160" align="center">
          <template #default="{ row }">
            {{ row.last_transfer_date ? formatDateTime(row.last_transfer_date) : '无' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="transferUser(row)"
              v-if="userStore.user?.role === 'admin'"
            >
              <el-icon><Switch /></el-icon>
              调动
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && users.length === 0" class="empty-state">
        <el-empty description="该部门暂无用户" />
      </div>
    </div>

    <!-- 用户调动对话框 -->
    <UserTransferDialog 
      v-model="showTransferDialog"
      :user="selectedUser"
      :current-department="department"
      @success="handleTransferSuccess"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Switch, Refresh } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import UserTransferDialog from './UserTransferDialog.vue'

// Store
const userStore = useUserStore()

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  department: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const users = ref([])
const showTransferDialog = ref(false)
const selectedUser = ref(null)

// 计算属性
const activeUsers = computed(() => {
  return users.value.filter(user => user.is_active)
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.department) {
    loadUsers()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const loadUsers = async () => {
  if (!props.department?.id) return
  
  try {
    loading.value = true
    
    const response = await fetch(`/api/v1/departments/${props.department.id}/users`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      users.value = await response.json()
    } else {
      ElMessage.error('获取部门用户失败')
    }
  } catch (error) {
    console.error('获取部门用户失败:', error)
    ElMessage.error('获取部门用户失败')
  } finally {
    loading.value = false
  }
}

const refreshUsers = () => {
  loadUsers()
}

const transferUser = (user) => {
  selectedUser.value = user
  showTransferDialog.value = true
}

const handleTransferSuccess = () => {
  showTransferDialog.value = false
  selectedUser.value = null
  loadUsers() // 刷新用户列表
}

const handleClose = () => {
  dialogVisible.value = false
}

const getRoleType = (role) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'supervisor': return 'warning'
    case 'manager': return 'primary'
    case 'user': return 'success'
    default: return 'info'
  }
}

const getRoleText = (role) => {
  switch (role) {
    case 'admin': return '管理员'
    case 'supervisor': return '主管'
    case 'manager': return '管理部门'
    case 'user': return '基层用户'
    default: return '未知'
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.users-management {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.user-count {
  display: flex;
  gap: 8px;
}

.empty-state {
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
