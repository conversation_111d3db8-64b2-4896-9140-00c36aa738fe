/**
 * 用户管理相关API调用
 */
import { request } from './index'
import type { UserInfo } from './auth'

// 用户管理接口类型定义
export interface UserCreateRequest {
  username: string
  password: string
  full_name: string
  email?: string
  phone?: string
  department_id?: number
  role: string
  is_active: boolean
}

export interface UserUpdateRequest {
  full_name?: string
  email?: string
  phone?: string
  department_id?: number
  role?: string
  is_active?: boolean
}

export interface UserListResponse {
  users: UserInfo[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export interface UserStats {
  total_users: number
  active_users: number
  admin_count: number
  manager_count: number
  user_count: number
  recent_logins: number
}

// 用户管理API
export const userAPI = {
  // 获取用户列表
  getUsers: (params: {
    page?: number
    limit?: number
    search?: string
    department?: string
    role?: string
    is_active?: boolean
  } = {}): Promise<UserListResponse> => {
    return request.get('/auth/users', { params })
  },

  // 创建用户
  createUser: (data: UserCreateRequest): Promise<UserInfo> => {
    return request.post('/auth/users', data)
  },

  // 获取用户详情
  getUser: (userId: number): Promise<UserInfo> => {
    return request.get(`/auth/users/${userId}`)
  },

  // 更新用户信息
  updateUser: (userId: number, data: UserUpdateRequest): Promise<UserInfo> => {
    return request.put(`/auth/users/${userId}`, data)
  },

  // 删除用户
  deleteUser: (userId: number): Promise<{ message: string }> => {
    return request.delete(`/auth/users/${userId}`)
  },

  // 分配用户角色
  assignRole: (userId: number, role: string): Promise<UserInfo> => {
    return request.put(`/auth/users/${userId}/role`, null, {
      params: { role }
    })
  },

  // 更新用户状态
  updateStatus: (userId: number, isActive: boolean): Promise<UserInfo> => {
    return request.put(`/auth/users/${userId}/status`, null, {
      params: { is_active: isActive }
    })
  },

  // 获取用户统计
  getUserStats: (): Promise<UserStats> => {
    return request.get('/auth/users/stats/overview')
  }
}

// 用户角色和权限常量
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user'
} as const

export const ROLE_LABELS = {
  [USER_ROLES.ADMIN]: '系统管理员',
  [USER_ROLES.MANAGER]: '管理部门',
  [USER_ROLES.USER]: '基层用户'
} as const

export const ROLE_DESCRIPTIONS = {
  [USER_ROLES.ADMIN]: '拥有系统所有权限，可以管理用户和系统配置',
  [USER_ROLES.MANAGER]: '可以管理标签和批注，导出相关数据',
  [USER_ROLES.USER]: '只能录入和查看本部门数据'
} as const

// 权限检查工具
export const permissionUtils = {
  // 检查是否为管理员
  isAdmin: (role: string): boolean => {
    return role === USER_ROLES.ADMIN
  },

  // 检查是否为管理部门
  isManager: (role: string): boolean => {
    return role === USER_ROLES.MANAGER
  },

  // 检查是否为基层用户
  isUser: (role: string): boolean => {
    return role === USER_ROLES.USER
  },

  // 检查是否可以管理用户
  canManageUsers: (role: string): boolean => {
    return role === USER_ROLES.ADMIN
  },

  // 检查是否可以管理标签
  canManageTags: (role: string): boolean => {
    return [USER_ROLES.ADMIN, USER_ROLES.MANAGER].includes(role as any)
  },

  // 检查是否可以编辑序号
  canEditSequence: (role: string): boolean => {
    return role === USER_ROLES.ADMIN
  },

  // 检查是否可以导出数据
  canExportData: (role: string): boolean => {
    return [USER_ROLES.ADMIN, USER_ROLES.MANAGER].includes(role as any)
  },

  // 获取角色标签
  getRoleLabel: (role: string): string => {
    return ROLE_LABELS[role as keyof typeof ROLE_LABELS] || role
  },

  // 获取角色描述
  getRoleDescription: (role: string): string => {
    return ROLE_DESCRIPTIONS[role as keyof typeof ROLE_DESCRIPTIONS] || ''
  }
}
