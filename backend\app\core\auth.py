"""
认证核心逻辑
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.orm import Session
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from ..core.config import settings
from ..core.database import get_db
from ..models.user import User, UserSession


# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer 认证
security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """生成密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError:
        return None


def get_user_from_token(db: Session, token: str) -> Optional[User]:
    """从令牌获取用户"""
    payload = verify_token(token)
    if payload is None:
        return None

    user_id: int = payload.get("sub")
    if user_id is None:
        return None

    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    return user


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    token = credentials.credentials

    # 验证令牌
    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 获取用户ID
    user_id: int = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌格式",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 查询用户
    user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """验证用户身份"""
    user = db.query(User).filter(User.username == username, User.is_active == True).first()
    if not user:
        return None
    
    if not verify_password(password, user.password_hash):
        return None
    
    return user


def create_user_session(
    db: Session, 
    user: User, 
    token: str, 
    expires_at: datetime,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> UserSession:
    """创建用户会话"""
    # 生成token哈希用于存储
    token_hash = get_password_hash(token)
    
    session = UserSession(
        user_id=user.id,
        token_hash=token_hash,
        expires_at=expires_at,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session


def invalidate_user_session(db: Session, token: str) -> bool:
    """使用户会话失效"""
    # 这里需要通过token找到对应的session并设为无效
    # 由于我们存储的是token_hash，需要遍历查找或使用其他方式
    # 简化实现：通过token解析用户ID，然后使该用户的所有session失效
    payload = verify_token(token)
    if payload is None:
        return False
    
    user_id = payload.get("sub")
    if user_id is None:
        return False
    
    # 使该用户的所有活跃会话失效
    db.query(UserSession).filter(
        UserSession.user_id == user_id,
        UserSession.is_active == True
    ).update({"is_active": False})
    
    db.commit()
    return True


def cleanup_expired_sessions(db: Session) -> int:
    """清理过期的会话"""
    current_time = datetime.utcnow()
    
    # 查找过期的会话
    expired_sessions = db.query(UserSession).filter(
        UserSession.expires_at < current_time,
        UserSession.is_active == True
    )
    
    count = expired_sessions.count()
    
    # 设置为无效
    expired_sessions.update({"is_active": False})
    db.commit()
    
    return count


def update_user_login_info(db: Session, user: User) -> None:
    """更新用户登录信息"""
    user.last_login = datetime.utcnow()
    user.login_count = (user.login_count or 0) + 1
    db.commit()


def check_user_permission(user: User, permission: str) -> bool:
    """检查用户权限"""
    from ..models.user import has_permission
    return has_permission(user.role, permission)


def require_permission(permission: str):
    """权限装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 这里需要从请求上下文中获取当前用户
            # 具体实现在依赖注入中处理
            return func(*args, **kwargs)
        return wrapper
    return decorator


class AuthenticationError(Exception):
    """认证错误"""
    pass


class AuthorizationError(Exception):
    """授权错误"""
    pass


class TokenExpiredError(AuthenticationError):
    """令牌过期错误"""
    pass


class InvalidTokenError(AuthenticationError):
    """无效令牌错误"""
    pass


class InsufficientPermissionError(AuthorizationError):
    """权限不足错误"""
    pass
