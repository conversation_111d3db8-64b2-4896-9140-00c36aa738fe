"""
年度管理相关数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import Base


class Year(Base):
    """年度管理模型"""
    
    __tablename__ = "years"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 基础字段
    year = Column(Integer, unique=True, nullable=False, index=True, comment="年度")
    status = Column(String(20), default='active', nullable=False, comment="状态: active, archived")
    
    # 统计字段
    total_ledgers = Column(Integer, default=0, comment="台账总数")
    completed_ledgers = Column(Integer, default=0, comment="已完成台账数")
    in_progress_ledgers = Column(Integer, default=0, comment="进行中台账数")
    draft_ledgers = Column(Integer, default=0, comment="草稿台账数")
    completed_ongoing_ledgers = Column(Integer, default=0, comment="已完成并持续推进台账数")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    archived_at = Column(DateTime, nullable=True, comment="归档时间")
    
    # 关联字段
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建人ID")
    
    # 关系
    creator = relationship("User", back_populates="created_years", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<Year(year={self.year}, status={self.status})>"
    
    @property
    def completion_rate(self) -> float:
        """完成率计算"""
        if self.total_ledgers == 0:
            return 0.0
        return round((self.completed_ledgers / self.total_ledgers) * 100, 1)
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "year": self.year,
            "status": self.status,
            "total_ledgers": self.total_ledgers,
            "completed_ledgers": self.completed_ledgers,
            "in_progress_ledgers": self.in_progress_ledgers,
            "draft_ledgers": self.draft_ledgers,
            "completion_rate": self.completion_rate,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "archived_at": self.archived_at.isoformat() if self.archived_at else None
        }


class Backup(Base):
    """备份管理模型"""
    
    __tablename__ = "backups"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 基础字段
    backup_name = Column(String(200), nullable=False, comment="备份名称")
    backup_type = Column(String(50), default='manual', comment="备份类型: manual, auto")
    backup_scope = Column(String(50), default='full', comment="备份范围: full, year, partial")
    target_year = Column(Integer, nullable=True, comment="目标年度")
    
    # 文件信息
    file_path = Column(String(500), nullable=True, comment="备份文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小(字节)")
    checksum = Column(String(64), nullable=True, comment="文件校验和")
    
    # 状态和描述
    status = Column(String(20), default='completed', comment="状态: creating, completed, failed")
    description = Column(Text, nullable=True, comment="备份描述")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    
    # 关联字段
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建人ID")
    
    # 关系
    creator = relationship("User", back_populates="created_backups", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<Backup(name={self.backup_name}, type={self.backup_type})>"
    
    @property
    def file_size_mb(self) -> float:
        """文件大小(MB)"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "backup_name": self.backup_name,
            "backup_type": self.backup_type,
            "backup_scope": self.backup_scope,
            "target_year": self.target_year,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_size_mb": self.file_size_mb,
            "checksum": self.checksum,
            "status": self.status,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class Archive(Base):
    """归档管理模型"""
    
    __tablename__ = "archives"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 基础字段
    year = Column(Integer, nullable=False, index=True, comment="归档年度")
    archive_type = Column(String(50), default='year', comment="归档类型: year, manual")
    
    # 文件信息
    file_path = Column(String(500), nullable=True, comment="归档文件路径")
    file_size = Column(Integer, nullable=True, comment="文件大小")
    checksum = Column(String(64), nullable=True, comment="文件校验和")
    ledger_count = Column(Integer, nullable=True, comment="归档台账数量")
    
    # 状态字段
    status = Column(String(20), default='completed', comment="状态: creating, completed, failed")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    
    # 关联字段
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建人ID")
    
    # 关系
    creator = relationship("User", back_populates="created_archives", foreign_keys=[created_by])
    
    def __repr__(self):
        return f"<Archive(year={self.year}, type={self.archive_type})>"
    
    @property
    def file_size_mb(self) -> float:
        """文件大小(MB)"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0.0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "year": self.year,
            "archive_type": self.archive_type,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_size_mb": self.file_size_mb,
            "checksum": self.checksum,
            "ledger_count": self.ledger_count,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
