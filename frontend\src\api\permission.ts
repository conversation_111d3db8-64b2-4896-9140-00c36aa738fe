/**
 * 权限管理API
 */
import api from './index'

export interface Permission {
  id: number
  name: string
  code: string
  description: string
  module: string
  created_at: string
}

export interface Role {
  id: number
  name: string
  code: string
  description: string
  permissions: Permission[]
  created_at: string
}

export interface UserRole {
  user_id: number
  role_id: number
  granted_by: number
  granted_at: string
}

export interface PermissionMatrix {
  module: string
  permissions: Array<{
    name: string
    code: string
    roles: Record<string, boolean>
  }>
}

export interface RolePermissionUpdate {
  role_id: number
  permission_codes: string[]
}

export interface UserRoleUpdate {
  user_id: number
  role_codes: string[]
}

// 权限管理
export const getPermissions = async (): Promise<Permission[]> => {
  const response = await api.get('/permissions')
  return response.data
}

export const getPermissionsByModule = async (module: string): Promise<Permission[]> => {
  const response = await api.get(`/permissions/module/${module}`)
  return response.data
}

// 角色管理
export const getRoles = async (): Promise<Role[]> => {
  const response = await api.get('/roles')
  return response.data
}

export const createRole = async (data: {
  name: string
  code: string
  description: string
  permission_codes: string[]
}): Promise<Role> => {
  const response = await api.post('/roles', data)
  return response.data
}

export const updateRole = async (id: number, data: {
  name?: string
  description?: string
  permission_codes?: string[]
}): Promise<Role> => {
  const response = await api.put(`/roles/${id}`, data)
  return response.data
}

export const deleteRole = async (id: number): Promise<void> => {
  await api.delete(`/roles/${id}`)
}

// 权限矩阵
export const getPermissionMatrix = async (): Promise<PermissionMatrix[]> => {
  const response = await api.get('/permissions/matrix')
  return response.data
}

export const updateRolePermissions = async (data: RolePermissionUpdate): Promise<void> => {
  await api.put('/permissions/role-permissions', data)
}

// 用户角色管理
export const getUserRoles = async (userId: number): Promise<Role[]> => {
  const response = await api.get(`/users/${userId}/roles`)
  return response.data
}

export const updateUserRoles = async (data: UserRoleUpdate): Promise<void> => {
  await api.put('/permissions/user-roles', data)
}

export const grantUserRole = async (userId: number, roleId: number): Promise<void> => {
  await api.post(`/users/${userId}/roles/${roleId}`)
}

export const revokeUserRole = async (userId: number, roleId: number): Promise<void> => {
  await api.delete(`/users/${userId}/roles/${roleId}`)
}

// 权限检查
export const checkUserPermission = async (userId: number, permissionCode: string): Promise<boolean> => {
  const response = await api.get(`/permissions/check`, {
    params: { user_id: userId, permission_code: permissionCode }
  })
  return response.data.has_permission
}

export const getUserPermissions = async (userId: number): Promise<Permission[]> => {
  const response = await api.get(`/users/${userId}/permissions`)
  return response.data
}

// 批量操作
export const batchUpdateUserRoles = async (updates: UserRoleUpdate[]): Promise<void> => {
  await api.post('/permissions/batch-user-roles', { updates })
}

export const batchUpdateRolePermissions = async (updates: RolePermissionUpdate[]): Promise<void> => {
  await api.post('/permissions/batch-role-permissions', { updates })
}
