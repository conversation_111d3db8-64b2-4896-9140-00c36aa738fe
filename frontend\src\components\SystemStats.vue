<template>
  <div class="system-stats">
    <div class="stats-header">
      <h3>系统统计</h3>
      <p>查看系统用户和数据统计信息</p>
    </div>

    <div class="stats-content" v-loading="loading">
      <!-- 用户统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#409eff"><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.total_users }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#67c23a"><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.active_users }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#e6a23c"><Star /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.admin_count }}</div>
            <div class="stat-label">管理员</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#f56c6c"><Avatar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.manager_count }}</div>
            <div class="stat-label">管理部门</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#909399"><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.user_count }}</div>
            <div class="stat-label">基层用户</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#409eff"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ userStats.recent_logins }}</div>
            <div class="stat-label">近期登录</div>
          </div>
        </div>
      </div>

      <!-- 角色分布图表 -->
      <div class="chart-section">
        <div class="chart-card">
          <h4>用户角色分布</h4>
          <div class="role-distribution">
            <div class="role-item">
              <div class="role-bar">
                <div
                  class="role-progress admin"
                  :style="{ width: getPercentage(userStats.admin_count) + '%' }"
                ></div>
              </div>
              <div class="role-info">
                <span class="role-name">系统管理员</span>
                <span class="role-count">{{ userStats.admin_count }}</span>
              </div>
            </div>

            <div class="role-item">
              <div class="role-bar">
                <div
                  class="role-progress manager"
                  :style="{ width: getPercentage(userStats.manager_count) + '%' }"
                ></div>
              </div>
              <div class="role-info">
                <span class="role-name">管理部门</span>
                <span class="role-count">{{ userStats.manager_count }}</span>
              </div>
            </div>

            <div class="role-item">
              <div class="role-bar">
                <div
                  class="role-progress user"
                  :style="{ width: getPercentage(userStats.user_count) + '%' }"
                ></div>
              </div>
              <div class="role-info">
                <span class="role-name">基层用户</span>
                <span class="role-count">{{ userStats.user_count }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="system-info-card">
          <h4>系统信息</h4>
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">系统版本</span>
              <span class="info-value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库</span>
              <span class="info-value">SQLite3</span>
            </div>
            <div class="info-item">
              <span class="info-label">后端框架</span>
              <span class="info-value">FastAPI</span>
            </div>
            <div class="info-item">
              <span class="info-label">前端框架</span>
              <span class="info-value">Vue3 + Element Plus</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后更新</span>
              <span class="info-value">{{ formatDateTime(new Date()) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限矩阵概览 -->
      <div class="permission-overview">
        <h4>权限矩阵概览</h4>
        <div class="permission-summary">
          <div class="permission-item">
            <el-icon><Lock /></el-icon>
            <span>序号编辑：仅管理员</span>
          </div>
          <div class="permission-item">
            <el-icon><Edit /></el-icon>
            <span>台账录入：所有用户（限本部门）</span>
          </div>
          <div class="permission-item">
            <el-icon><View /></el-icon>
            <span>数据查看：根据角色和部门限制</span>
          </div>
          <div class="permission-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>批注功能：管理部门及以上</span>
          </div>
          <div class="permission-item">
            <el-icon><Download /></el-icon>
            <span>导出功能：管理部门及以上</span>
          </div>
          <div class="permission-item">
            <el-icon><PriceTag /></el-icon>
            <span>标签管理：管理部门及以上</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, UserFilled, Star, Avatar, Clock, Lock, Edit,
  View, ChatDotRound, Download, PriceTag
} from '@element-plus/icons-vue'
import { userAPI, type UserStats } from '@/api/user'

// 状态
const loading = ref(false)

// 用户统计数据
const userStats = reactive<UserStats>({
  total_users: 0,
  active_users: 0,
  admin_count: 0,
  manager_count: 0,
  user_count: 0,
  recent_logins: 0
})

// 获取用户统计数据
const getUserStats = async () => {
  try {
    loading.value = true
    const response = await userAPI.getUserStats()
    Object.assign(userStats, response)
  } catch (error) {
    console.error('Get user stats error:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 计算百分比
const getPercentage = (count: number): number => {
  if (userStats.total_users === 0) return 0
  return Math.round((count / userStats.total_users) * 100)
}

// 格式化日期时间
const formatDateTime = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  getUserStats()
})
</script>

<style scoped>
.system-stats {
  padding: 0;
}

.stats-header {
  margin-bottom: 20px;
}

.stats-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.stats-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 50%;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.chart-card,
.system-info-card {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
}

.chart-card h4,
.system-info-card h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.role-distribution {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.role-bar {
  flex: 1;
  height: 20px;
  background: #f5f7fa;
  border-radius: 10px;
  overflow: hidden;
}

.role-progress {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.role-progress.admin {
  background: linear-gradient(90deg, #409eff, #66b1ff);
}

.role-progress.manager {
  background: linear-gradient(90deg, #e6a23c, #ebb563);
}

.role-progress.user {
  background: linear-gradient(90deg, #909399, #a6a9ad);
}

.role-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 120px;
}

.role-name {
  font-size: 14px;
  color: #606266;
}

.role-count {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #909399;
}

.info-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.permission-overview {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
}

.permission-overview h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.permission-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
  font-size: 14px;
  color: #606266;
}

.permission-item .el-icon {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .chart-section {
    grid-template-columns: 1fr;
  }

  .permission-summary {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
  }

  .stat-value {
    font-size: 20px;
  }
}
</style>
