"""
年度管理服务
"""
import os
import json
import hashlib
import shutil
from datetime import datetime
from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc

from ..models.year import Year, Archive
from ..models.ledger import Ledger
from ..models.department import Department
from ..models.tag import Tag
from ..models.user import User
from ..schemas.year import (
    YearCreate, YearUpdate, YearResponse, YearListResponse,
    YearStatsResponse, YearComparisonResponse, YearCreateResponse
)


class YearService:
    """年度管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_years(self, skip: int = 0, limit: int = 100) -> YearListResponse:
        """获取年度列表"""
        # 查询年度列表，按年度倒序
        query = self.db.query(Year).order_by(desc(Year.year))
        total = query.count()
        years = query.offset(skip).limit(limit).all()

        # 转换为响应格式
        year_responses = []
        for year in years:
            total_ledgers = year.total_ledgers or 0
            completed_ledgers = year.completed_ledgers or 0
            completion_rate = 0.0
            if total_ledgers > 0:
                completion_rate = round((completed_ledgers / total_ledgers) * 100, 1)

            year_data = {
                "id": year.id,
                "year": year.year,
                "status": year.status,
                "total_ledgers": total_ledgers,
                "completed_ledgers": completed_ledgers,
                "in_progress_ledgers": year.in_progress_ledgers or 0,
                "draft_ledgers": year.draft_ledgers or 0,
                "completed_ongoing_ledgers": year.completed_ongoing_ledgers or 0,
                "completion_rate": completion_rate,
                "created_at": year.created_at,
                "archived_at": year.archived_at,
                "created_by": year.created_by
            }
            year_responses.append(YearResponse(**year_data))

        return YearListResponse(
            data=year_responses,
            total=total
        )
    
    def get_year_by_year(self, year: int) -> Optional[YearResponse]:
        """根据年度获取年度信息"""
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if year_obj:
            total_ledgers = year_obj.total_ledgers or 0
            completed_ledgers = year_obj.completed_ledgers or 0
            completion_rate = 0.0
            if total_ledgers > 0:
                completion_rate = round((completed_ledgers / total_ledgers) * 100, 1)

            year_data = {
                "id": year_obj.id,
                "year": year_obj.year,
                "status": year_obj.status,
                "total_ledgers": total_ledgers,
                "completed_ledgers": completed_ledgers,
                "in_progress_ledgers": year_obj.in_progress_ledgers or 0,
                "draft_ledgers": year_obj.draft_ledgers or 0,
                "completed_ongoing_ledgers": year_obj.completed_ongoing_ledgers or 0,
                "completion_rate": completion_rate,
                "created_at": year_obj.created_at,
                "archived_at": year_obj.archived_at,
                "created_by": year_obj.created_by
            }
            return YearResponse(**year_data)
        return None
    
    def create_year(self, year_data: YearCreate, current_user_id: int) -> YearCreateResponse:
        """创建新年度"""
        # 检查年度是否已存在
        existing_year = self.db.query(Year).filter(Year.year == year_data.year).first()
        if existing_year:
            raise ValueError(f"年度 {year_data.year} 已存在")
        
        # 创建年度记录
        new_year = Year(
            year=year_data.year,
            status="active",
            created_by=current_user_id
        )
        self.db.add(new_year)
        self.db.flush()  # 获取ID
        
        # 继承配置
        inherited_items = {}
        if year_data.inherit_config:
            inherited_items = self._inherit_configuration(year_data.year)
        
        # 归档上一年度
        archive_info = None
        if year_data.archive_previous:
            previous_year = year_data.year - 1
            archive_info = self._archive_year(previous_year, current_user_id)
        
        # 初始化统计数据
        self._initialize_year_stats(year_data.year)
        
        self.db.commit()
        
        return YearCreateResponse(
            id=new_year.id,
            year=new_year.year,
            status=new_year.status,
            inherited_items=inherited_items,
            archive_info=archive_info,
            message=f"{year_data.year}年度创建成功"
        )
    
    def update_year(self, year: int, year_data: YearUpdate) -> Optional[YearResponse]:
        """更新年度信息"""
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if not year_obj:
            return None
        
        # 更新字段
        for field, value in year_data.dict(exclude_unset=True).items():
            setattr(year_obj, field, value)
        
        self.db.commit()
        self.db.refresh(year_obj)
        
        return YearResponse.from_orm(year_obj)
    
    def delete_year(self, year: int) -> bool:
        """删除年度"""
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if not year_obj:
            return False
        
        # 检查是否有关联的台账数据
        ledger_count = self.db.query(Ledger).filter(Ledger.year == year).count()
        if ledger_count > 0:
            raise ValueError(f"年度 {year} 存在 {ledger_count} 条台账数据，无法删除")
        
        self.db.delete(year_obj)
        self.db.commit()
        return True
    
    def get_year_stats(self, year: int) -> Optional[YearStatsResponse]:
        """获取年度统计"""
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if not year_obj:
            return None
        
        # 基础统计
        total_ledgers = year_obj.total_ledgers or 0
        completed_ledgers = year_obj.completed_ledgers or 0
        completion_rate = 0.0
        if total_ledgers > 0:
            completion_rate = round((completed_ledgers / total_ledgers) * 100, 1)

        stats = {
            "year": year,
            "total_ledgers": total_ledgers,
            "completed_ledgers": completed_ledgers,
            "in_progress_ledgers": year_obj.in_progress_ledgers or 0,
            "draft_ledgers": year_obj.draft_ledgers or 0,
            "completion_rate": completion_rate
        }
        
        # 部门统计
        try:
            department_stats = self._get_department_stats(year)
            stats["department_stats"] = department_stats
        except Exception:
            stats["department_stats"] = {}

        # 月度进度（模拟数据）
        try:
            monthly_progress = self._get_monthly_progress(year)
            stats["monthly_progress"] = monthly_progress
        except Exception:
            stats["monthly_progress"] = []

        # 标签分布
        try:
            tag_distribution = self._get_tag_distribution(year)
            stats["tag_distribution"] = tag_distribution
        except Exception:
            stats["tag_distribution"] = {}

        return YearStatsResponse(**stats)
    
    def get_year_ledgers(self, year: int, skip: int = 0, limit: int = 20) -> Dict[str, Any]:
        """获取年度台账数据"""
        # 查询台账数据
        query = self.db.query(Ledger).filter(Ledger.year == year)
        total = query.count()
        ledgers = query.offset(skip).limit(limit).all()
        
        # 转换为字典格式
        ledger_data = []
        for ledger in ledgers:
            ledger_dict = {
                "id": ledger.id,
                "sequence_number": ledger.sequence_number,
                "work_item": ledger.work_item,
                "measures": ledger.measures,
                "progress": ledger.progress,
                "effectiveness": ledger.effectiveness,
                "responsible_dept": ledger.responsible_dept,
                "counterpart_depts": json.loads(ledger.counterpart_depts) if ledger.counterpart_depts else [],
                "tags": json.loads(ledger.tags) if ledger.tags else [],
                "remarks": ledger.remarks,
                "status": ledger.status,
                "year": ledger.year,
                "created_at": ledger.created_at.isoformat() if ledger.created_at else None,
                "updated_at": ledger.updated_at.isoformat() if ledger.updated_at else None
            }
            ledger_data.append(ledger_dict)
        
        return {
            "data": ledger_data,
            "total": total,
            "page": (skip // limit) + 1,
            "limit": limit,
            "readonly": True  # 历史数据只读
        }
    
    def compare_years(self, years: List[int]) -> YearComparisonResponse:
        """年度对比"""
        comparison = {}
        
        for year in years:
            year_obj = self.db.query(Year).filter(Year.year == year).first()
            if year_obj:
                comparison[str(year)] = {
                    "total_ledgers": year_obj.total_ledgers,
                    "completed_ledgers": year_obj.completed_ledgers,
                    "completion_rate": year_obj.completion_rate
                }
        
        # 计算趋势
        trends = {}
        if len(years) == 2:
            year1, year2 = sorted(years)
            if str(year1) in comparison and str(year2) in comparison:
                data1 = comparison[str(year1)]
                data2 = comparison[str(year2)]
                
                # 台账增长率
                if data1["total_ledgers"] > 0:
                    ledger_growth = ((data2["total_ledgers"] - data1["total_ledgers"]) / data1["total_ledgers"]) * 100
                    trends["ledger_growth"] = round(ledger_growth, 1)
                
                # 完成率变化
                completion_change = data2["completion_rate"] - data1["completion_rate"]
                trends["completion_change"] = round(completion_change, 1)
        
        return YearComparisonResponse(
            comparison=comparison,
            trends=trends
        )
    
    def _inherit_configuration(self, year: int) -> Dict[str, int]:
        """继承配置信息"""
        inherited_items = {}
        
        # 统计继承的项目数量
        departments_count = self.db.query(Department).count()
        tags_count = self.db.query(Tag).count()
        users_count = self.db.query(User).count()
        
        inherited_items = {
            "departments": departments_count,
            "tags": tags_count,
            "users": users_count
        }
        
        return inherited_items
    
    def _archive_year(self, year: int, current_user_id: int) -> Dict[str, Any]:
        """归档年度数据"""
        # 获取年度台账数据
        ledgers = self.db.query(Ledger).filter(Ledger.year == year).all()
        
        # 创建归档文件名
        archive_filename = f"archive_{year}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        archive_path = os.path.join("data", "archives", str(year))
        
        # 确保目录存在
        os.makedirs(archive_path, exist_ok=True)
        
        # 导出数据
        archive_data = {
            "year": year,
            "archived_at": datetime.now().isoformat(),
            "ledger_count": len(ledgers),
            "ledgers": [
                {
                    "id": ledger.id,
                    "sequence_number": ledger.sequence_number,
                    "work_item": ledger.work_item,
                    "measures": ledger.measures,
                    "progress": ledger.progress,
                    "effectiveness": ledger.effectiveness,
                    "responsible_dept": ledger.responsible_dept,
                    "counterpart_depts": json.loads(ledger.counterpart_depts) if ledger.counterpart_depts else [],
                    "tags": json.loads(ledger.tags) if ledger.tags else [],
                    "remarks": ledger.remarks,
                    "status": ledger.status,
                    "created_at": ledger.created_at.isoformat() if ledger.created_at else None,
                    "updated_at": ledger.updated_at.isoformat() if ledger.updated_at else None
                }
                for ledger in ledgers
            ]
        }
        
        # 写入文件
        full_path = os.path.join(archive_path, archive_filename)
        with open(full_path, 'w', encoding='utf-8') as f:
            json.dump(archive_data, f, ensure_ascii=False, indent=2)
        
        # 计算文件大小和校验和
        file_size = os.path.getsize(full_path)
        checksum = self._calculate_checksum(full_path)
        
        # 创建归档记录
        archive = Archive(
            year=year,
            archive_type="year",
            file_path=full_path,
            file_size=file_size,
            checksum=checksum,
            ledger_count=len(ledgers),
            created_by=current_user_id
        )
        self.db.add(archive)
        
        # 更新年度状态
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if year_obj:
            year_obj.status = "archived"
            year_obj.archived_at = datetime.now()
        
        return {
            "archived_year": year,
            "archive_file": archive_filename,
            "ledger_count": len(ledgers),
            "file_size": file_size
        }
    
    def _initialize_year_stats(self, year: int):
        """初始化年度统计数据"""
        # 统计当前年度的台账数据
        total_ledgers = self.db.query(Ledger).filter(Ledger.year == year).count()
        completed_ledgers = self.db.query(Ledger).filter(
            and_(Ledger.year == year, Ledger.status == "completed")
        ).count()
        in_progress_ledgers = self.db.query(Ledger).filter(
            and_(Ledger.year == year, Ledger.status == "in_progress")
        ).count()
        draft_ledgers = self.db.query(Ledger).filter(
            and_(Ledger.year == year, Ledger.status == "draft")
        ).count()
        
        # 更新年度统计
        year_obj = self.db.query(Year).filter(Year.year == year).first()
        if year_obj:
            year_obj.total_ledgers = total_ledgers
            year_obj.completed_ledgers = completed_ledgers
            year_obj.in_progress_ledgers = in_progress_ledgers
            year_obj.draft_ledgers = draft_ledgers
    
    def _get_department_stats(self, year: int) -> Dict[str, Dict[str, int]]:
        """获取部门统计"""
        # 查询各部门的台账统计
        stats = {}
        
        # 获取所有部门
        departments = self.db.query(Department).all()
        
        for dept in departments:
            total = self.db.query(Ledger).filter(
                and_(Ledger.year == year, Ledger.responsible_dept == dept.name)
            ).count()
            
            completed = self.db.query(Ledger).filter(
                and_(
                    Ledger.year == year,
                    Ledger.responsible_dept == dept.name,
                    Ledger.status == "completed"
                )
            ).count()
            
            if total > 0:
                stats[dept.name] = {
                    "total": total,
                    "completed": completed
                }
        
        return stats
    
    def _get_monthly_progress(self, year: int) -> List[int]:
        """获取月度进度（模拟数据）"""
        # 这里返回模拟的月度进度数据
        # 实际应用中可以根据台账的创建时间统计
        return [12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45, 48]
    
    def _get_tag_distribution(self, year: int) -> Dict[str, int]:
        """获取标签分布"""
        # 查询标签使用统计
        distribution = {}
        
        # 获取所有台账的标签
        ledgers = self.db.query(Ledger).filter(Ledger.year == year).all()
        
        for ledger in ledgers:
            if ledger.tags:
                tags = json.loads(ledger.tags)
                for tag in tags:
                    distribution[tag] = distribution.get(tag, 0) + 1
        
        return distribution
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
