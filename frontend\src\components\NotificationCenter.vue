<template>
  <div class="notification-center">
    <!-- 通知铃铛图标 -->
    <el-dropdown @command="handleCommand" trigger="click" placement="bottom-end">
      <div class="notification-trigger">
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
          <el-icon size="20" class="notification-icon">
            <Bell />
          </el-icon>
        </el-badge>
      </div>
      
      <template #dropdown>
        <el-dropdown-menu class="notification-dropdown">
          <!-- 头部 -->
          <div class="notification-header">
            <div class="header-left">
              <span class="title">通知中心</span>
              <span class="count">({{ unreadCount }})</span>
            </div>
            <div class="header-right">
              <el-button 
                text 
                size="small" 
                @click="markAllRead"
                :disabled="unreadCount === 0"
              >
                全部已读
              </el-button>
            </div>
          </div>
          
          <!-- 通知列表 -->
          <div class="notification-list" v-loading="loading">
            <div v-if="notifications.length === 0" class="empty-state">
              <el-icon size="48" color="#c0c4cc"><Bell /></el-icon>
              <p>暂无通知</p>
            </div>
            
            <div 
              v-for="notification in notifications" 
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.is_read, 'pinned': notification.is_pinned }"
              @click="handleNotificationClick(notification)"
            >
              <!-- 通知图标 -->
              <div class="notification-icon-wrapper">
                <el-icon 
                  :size="16" 
                  :color="getNotificationColor(notification)"
                  class="notification-type-icon"
                >
                  <component :is="getNotificationIcon(notification)" />
                </el-icon>
                <div v-if="notification.is_pinned" class="pin-indicator">
                  <el-icon size="12"><Star /></el-icon>
                </div>
              </div>
              
              <!-- 通知内容 -->
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-text">{{ notification.content }}</div>
                <div class="notification-meta">
                  <span class="time">{{ formatTime(notification.created_at) }}</span>
                  <span v-if="notification.action_text" class="action">
                    {{ notification.action_text }}
                  </span>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="notification-actions">
                <el-dropdown @command="(cmd) => handleNotificationAction(cmd, notification)" trigger="click">
                  <el-icon class="action-trigger"><MoreFilled /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="!notification.is_read" command="read">
                        标记已读
                      </el-dropdown-item>
                      <el-dropdown-item 
                        :command="notification.is_pinned ? 'unpin' : 'pin'"
                      >
                        {{ notification.is_pinned ? '取消置顶' : '置顶' }}
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
          
          <!-- 底部 -->
          <div class="notification-footer">
            <el-button text @click="viewAllNotifications">
              查看全部通知
            </el-button>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 通知详情对话框 -->
    <NotificationDetailDialog 
      v-model="showDetailDialog"
      :notification="selectedNotification"
      @updated="loadNotifications"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bell, Star, MoreFilled, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'
import NotificationDetailDialog from './NotificationDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const showDetailDialog = ref(false)
const selectedNotification = ref(null)

// 计算属性
const hasNotifications = computed(() => notifications.value.length > 0)

// 生命周期
onMounted(() => {
  loadNotifications()
  // 设置定时刷新
  setInterval(loadNotifications, 30000) // 30秒刷新一次
})

// 方法
const loadNotifications = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/v1/reminders/notifications?limit=10', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      notifications.value = data.items
      unreadCount.value = data.unread_count
    }
  } catch (error) {
    console.error('加载通知失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'viewAll':
      viewAllNotifications()
      break
    case 'settings':
      openNotificationSettings()
      break
  }
}

const handleNotificationClick = async (notification: any) => {
  // 标记为已读
  if (!notification.is_read) {
    await markNotificationRead(notification.id)
  }
  
  // 如果有操作链接，跳转
  if (notification.action_url) {
    window.location.href = notification.action_url
  } else {
    // 显示详情对话框
    selectedNotification.value = notification
    showDetailDialog.value = true
  }
}

const handleNotificationAction = async (action: string, notification: any) => {
  switch (action) {
    case 'read':
      await markNotificationRead(notification.id)
      break
    case 'pin':
      await pinNotification(notification.id, true)
      break
    case 'unpin':
      await pinNotification(notification.id, false)
      break
    case 'delete':
      await deleteNotification(notification.id)
      break
  }
}

const markNotificationRead = async (notificationId: number) => {
  try {
    const response = await fetch(`/api/v1/reminders/notifications/${notificationId}/read`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      await loadNotifications()
      ElMessage.success('已标记为已读')
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

const markAllRead = async () => {
  try {
    const response = await fetch('/api/v1/reminders/notifications/read-all', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      await loadNotifications()
      ElMessage.success('所有通知已标记为已读')
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
    ElMessage.error('操作失败')
  }
}

const pinNotification = async (notificationId: number, pinned: boolean) => {
  try {
    const response = await fetch(`/api/v1/reminders/notifications/${notificationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({ is_pinned: pinned })
    })
    
    if (response.ok) {
      await loadNotifications()
      ElMessage.success(pinned ? '已置顶' : '已取消置顶')
    }
  } catch (error) {
    console.error('置顶操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const deleteNotification = async (notificationId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await fetch(`/api/v1/reminders/notifications/${notificationId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      body: JSON.stringify({ is_deleted: true })
    })
    
    if (response.ok) {
      await loadNotifications()
      ElMessage.success('通知已删除')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const viewAllNotifications = () => {
  // 跳转到通知管理页面
  window.location.href = '/notifications'
}

const openNotificationSettings = () => {
  // 跳转到通知设置页面
  window.location.href = '/settings/notifications'
}

const getNotificationIcon = (notification: any) => {
  const iconMap = {
    'reminder': Bell,
    'warning': Warning,
    'info': InfoFilled,
    'success': SuccessFilled,
    'system': InfoFilled
  }
  return iconMap[notification.notification_type] || Bell
}

const getNotificationColor = (notification: any) => {
  const colorMap = {
    'primary': '#409eff',
    'success': '#67c23a',
    'warning': '#e6a23c',
    'danger': '#f56c6c',
    'info': '#909399'
  }
  return colorMap[notification.color] || '#409eff'
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-trigger {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.notification-trigger:hover {
  background-color: var(--el-fill-color-light);
}

.notification-icon {
  color: var(--el-text-color-regular);
}

.notification-dropdown {
  width: 380px;
  max-height: 500px;
  padding: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color-page);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.count {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: var(--el-fill-color-light);
}

.notification-item.unread {
  background-color: var(--el-color-primary-light-9);
  border-left: 3px solid var(--el-color-primary);
}

.notification-item.pinned {
  background-color: var(--el-color-warning-light-9);
}

.notification-icon-wrapper {
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.pin-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  color: var(--el-color-warning);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-text {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.action {
  color: var(--el-color-primary);
  font-weight: 500;
}

.notification-actions {
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.3s;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.action-trigger {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: var(--el-text-color-secondary);
}

.action-trigger:hover {
  background-color: var(--el-fill-color);
  color: var(--el-text-color-primary);
}

.notification-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--el-border-color-light);
  text-align: center;
  background-color: var(--el-bg-color-page);
}
</style>
