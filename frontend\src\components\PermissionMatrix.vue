<template>
  <div class="permission-matrix">
    <div class="matrix-header">
      <h3>系统权限矩阵</h3>
      <p>查看不同角色在各功能模块的权限配置</p>
    </div>

    <div class="matrix-content" v-loading="loading">
      <el-table
        :data="matrixData"
        border
        stripe
        class="matrix-table"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="name" label="功能模块" width="200" fixed>
          <template #default="{ row }">
            <div class="module-info">
              <span class="module-name">{{ row.name }}</span>
              <el-tooltip :content="row.description" placement="top">
                <el-icon class="info-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="系统管理员" align="center" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.permissions.admin ? 'success' : 'danger'"
              size="small"
            >
              {{ row.permissions.admin ? '允许' : '禁止' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="管理部门" align="center" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.permissions.manager ? 'success' : 'danger'"
              size="small"
            >
              {{ row.permissions.manager ? '允许' : '禁止' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="基层用户" align="center" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.permissions.user ? 'success' : 'danger'"
              size="small"
            >
              {{ row.permissions.user ? '允许' : '禁止' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="权限说明" min-width="300">
          <template #default="{ row }">
            <span class="description-text">{{ row.description }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="matrix-footer">
      <div class="role-legend">
        <h4>角色说明</h4>
        <div class="legend-items">
          <div class="legend-item">
            <el-tag type="primary" size="small">系统管理员</el-tag>
            <span>拥有系统所有权限，可以管理用户和系统配置</span>
          </div>
          <div class="legend-item">
            <el-tag type="warning" size="small">管理部门</el-tag>
            <span>可以管理标签和批注，导出相关数据</span>
          </div>
          <div class="legend-item">
            <el-tag type="info" size="small">基层用户</el-tag>
            <span>只能录入和查看本部门数据</span>
          </div>
        </div>
      </div>

      <div class="current-user-info">
        <h4>当前用户权限</h4>
        <div class="user-permissions">
          <div class="user-basic-info">
            <span class="user-name">{{ authStore.user?.full_name }}</span>
            <el-tag :type="getRoleTagType(authStore.user?.role)" size="small">
              {{ getRoleLabel(authStore.user?.role) }}
            </el-tag>
          </div>
          <div class="permission-list">
            <el-tag
              v-for="permission in userPermissions"
              :key="permission"
              size="small"
              class="permission-tag"
            >
              {{ getPermissionLabel(permission) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { authAPI } from '@/api/auth'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 状态
const loading = ref(false)
const matrixData = ref<any[]>([])
const userPermissions = ref<string[]>([])

// 获取权限矩阵数据
const getPermissionMatrix = async () => {
  try {
    loading.value = true
    const response = await authAPI.getPermissionMatrix()
    matrixData.value = response.modules
  } catch (error) {
    console.error('Get permission matrix error:', error)
    ElMessage.error('获取权限矩阵失败')
  } finally {
    loading.value = false
  }
}

// 获取用户权限
const getUserPermissions = async () => {
  try {
    const response = await authAPI.getUserPermissions()
    userPermissions.value = response.permissions
  } catch (error) {
    console.error('Get user permissions error:', error)
  }
}

// 获取角色标签类型
const getRoleTagType = (role?: string) => {
  switch (role) {
    case 'admin':
      return 'primary'
    case 'manager':
      return 'warning'
    case 'user':
      return 'info'
    default:
      return 'info'
  }
}

// 获取角色标签
const getRoleLabel = (role?: string) => {
  switch (role) {
    case 'admin':
      return '系统管理员'
    case 'manager':
      return '管理部门'
    case 'user':
      return '基层用户'
    default:
      return '未知角色'
  }
}

// 获取权限标签
const getPermissionLabel = (permission: string) => {
  const permissionLabels: Record<string, string> = {
    'edit_sequence': '编辑序号',
    'edit_all_ledgers': '编辑所有台账',
    'edit_own_ledgers': '编辑本部门台账',
    'delete_ledgers': '删除台账',
    'view_all_data': '查看所有数据',
    'view_related_data': '查看相关数据',
    'view_own_data': '查看本部门数据',
    'manage_tags': '管理标签',
    'view_tags': '查看标签',
    'manage_users': '管理用户',
    'manage_departments': '管理部门',
    'export_data': '导出数据',
    'add_comments': '添加批注'
  }
  return permissionLabels[permission] || permission
}

// 组件挂载时获取数据
onMounted(() => {
  getPermissionMatrix()
  getUserPermissions()
})
</script>

<style scoped>
.permission-matrix {
  padding: 0;
}

.matrix-header {
  margin-bottom: 20px;
}

.matrix-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.matrix-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.matrix-content {
  margin-bottom: 30px;
}

.matrix-table {
  width: 100%;
}

.module-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-name {
  font-weight: 500;
  color: #303133;
}

.info-icon {
  color: #909399;
  cursor: help;
}

.description-text {
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.matrix-footer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.role-legend h4,
.current-user-info h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-item span {
  color: #606266;
  font-size: 14px;
}

.user-permissions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-basic-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-tag {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .matrix-footer {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .matrix-table {
    font-size: 12px;
  }
  
  .legend-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
