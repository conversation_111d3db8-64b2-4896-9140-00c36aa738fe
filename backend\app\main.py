"""
FastAPI应用主入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import json
from .core.config import settings
from .core.database import create_tables
from .api.v1 import ledgers, departments, tags, users, auth, years, backups, reminders, dashboard, permissions

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="台账管理系统后端API",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 自定义JSON响应类，确保中文字符正确显示
class UnicodeJSONResponse(JSONResponse):
    def render(self, content) -> bytes:
        return json.dumps(
            content,
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")

# 设置默认响应类
app.default_response_class = UnicodeJSONResponse

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
# 认证相关路由
app.include_router(
    auth.router,
    prefix=f"{settings.api_v1_prefix}/auth",
    tags=["认证授权"]
)

app.include_router(
    ledgers.router,
    prefix=f"{settings.api_v1_prefix}/ledgers",
    tags=["台账管理"]
)

app.include_router(
    departments.router,
    prefix=f"{settings.api_v1_prefix}/departments",
    tags=["部门管理"]
)

app.include_router(
    tags.router,
    prefix=f"{settings.api_v1_prefix}/tags",
    tags=["标签管理"]
)

app.include_router(
    users.router,
    prefix=f"{settings.api_v1_prefix}/users",
    tags=["用户管理"]
)

app.include_router(
    years.router,
    prefix=f"{settings.api_v1_prefix}/years",
    tags=["年度管理"]
)

app.include_router(
    backups.router,
    prefix=f"{settings.api_v1_prefix}/backups",
    tags=["备份管理"]
)

# 导入部门管理路由
from .api.v1 import department_management

app.include_router(
    department_management.router,
    prefix=f"{settings.api_v1_prefix}/department-management",
    tags=["部门管理高级功能"]
)

app.include_router(
    reminders.router,
    prefix=f"{settings.api_v1_prefix}/reminders",
    tags=["提醒系统"]
)

app.include_router(
    dashboard.router,
    prefix=f"{settings.api_v1_prefix}/dashboard",
    tags=["仪表板"]
)

app.include_router(
    permissions.router,
    prefix=f"{settings.api_v1_prefix}/permissions",
    tags=["权限管理"]
)


@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化操作"""
    # 导入所有模型以确保表被创建
    from .models import Year, Backup, Archive  # 导入新模型
    from .models.reminder import ReminderRule, Reminder, NotificationCenter, ReminderLog  # 导入提醒模型

    # 创建数据库表
    create_tables()


@app.get("/", tags=["根路径"])
async def root():
    """根路径健康检查"""
    return {
        "message": f"欢迎使用{settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs"
    }


@app.get("/health", tags=["健康检查"])
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": settings.app_name}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.backend_port,
        reload=settings.debug
    )
