<template>
  <div class="tag-management">
    <div class="management-header">
      <div class="header-left">
        <h3>标签管理</h3>
        <p>管理系统标签分类和权限控制</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog" v-if="canManage">
          <el-icon><Plus /></el-icon>
          新增标签
        </el-button>
      </div>
    </div>

    <!-- 权限提示 -->
    <el-alert
      v-if="!canManage"
      title="权限提示"
      type="info"
      :description="permissionMessage"
      show-icon
      :closable="false"
      class="permission-alert"
    />

    <!-- 标签分类筛选 -->
    <div class="category-filters">
      <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button
          v-for="(desc, category) in categoryDescriptions"
          :key="category"
          :label="category"
        >
          {{ desc }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 标签列表 -->
    <div class="tag-content" v-loading="loading">
      <div class="tag-grid">
        <div
          v-for="tag in filteredTags"
          :key="tag.id"
          class="tag-card"
        >
          <div class="tag-header">
            <el-tag :type="getCategoryTagType(tag.category)" size="large">
              {{ tag.name }}
            </el-tag>
            <div class="tag-actions" v-if="canManage">
              <el-button size="small" @click="showEditDialog(tag)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteTag(tag)">删除</el-button>
            </div>
          </div>
          <div class="tag-info">
            <div class="tag-category">
              <span class="label">分类：</span>
              <el-tag size="small" :type="getCategoryTagType(tag.category)">
                {{ getCategoryLabel(tag.category) }}
              </el-tag>
            </div>
            <div class="tag-time">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDateTime(tag.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTags.length === 0 && !loading" class="empty-state">
        <el-empty description="暂无标签数据" />
      </div>
    </div>

    <!-- 标签编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑标签' : '新增标签'"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="tagFormRef"
        :model="tagForm"
        :rules="tagRules"
        label-width="80px"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input
            v-model="tagForm.name"
            placeholder="请输入标签名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="标签分类" prop="category">
          <el-select v-model="tagForm.category" placeholder="请选择标签分类">
            <el-option
              v-for="(desc, category) in categoryDescriptions"
              :key="category"
              :label="desc"
              :value="category"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { tagAPI } from '@/api/tag'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 状态
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const tagFormRef = ref<FormInstance>()
const selectedCategory = ref('')

// 标签列表
const tagList = ref<any[]>([])
const tagPermissions = ref<any>({})

// 标签表单
const tagForm = reactive({
  name: '',
  category: ''
})

// 分类描述
const categoryDescriptions = {
  'work_type': '工作类型',
  'time_cycle': '时间周期',
  'priority': '优先级',
  'department': '部门归属'
}

// 表单验证规则
const tagRules: FormRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 50, message: '标签名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择标签分类', trigger: 'change' }
  ]
}

// 计算属性
const canManage = computed(() => authStore.canManageTags)

const permissionMessage = computed(() => {
  if (authStore.isUser) {
    return '您是基层用户，只能查看和选择标签，无法进行增删改操作。如需管理标签，请联系管理部门或系统管理员。'
  }
  return '您没有标签管理权限。'
})

const filteredTags = computed(() => {
  if (!selectedCategory.value) {
    return tagList.value
  }
  return tagList.value.filter(tag => tag.category === selectedCategory.value)
})

// 获取标签列表
const getTagList = async () => {
  try {
    loading.value = true
    const response = await tagAPI.getTags()

    // 处理多种可能的数据格式
    if (Array.isArray(response)) {
      // 直接是数组格式
      tagList.value = response
    } else if (response.data && Array.isArray(response.data)) {
      // 包含data字段的格式
      tagList.value = response.data
    } else if (response.items && Array.isArray(response.items)) {
      // 包含items字段的格式
      tagList.value = response.items
    } else if (response.tags && Array.isArray(response.tags)) {
      // 包含tags字段的格式
      tagList.value = response.tags
    } else {
      console.warn('标签数据格式异常:', response)
      tagList.value = []
    }

    console.log('标签列表加载成功:', tagList.value.length, '个标签')
  } catch (error) {
    console.error('Get tags error:', error)
    ElMessage.error('获取标签列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 获取标签权限
const getTagPermissions = async () => {
  try {
    const response = await tagAPI.getTagPermissions()
    tagPermissions.value = response
  } catch (error) {
    console.error('Get tag permissions error:', error)
  }
}

// 分类变更处理
const handleCategoryChange = () => {
  // 分类筛选逻辑已在计算属性中处理
}

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false
  resetTagForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (tag: any) => {
  isEdit.value = true
  tagForm.name = tag.name
  tagForm.category = tag.category
  dialogVisible.value = true
}

// 重置表单
const resetTagForm = () => {
  tagForm.name = ''
  tagForm.category = ''
}

// 提交表单
const handleSubmit = async () => {
  if (!tagFormRef.value) return
  
  try {
    const valid = await tagFormRef.value.validate()
    if (!valid) return
    
    submitLoading.value = true
    
    if (isEdit.value) {
      // 更新标签
      const tag = tagList.value.find(t => t.name === tagForm.name && t.category === tagForm.category)
      if (tag) {
        await tagAPI.updateTag(tag.id, {
          name: tagForm.name,
          category: tagForm.category
        })
        ElMessage.success('标签更新成功')
      }
    } else {
      // 创建标签
      await tagAPI.createTag({
        name: tagForm.name,
        category: tagForm.category
      })
      ElMessage.success('标签创建成功')
    }
    
    dialogVisible.value = false
    getTagList()
  } catch (error: any) {
    console.error('Submit tag error:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除标签
const deleteTag = async (tag: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除标签 "${tag.name}" 吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await tagAPI.deleteTag(tag.id)
    ElMessage.success('标签删除成功')
    getTagList()
  } catch (error) {
    // 用户取消或操作失败
  }
}

// 获取分类标签类型
const getCategoryTagType = (category: string) => {
  switch (category) {
    case 'work_type': return 'primary'
    case 'time_cycle': return 'success'
    case 'priority': return 'warning'
    case 'department': return 'info'
    default: return 'info'
  }
}

// 获取分类标签
const getCategoryLabel = (category: string) => {
  return categoryDescriptions[category as keyof typeof categoryDescriptions] || category
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  getTagList()
  getTagPermissions()
})
</script>

<style scoped>
.tag-management {
  padding: 0;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.permission-alert {
  margin-bottom: 20px;
}

.category-filters {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 6px;
}

.tag-content {
  min-height: 400px;
}

.tag-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.tag-card {
  background: white;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
}

.tag-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.tag-actions {
  display: flex;
  gap: 8px;
}

.tag-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tag-category,
.tag-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.label {
  color: #909399;
  font-weight: 500;
}

.value {
  color: #606266;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .management-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .tag-grid {
    grid-template-columns: 1fr;
  }
  
  .tag-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .tag-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
