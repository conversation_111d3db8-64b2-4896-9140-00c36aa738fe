"""
年度管理相关的Pydantic模式
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


# 年度管理模式
class YearBase(BaseModel):
    """年度基础模式"""
    year: int = Field(..., description="年度", ge=2020, le=2030)
    status: str = Field(default="active", description="状态")


class YearCreate(YearBase):
    """创建年度模式"""
    inherit_config: bool = Field(default=True, description="是否继承配置")
    archive_previous: bool = Field(default=False, description="是否归档上一年度")


class YearUpdate(BaseModel):
    """更新年度模式"""
    status: Optional[str] = Field(None, description="状态")
    total_ledgers: Optional[int] = Field(None, description="台账总数")
    completed_ledgers: Optional[int] = Field(None, description="已完成台账数")
    in_progress_ledgers: Optional[int] = Field(None, description="进行中台账数")
    draft_ledgers: Optional[int] = Field(None, description="草稿台账数")


class YearResponse(YearBase):
    """年度响应模式"""
    id: int
    total_ledgers: int = 0
    completed_ledgers: int = 0
    in_progress_ledgers: int = 0
    draft_ledgers: int = 0
    completed_ongoing_ledgers: int = 0
    completion_rate: float = 0.0
    created_at: Optional[datetime] = None
    archived_at: Optional[datetime] = None
    created_by: Optional[int] = None

    class Config:
        from_attributes = True


class YearListResponse(BaseModel):
    """年度列表响应模式"""
    data: List[YearResponse] = Field(..., description="年度数据列表")
    total: int = Field(..., description="总记录数")

    class Config:
        from_attributes = True


class YearStatsResponse(BaseModel):
    """年度统计响应模式"""
    year: int = Field(..., description="年度")
    total_ledgers: int = Field(..., description="台账总数")
    completed_ledgers: int = Field(..., description="已完成台账数")
    in_progress_ledgers: int = Field(..., description="进行中台账数")
    draft_ledgers: int = Field(..., description="草稿台账数")
    completion_rate: float = Field(..., description="完成率")
    department_stats: Dict[str, Dict[str, int]] = Field(default_factory=dict, description="部门统计")
    monthly_progress: List[int] = Field(default_factory=list, description="月度进度")
    tag_distribution: Dict[str, int] = Field(default_factory=dict, description="标签分布")

    class Config:
        from_attributes = True


class YearComparisonResponse(BaseModel):
    """年度对比响应模式"""
    comparison: Dict[str, Dict[str, Any]] = Field(..., description="对比数据")
    trends: Dict[str, float] = Field(..., description="趋势数据")

    class Config:
        from_attributes = True


# 备份管理模式
class BackupBase(BaseModel):
    """备份基础模式"""
    backup_name: str = Field(..., description="备份名称", max_length=200)
    backup_type: str = Field(default="manual", description="备份类型")
    backup_scope: str = Field(default="full", description="备份范围")
    target_year: Optional[int] = Field(None, description="目标年度")
    description: Optional[str] = Field(None, description="备份描述")


class BackupCreate(BackupBase):
    """创建备份模式"""
    pass


class BackupResponse(BackupBase):
    """备份响应模式"""
    id: int
    file_path: Optional[str]
    file_size: Optional[int]
    file_size_mb: float
    checksum: Optional[str]
    status: str
    created_at: Optional[datetime]

    class Config:
        from_attributes = True


class BackupListResponse(BaseModel):
    """备份列表响应模式"""
    data: List[BackupResponse] = Field(..., description="备份数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页记录数")

    class Config:
        from_attributes = True


# 归档管理模式
class ArchiveBase(BaseModel):
    """归档基础模式"""
    year: int = Field(..., description="归档年度")
    archive_type: str = Field(default="year", description="归档类型")


class ArchiveCreate(ArchiveBase):
    """创建归档模式"""
    pass


class ArchiveResponse(ArchiveBase):
    """归档响应模式"""
    id: int
    file_path: Optional[str]
    file_size: Optional[int]
    file_size_mb: float
    checksum: Optional[str]
    ledger_count: Optional[int]
    status: str
    created_at: Optional[datetime]

    class Config:
        from_attributes = True


class ArchiveListResponse(BaseModel):
    """归档列表响应模式"""
    data: List[ArchiveResponse] = Field(..., description="归档数据列表")
    total: int = Field(..., description="总记录数")

    class Config:
        from_attributes = True


# 年度创建响应模式
class YearCreateResponse(BaseModel):
    """年度创建响应模式"""
    id: int = Field(..., description="年度ID")
    year: int = Field(..., description="年度")
    status: str = Field(..., description="状态")
    inherited_items: Dict[str, int] = Field(default_factory=dict, description="继承项目统计")
    archive_info: Optional[Dict[str, Any]] = Field(None, description="归档信息")
    message: str = Field(..., description="创建结果消息")

    class Config:
        from_attributes = True


# 通用响应模式
class MessageResponse(BaseModel):
    """消息响应模式"""
    message: str = Field(..., description="响应消息")
    success: bool = Field(default=True, description="是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="附加数据")

    class Config:
        from_attributes = True
