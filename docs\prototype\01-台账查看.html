<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>台账查看 - XXX单位台账管理系统</title>
    <link rel="stylesheet" href="common.css">
    <style>
        /* 左侧筛选栏 */
        .filter-sidebar {
            width: 300px;
            background: white;
            padding: 20px;
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }
        
        .filter-title {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .filter-title::before {
            content: "🔍";
            margin-right: 8px;
        }
        
        .filter-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .filter-section:last-child {
            border-bottom: none;
        }
        
        .filter-label {
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
            display: flex;
            align-items: center;
        }
        
        .filter-label::before {
            margin-right: 8px;
        }
        
        .date-range {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .date-input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 5px 0;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.1);
        }
        
        .checkbox-item label {
            cursor: pointer;
            font-size: 14px;
        }
        
        .tag-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .tag-item {
            display: flex;
            align-items: center;
            background: #f0f8ff;
            border: 1px solid #1976d2;
            border-radius: 15px;
            padding: 5px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .tag-item input[type="checkbox"] {
            margin-right: 6px;
        }
        
        .tag-item:hover {
            background: #e3f2fd;
        }
        
        .tag-item.checked {
            background: #1976d2;
            color: white;
        }
        
        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        /* 右侧表格区域 */
        .table-area {
            flex: 1;
            background: white;
            padding: 20px;
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #1976d2;
        }
        
        .table-title {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .result-count {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="01-台账查看.html" class="nav-tab active">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 左侧筛选栏 -->
        <div class="filter-sidebar">
            <div class="filter-title">智能筛选</div>
            
            <!-- 时间范围筛选 -->
            <div class="filter-section">
                <div class="filter-label" style="--icon: '📅';">📅 时间范围</div>
                <div class="date-range">
                    <input type="date" class="date-input" value="2025-01-01">
                    <span>至</span>
                    <input type="date" class="date-input" value="2025-12-31">
                </div>
            </div>
            
            <!-- 责任单位筛选 -->
            <div class="filter-section">
                <div class="filter-label">🏢 责任单位</div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="dept1" checked>
                        <label for="dept1">综合办</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="dept2">
                        <label for="dept2">人力</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="dept3">
                        <label for="dept3">财务</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="dept4">
                        <label for="dept4">党建部</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="dept5">
                        <label for="dept5">管控部</label>
                    </div>
                </div>
            </div>
            
            <!-- 对口部门筛选 -->
            <div class="filter-section">
                <div class="filter-label">🏛️ 对口部门</div>
                <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">本单位：</div>
                <div class="checkbox-group" style="margin-bottom: 15px;">
                    <div class="checkbox-item">
                        <input type="checkbox" id="local1" checked>
                        <label for="local1">综合办</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="local2">
                        <label for="local2">人力</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="local3">
                        <label for="local3">财务</label>
                    </div>
                </div>
                <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">省分公司：</div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="prov1" checked>
                        <label for="prov1">综合办(党委办)</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="prov2">
                        <label for="prov2">人力</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="prov3">
                        <label for="prov3">财务</label>
                    </div>
                </div>
            </div>
            
            <!-- 关键词标签筛选 -->
            <div class="filter-section">
                <div class="filter-label">🏷️ 关键词标签</div>
                <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">工作类型：</div>
                <div class="tag-group" style="margin-bottom: 15px;">
                    <div class="tag-item checked">
                        <input type="checkbox" checked>
                        <label>重点工作</label>
                    </div>
                    <div class="tag-item checked">
                        <input type="checkbox" checked>
                        <label>大监督</label>
                    </div>
                    <div class="tag-item">
                        <input type="checkbox">
                        <label>专项工作</label>
                    </div>
                    <div class="tag-item">
                        <input type="checkbox">
                        <label>安全生产</label>
                    </div>
                </div>
                <div style="margin-bottom: 10px; font-weight: bold; color: #666; font-size: 13px;">时间周期：</div>
                <div class="tag-group">
                    <div class="tag-item">
                        <input type="checkbox">
                        <label>季度报送</label>
                    </div>
                    <div class="tag-item">
                        <input type="checkbox">
                        <label>半年报送</label>
                    </div>
                    <div class="tag-item">
                        <input type="checkbox">
                        <label>年度报送</label>
                    </div>
                </div>
            </div>
            
            <!-- 填报状态筛选 -->
            <div class="filter-section">
                <div class="filter-label">📊 填报状态</div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="status1" checked>
                        <label for="status1">已完成</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="status2">
                        <label for="status2">草稿</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="status3">
                        <label for="status3">待审核</label>
                    </div>
                </div>
            </div>
            
            <!-- 筛选按钮 -->
            <div class="filter-buttons">
                <button class="btn btn-primary" onclick="applyFilter()">应用筛选</button>
                <button class="btn btn-secondary" onclick="resetFilter()">重置筛选</button>
            </div>
        </div>
        
        <!-- 右侧表格区域 -->
        <div class="table-area">
            <div class="table-header">
                <div class="table-title">台账列表</div>
                <div class="result-count">筛选结果：共 <span id="result-count">15</span> 条记录</div>
            </div>
            
            <table class="data-table" id="ledger-table">
                <thead>
                    <tr>
                        <th style="width: 60px;">序号</th>
                        <th style="width: 200px;">工作事项</th>
                        <th style="width: 150px;">具体措施</th>
                        <th style="width: 150px;">进展情况</th>
                        <th style="width: 100px;">责任单位</th>
                        <th style="width: 150px;">关键词标签</th>
                        <th style="width: 100px;">批注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>001</td>
                        <td>安全生产检查工作</td>
                        <td>制定检查计划，组织专项检查...</td>
                        <td>已完成第一轮检查，发现问题3项...</td>
                        <td>综合办</td>
                        <td>
                            <div class="tag-display">
                                <span class="tag-badge">重点工作</span>
                                <span class="tag-badge">大监督</span>
                            </div>
                        </td>
                        <td>需要加强整改力度</td>
                    </tr>
                    <tr>
                        <td>002</td>
                        <td>质量管控体系建设</td>
                        <td>完善质量管理制度，建立监控体系...</td>
                        <td>制度已修订完成，正在试运行...</td>
                        <td>管控部</td>
                        <td>
                            <div class="tag-display">
                                <span class="tag-badge">重点工作</span>
                                <span class="tag-badge">专项工作</span>
                            </div>
                        </td>
                        <td>进展良好</td>
                    </tr>
                    <tr>
                        <td>003</td>
                        <td>人员培训计划实施</td>
                        <td>制定年度培训计划，分批次实施...</td>
                        <td>已完成第一批培训，效果良好...</td>
                        <td>人力</td>
                        <td>
                            <div class="tag-display">
                                <span class="tag-badge">重点工作</span>
                            </div>
                        </td>
                        <td>建议增加实操训练</td>
                    </tr>
                    <tr>
                        <td>004</td>
                        <td>财务预算执行监控</td>
                        <td>建立预算执行监控机制...</td>
                        <td>监控系统已上线，运行稳定...</td>
                        <td>财务</td>
                        <td>
                            <div class="tag-display">
                                <span class="tag-badge">大监督</span>
                            </div>
                        </td>
                        <td>数据准确性有待提升</td>
                    </tr>
                    <tr>
                        <td>005</td>
                        <td>党建工作创新实践</td>
                        <td>开展党建工作创新活动...</td>
                        <td>活动方案已制定，正在组织实施...</td>
                        <td>党建部</td>
                        <td>
                            <div class="tag-display">
                                <span class="tag-badge">重点工作</span>
                                <span class="tag-badge">专项工作</span>
                            </div>
                        </td>
                        <td>活动形式需要更加丰富</td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页控件 -->
            <div class="pagination">
                <button class="page-btn">◀ 上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">下一页 ▶</button>
            </div>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        // 应用筛选功能
        function applyFilter() {
            const checkedTags = document.querySelectorAll('.tag-item input:checked').length;
            const checkedDepts = document.querySelectorAll('.checkbox-group input:checked').length;
            
            // 更新结果数量
            const resultCount = Math.max(1, 20 - checkedTags - checkedDepts);
            document.getElementById('result-count').textContent = resultCount;
            
            showMessage('筛选已应用！找到 ' + resultCount + ' 条记录', 'success');
        }
        
        // 重置筛选功能
        function resetFilter() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            const tagItems = document.querySelectorAll('.tag-item');
            tagItems.forEach(item => item.classList.remove('checked'));
            
            document.querySelector('input[type="date"]').value = '2025-01-01';
            document.querySelectorAll('input[type="date"]')[1].value = '2025-12-31';
            
            document.getElementById('result-count').textContent = '所有';
            
            showMessage('筛选条件已重置！', 'info');
        }
        
        // 标签点击效果
        document.addEventListener('DOMContentLoaded', function() {
            const tagItems = document.querySelectorAll('.tag-item');
            tagItems.forEach(item => {
                item.addEventListener('click', function() {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    this.classList.toggle('checked', checkbox.checked);
                });
            });
            
            // 初始化已选中的标签样式
            const checkedTags = document.querySelectorAll('.tag-item input:checked');
            checkedTags.forEach(tag => {
                tag.closest('.tag-item').classList.add('checked');
            });
        });
    </script>
</body>
</html>
