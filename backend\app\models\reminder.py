"""
提醒系统数据模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from .base import Base


class ReminderRule(Base):
    """提醒规则模型"""
    
    __tablename__ = "reminder_rules"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 基础字段
    name = Column(String(100), nullable=False, comment="规则名称")
    description = Column(Text, nullable=True, comment="规则描述")
    
    # 规则配置
    work_type = Column(String(50), nullable=True, comment="工作类型")
    department = Column(String(100), nullable=True, comment="适用部门")
    tags = Column(JSON, nullable=True, comment="适用标签")
    
    # 提醒时间配置
    advance_days = Column(Integer, default=7, comment="提前天数")
    reminder_frequency = Column(String(20), default="once", comment="提醒频率: once, daily, weekly")
    
    # 多级提醒配置
    enable_escalation = Column(Boolean, default=False, comment="是否启用升级提醒")
    escalation_days = Column(Integer, default=3, comment="升级提醒间隔天数")
    escalation_levels = Column(JSON, nullable=True, comment="升级提醒级别配置")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 审计字段
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    creator = relationship("User", foreign_keys=[created_by])
    reminders = relationship("Reminder", back_populates="rule")
    
    def __repr__(self):
        return f"<ReminderRule(id={self.id}, name='{self.name}', active={self.is_active})>"


class Reminder(Base):
    """提醒记录模型"""
    
    __tablename__ = "reminders"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 关联字段
    rule_id = Column(Integer, ForeignKey("reminder_rules.id"), nullable=False, comment="提醒规则ID")
    ledger_id = Column(Integer, ForeignKey("ledgers.id"), nullable=False, comment="台账ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="提醒对象用户ID")
    
    # 提醒内容
    title = Column(String(200), nullable=False, comment="提醒标题")
    content = Column(Text, nullable=False, comment="提醒内容")
    reminder_type = Column(String(20), default="deadline", comment="提醒类型: deadline, escalation, custom")
    priority = Column(String(10), default="normal", comment="优先级: low, normal, high, urgent")
    
    # 时间字段
    scheduled_time = Column(DateTime, nullable=False, comment="计划提醒时间")
    sent_time = Column(DateTime, nullable=True, comment="实际发送时间")
    read_time = Column(DateTime, nullable=True, comment="阅读时间")
    
    # 状态字段
    status = Column(String(20), default="pending", comment="状态: pending, sent, read, dismissed")
    is_read = Column(Boolean, default=False, comment="是否已读")
    is_dismissed = Column(Boolean, default=False, comment="是否已忽略")
    
    # 扩展信息
    extra_data = Column(JSON, nullable=True, comment="扩展元数据")
    
    # 审计字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    rule = relationship("ReminderRule", back_populates="reminders")
    ledger = relationship("Ledger")
    user = relationship("User")
    
    def __repr__(self):
        return f"<Reminder(id={self.id}, title='{self.title}', status='{self.status}')>"


class NotificationCenter(Base):
    """通知中心模型"""
    
    __tablename__ = "notifications"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 关联字段
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    reminder_id = Column(Integer, ForeignKey("reminders.id"), nullable=True, comment="关联提醒ID")
    
    # 通知内容
    title = Column(String(200), nullable=False, comment="通知标题")
    content = Column(Text, nullable=False, comment="通知内容")
    notification_type = Column(String(30), default="reminder", comment="通知类型: reminder, system, announcement")
    
    # 显示配置
    icon = Column(String(50), nullable=True, comment="图标")
    color = Column(String(20), default="primary", comment="颜色主题")
    action_url = Column(String(200), nullable=True, comment="操作链接")
    action_text = Column(String(50), nullable=True, comment="操作按钮文本")
    
    # 状态字段
    is_read = Column(Boolean, default=False, comment="是否已读")
    is_pinned = Column(Boolean, default=False, comment="是否置顶")
    is_deleted = Column(Boolean, default=False, comment="是否删除")
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    read_at = Column(DateTime, nullable=True, comment="阅读时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    # 关联关系
    user = relationship("User")
    reminder = relationship("Reminder")
    
    def __repr__(self):
        return f"<NotificationCenter(id={self.id}, title='{self.title}', read={self.is_read})>"


class ReminderLog(Base):
    """提醒日志模型"""
    
    __tablename__ = "reminder_logs"
    
    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    # 关联字段
    reminder_id = Column(Integer, ForeignKey("reminders.id"), nullable=False, comment="提醒ID")
    
    # 日志内容
    action = Column(String(50), nullable=False, comment="操作类型: created, sent, read, dismissed, failed")
    details = Column(Text, nullable=True, comment="详细信息")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 执行信息
    executed_by = Column(String(50), default="system", comment="执行者: system, user")
    execution_time = Column(DateTime, default=datetime.now, comment="执行时间")
    
    # 扩展信息
    extra_data = Column(JSON, nullable=True, comment="扩展元数据")
    
    # 关联关系
    reminder = relationship("Reminder")
    
    def __repr__(self):
        return f"<ReminderLog(id={self.id}, action='{self.action}', time={self.execution_time})>"
