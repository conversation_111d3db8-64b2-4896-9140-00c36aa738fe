<template>
  <el-dialog
    v-model="dialogVisible"
    title="备份管理"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="backup-management">
      <!-- 统计信息 -->
      <div class="backup-stats" v-if="backupStats">
        <div class="stat-item">
          <div class="stat-number">{{ backupStats.total_backups }}</div>
          <div class="stat-label">总备份数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ backupStats.completed_backups }}</div>
          <div class="stat-label">成功备份</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ backupStats.success_rate }}%</div>
          <div class="stat-label">成功率</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ backupStats.total_size_mb }}MB</div>
          <div class="stat-label">总大小</div>
        </div>
      </div>

      <!-- 操作栏 -->
      <div class="backup-actions">
        <el-button type="primary" @click="createQuickBackup" :loading="quickBackupLoading">
          <el-icon><Plus /></el-icon>
          快速备份
        </el-button>
        <el-button @click="refreshBackupList" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="danger" @click="cleanupFailedBackups" :disabled="!hasFailedBackups">
          <el-icon><Delete /></el-icon>
          清理失败备份
        </el-button>
      </div>

      <!-- 备份列表 -->
      <el-table 
        :data="backups" 
        v-loading="loading"
        border
        style="width: 100%"
        :default-sort="{ prop: 'created_at', order: 'descending' }"
      >
        <el-table-column prop="backup_name" label="备份名称" min-width="200">
          <template #default="{ row }">
            <div class="backup-name">
              <span>{{ row.backup_name }}</span>
              <el-tag 
                size="small" 
                :type="getBackupTypeTag(row.backup_type)"
                style="margin-left: 8px"
              >
                {{ getBackupTypeText(row.backup_type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="backup_scope" label="备份范围" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getScopeTypeTag(row.backup_scope)">
              {{ getScopeText(row.backup_scope) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="target_year" label="目标年度" width="100" align="center">
          <template #default="{ row }">
            {{ row.target_year || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="file_size_mb" label="文件大小" width="100" align="center">
          <template #default="{ row }">
            {{ row.file_size_mb ? `${row.file_size_mb}MB` : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTypeTag(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                type="primary" 
                size="small" 
                @click="downloadBackup(row)"
                :disabled="row.status !== 'completed'"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              
              <el-button 
                type="danger" 
                size="small" 
                @click="deleteBackup(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Delete, Download } from '@element-plus/icons-vue'

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const quickBackupLoading = ref(false)
const backups = ref([])
const backupStats = ref(null)

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    loadBackupList()
    loadBackupStats()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 计算属性
const hasFailedBackups = computed(() => {
  return backups.value.some(backup => backup.status === 'failed')
})

// 方法
const loadBackupList = async () => {
  try {
    loading.value = true
    
    const response = await fetch(`/api/v1/backups?page=${pagination.page}&size=${pagination.size}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      backups.value = data.data || []
      pagination.total = data.total || 0
    } else {
      ElMessage.error('获取备份列表失败')
    }
  } catch (error) {
    console.error('获取备份列表失败:', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    loading.value = false
  }
}

const loadBackupStats = async () => {
  try {
    const response = await fetch('/api/v1/backups/stats/summary', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      backupStats.value = await response.json()
    }
  } catch (error) {
    console.error('获取备份统计失败:', error)
  }
}

const createQuickBackup = async () => {
  try {
    quickBackupLoading.value = true
    
    const response = await fetch('/api/v1/backups/quick?backup_type=manual', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('快速备份创建成功')
      loadBackupList()
      loadBackupStats()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '快速备份失败')
    }
  } catch (error) {
    console.error('快速备份失败:', error)
    ElMessage.error('快速备份失败')
  } finally {
    quickBackupLoading.value = false
  }
}

const refreshBackupList = () => {
  loadBackupList()
  loadBackupStats()
}

const downloadBackup = async (backup) => {
  try {
    const response = await fetch(`/api/v1/backups/${backup.id}/download`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${backup.backup_name}.zip`
      link.click()
      URL.revokeObjectURL(url)
      
      ElMessage.success('备份下载成功')
    } else {
      ElMessage.error('备份下载失败')
    }
  } catch (error) {
    console.error('备份下载失败:', error)
    ElMessage.error('备份下载失败')
  }
}

const deleteBackup = async (backup) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除备份"${backup.backup_name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await fetch(`/api/v1/backups/${backup.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('备份删除成功')
      loadBackupList()
      loadBackupStats()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '备份删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('备份删除失败:', error)
      ElMessage.error('备份删除失败')
    }
  }
}

const cleanupFailedBackups = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有失败的备份记录吗？此操作不可恢复。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const failedBackups = backups.value.filter(backup => backup.status === 'failed')
    let successCount = 0
    
    for (const backup of failedBackups) {
      try {
        const response = await fetch(`/api/v1/backups/${backup.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
          }
        })
        
        if (response.ok) {
          successCount++
        }
      } catch (error) {
        console.error(`删除备份${backup.id}失败:`, error)
      }
    }
    
    ElMessage.success(`成功清理${successCount}个失败备份`)
    loadBackupList()
    loadBackupStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理失败备份失败:', error)
      ElMessage.error('清理失败备份失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadBackupList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadBackupList()
}

const getBackupTypeTag = (type) => {
  return type === 'manual' ? 'primary' : 'success'
}

const getBackupTypeText = (type) => {
  return type === 'manual' ? '手动' : '自动'
}

const getScopeTypeTag = (scope) => {
  return scope === 'full' ? 'success' : 'primary'
}

const getScopeText = (scope) => {
  return scope === 'full' ? '完整' : '年度'
}

const getStatusTypeTag = (status) => {
  switch (status) {
    case 'completed': return 'success'
    case 'failed': return 'danger'
    case 'creating': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '成功'
    case 'failed': return '失败'
    case 'creating': return '创建中'
    default: return '未知'
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.backup-management {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.backup-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.backup-actions {
  display: flex;
  gap: 12px;
}

.backup-name {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
