"""
台账数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.sql import func
from .base import Base


class Ledger(Base):
    """台账主表模型"""

    __tablename__ = "ledgers"

    # 主键
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # 基础字段
    sequence_number = Column(String(10), unique=True, nullable=False, index=True, comment="序号")
    work_item = Column(String(500), nullable=False, comment="工作事项名称")
    measures = Column(Text, nullable=False, comment="具体措施")
    progress = Column(Text, nullable=False, comment="进展情况")
    effectiveness = Column(Text, nullable=True, comment="成效描述")

    # 部门和标签
    responsible_dept = Column(String(100), nullable=False, index=True, comment="责任单位")
    reporting_unit = Column(String(100), nullable=True, comment="填报单位")
    counterpart_depts = Column(JSON, nullable=True, comment="对口部门(JSON数组)")
    tags = Column(JSON, nullable=True, comment="关键词标签(JSON数组)")

    # 工作目标和计划
    work_objectives = Column(Text, nullable=True, comment="工作目标")
    next_steps = Column(Text, nullable=True, comment="下一步措施或计划")
    difficulties = Column(Text, nullable=True, comment="遇到的困难或问题")

    # 问责和监督
    accountability_status = Column(String(100), nullable=True, comment="问责情况")

    # 时间管理
    required_submit_date = Column(DateTime, nullable=True, comment="要求填报日期")

    # 其他字段
    remarks = Column(Text, nullable=True, comment="备注")
    status = Column(String(20), default="completed", nullable=False, index=True, comment="状态")
    year = Column(Integer, default=2025, nullable=False, index=True, comment="年度")

    # 审计字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(Integer, nullable=True, comment="创建人ID")

    def __repr__(self):
        return f"<Ledger(id={self.id}, sequence_number='{self.sequence_number}', work_item='{self.work_item[:20]}...')>"
