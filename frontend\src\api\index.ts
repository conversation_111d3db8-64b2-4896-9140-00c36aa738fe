/**
 * API 配置和请求封装
 */
import axios from 'axios'

// API 基础配置 - 使用相对路径让Vite代理处理
const API_BASE_URL = '/api/v1'

// 创建 axios 实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Token验证函数
const isTokenValid = (token: string | null): boolean => {
  if (!token) return false

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    // 减去5分钟的缓冲时间，避免时间同步问题
    return payload.exp > (currentTime - 300)
  } catch (error) {
    console.warn('Token解析失败:', error)
    return false
  }
}

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url)

    const token = localStorage.getItem('access_token')

    // 添加认证token（优先添加，减少检查）
    if (token) {
      config.headers.Authorization = `Bearer ${token}`

      // 只在非登录页面检查token有效性
      if (window.location.pathname !== '/login' && !isTokenValid(token)) {
        console.warn('Token可能已过期，但继续请求，让服务器决定')
        // 不立即清除token，让服务器响应决定
      }
    }

    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url)
    return response.data
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data)

    // 处理不同类型的错误
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          // 未授权错误处理
          console.warn('API认证失败:', error.config?.url)

          // 只有在关键API失败时才清除认证信息并跳转
          const criticalAPIs = ['/auth/login', '/auth/me', '/auth/permissions']
          const isCriticalAPI = criticalAPIs.some(api => error.config?.url?.includes(api))

          if (isCriticalAPI) {
            // 清除认证信息
            localStorage.removeItem('access_token')
            localStorage.removeItem('user_info')
            localStorage.removeItem('user_permissions')

            // 如果不在登录页面，显示错误信息并跳转
            if (window.location.pathname !== '/login') {
              import('element-plus').then(({ ElMessage }) => {
                ElMessage.error('登录已过期，请重新登录')
              })
              console.log('登录已过期，即将跳转到登录页')
              setTimeout(() => {
                window.location.href = '/login'
              }, 1000)
            }
          } else {
            // 非关键API的401错误，只记录日志，不影响登录状态
            console.warn('非关键API认证失败，不影响登录状态:', error.config?.url)
          }
          break

        case 403:
          // 权限不足 - 显示用户友好的错误提示
          import('element-plus').then(({ ElMessage }) => {
            ElMessage.error('权限不足，无法访问此功能')
          })
          console.error('权限不足:', data?.detail || '无权限执行此操作')
          break

        case 404:
          // 资源不存在
          console.error('资源不存在:', data?.detail || '请求的资源不存在')
          break

        case 500:
          // 服务器错误
          console.error('服务器错误:', data?.detail || '服务器内部错误')
          break

        default:
          console.error('API错误:', status, data?.detail || '未知错误')
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('网络错误: 服务器无响应')
    } else {
      // 请求配置出错
      console.error('请求配置错误:', error.message)
    }

    return Promise.reject(error)
  }
)

// 导出请求实例
export const request = api
export default api
export { API_BASE_URL }
