"""
台账相关的Pydantic模型
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator
import re


class LedgerBase(BaseModel):
    """台账基础模型"""

    sequence_number: str = Field(..., description="序号")
    work_item: str = Field(..., description="工作事项名称")
    work_objectives: Optional[str] = Field(None, description="工作目标")
    measures: str = Field(..., description="具体措施")
    progress: str = Field(..., description="进展情况")
    effectiveness: Optional[str] = Field(None, description="成效描述")
    responsible_dept: str = Field(..., description="责任单位")
    reporting_unit: Optional[str] = Field(None, description="填报单位")
    counterpart_depts: List[str] = Field(default_factory=list, description="对口部门")
    tags: List[str] = Field(default_factory=list, description="关键词标签")
    next_steps: Optional[str] = Field(None, description="下一步措施或计划")
    difficulties: Optional[str] = Field(None, description="遇到的困难或问题")
    accountability_status: Optional[str] = Field(None, description="问责情况")
    required_submit_date: Optional[datetime] = Field(None, description="要求填报日期")
    remarks: Optional[str] = Field(None, description="备注")
    status: str = Field(default="draft", description="状态")

    @validator('status')
    def validate_status(cls, v):
        """验证状态值"""
        valid_statuses = ["draft", "in_progress", "completed", "completed_ongoing"]
        if v not in valid_statuses:
            raise ValueError(f"无效的状态，有效状态: {', '.join(valid_statuses)}")
        return v
    year: int = Field(default=2025, description="年度")


class LedgerCreate(LedgerBase):
    """创建台账模型"""

    @validator('sequence_number')
    def validate_sequence_number(cls, v):
        if not re.match(r'^\d{3}$', v):
            raise ValueError('序号必须是3位数字')
        return v

    @validator('counterpart_depts')
    def validate_counterpart_depts(cls, v):
        if not v or len(v) == 0:
            raise ValueError('请选择至少一个对口部门')
        return v

    @validator('tags')
    def validate_tags(cls, v):
        if not v or len(v) == 0:
            raise ValueError('请选择至少一个关键词标签')
        return v


class LedgerUpdate(BaseModel):
    """更新台账模型"""

    sequence_number: Optional[str] = Field(None, description="序号")
    work_item: Optional[str] = Field(None, description="工作事项名称")
    work_objectives: Optional[str] = Field(None, description="工作目标")
    measures: Optional[str] = Field(None, description="具体措施")
    progress: Optional[str] = Field(None, description="进展情况")
    effectiveness: Optional[str] = Field(None, description="成效描述")
    responsible_dept: Optional[str] = Field(None, description="责任单位")
    reporting_unit: Optional[str] = Field(None, description="填报单位")
    counterpart_depts: Optional[List[str]] = Field(None, description="对口部门")
    tags: Optional[List[str]] = Field(None, description="关键词标签")
    next_steps: Optional[str] = Field(None, description="下一步措施或计划")
    difficulties: Optional[str] = Field(None, description="遇到的困难或问题")
    accountability_status: Optional[str] = Field(None, description="问责情况")
    required_submit_date: Optional[datetime] = Field(None, description="要求填报日期")
    remarks: Optional[str] = Field(None, description="备注")
    status: Optional[str] = Field(None, description="状态")

    @validator('status')
    def validate_status(cls, v):
        """验证状态值"""
        if v is not None:
            valid_statuses = ["draft", "in_progress", "completed", "completed_ongoing"]
            if v not in valid_statuses:
                raise ValueError(f"无效的状态，有效状态: {', '.join(valid_statuses)}")
        return v
    year: Optional[int] = Field(None, description="年度")

    @validator('sequence_number')
    def validate_sequence_number(cls, v):
        if v is not None and not re.match(r'^\d{3}$', v):
            raise ValueError('序号必须是3位数字')
        return v


class LedgerResponse(LedgerBase):
    """台账响应模型"""

    id: int
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class LedgerDraft(BaseModel):
    """台账草稿模型"""

    sequence_number: Optional[str] = Field(None, description="序号")
    work_item: Optional[str] = Field(None, description="工作事项名称")
    work_objectives: Optional[str] = Field(None, description="工作目标")
    measures: Optional[str] = Field(None, description="具体措施")
    progress: Optional[str] = Field(None, description="进展情况")
    effectiveness: Optional[str] = Field(None, description="成效描述")
    responsible_dept: Optional[str] = Field(None, description="责任单位")
    counterpart_depts: Optional[List[str]] = Field(None, description="对口部门")
    tags: Optional[List[str]] = Field(None, description="关键词标签")
    remarks: Optional[str] = Field(None, description="备注")
    draft_id: Optional[str] = Field(None, description="草稿唯一标识")
    year: Optional[int] = Field(None, description="年度")


class SequenceValidationResponse(BaseModel):
    """序号验证响应模型"""

    is_valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="验证消息")


class NextSequenceResponse(BaseModel):
    """下一个序号响应模型"""

    next_sequence: str = Field(..., description="下一个序号")


class UserPermissionsResponse(BaseModel):
    """用户权限响应模型"""

    can_edit_sequence: bool = Field(..., description="是否可以编辑序号")
    can_edit_all: bool = Field(..., description="是否可以编辑所有台账")
    department: str = Field(..., description="所属部门")
    role: str = Field(..., description="用户角色")  # Pydantic v2语法


class LedgerListResponse(BaseModel):
    """台账列表响应模型"""

    data: List[LedgerResponse] = Field(..., description="台账数据列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    limit: int = Field(..., description="每页记录数")
    pages: int = Field(..., description="总页数")

    class Config:
        from_attributes = True


class StatsResponse(BaseModel):
    """统计数据响应模型"""

    total_count: int = Field(..., description="总记录数")
    completed_count: int = Field(..., description="已完成数量（包含已完成并持续推进）")
    in_progress_count: int = Field(..., description="进行中数量")
    draft_count: int = Field(..., description="草稿数量")
    completed_ongoing_count: int = Field(default=0, description="已完成并持续推进数量")
    by_department: dict = Field(default_factory=dict, description="按部门统计")
    by_tags: dict = Field(default_factory=dict, description="按标签统计")

    class Config:
        from_attributes = True
