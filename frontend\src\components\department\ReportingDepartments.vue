<template>
  <div class="reporting-departments">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showCreateDialog = true" v-if="authStore.user?.role === 'admin'">
        <el-icon><Plus /></el-icon>
        新增填报单位
      </el-button>
      
      <el-button @click="refreshList" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>

      <el-button type="success" @click="exportDepartments">
        <el-icon><Download /></el-icon>
        导出列表
      </el-button>

      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索部门名称"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 部门列表 -->
    <el-table 
      :data="departments" 
      v-loading="loading"
      border
      style="width: 100%"
      :default-sort="{ prop: 'sort_order', order: 'ascending' }"
    >
      <el-table-column prop="name" label="部门名称" min-width="200">
        <template #default="{ row }">
          <div class="dept-name">
            <span>{{ row.name }}</span>
            <el-tag 
              size="small" 
              :type="row.status === 'active' ? 'success' : 'info'"
              style="margin-left: 8px"
            >
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="150">
        <template #default="{ row }">
          {{ row.description || '-' }}
        </template>
      </el-table-column>

      <el-table-column prop="user_count" label="用户数量" width="100" align="center">
        <template #default="{ row }">
          <el-tag type="primary">{{ row.user_count }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="sort_order" label="排序" width="80" align="center" />

      <el-table-column prop="created_at" label="创建时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button 
              type="primary" 
              size="small" 
              @click="editDepartment(row)"
              v-if="userStore.user?.role === 'admin'"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            
            <el-button 
              type="info" 
              size="small" 
              @click="viewUsers(row)"
            >
              <el-icon><User /></el-icon>
              用户
            </el-button>
            
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteDepartment(row)"
              v-if="userStore.user?.role === 'admin'"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <DepartmentFormDialog 
      v-model="showCreateDialog"
      :department="editingDepartment"
      department-type="reporting"
      @success="handleFormSuccess"
    />

    <!-- 用户列表对话框 -->
    <DepartmentUsersDialog 
      v-model="showUsersDialog"
      :department="selectedDepartment"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Download, Search, Edit, User, Delete } from '@element-plus/icons-vue'
import { useAuthStore } from '../../stores/auth'
import DepartmentFormDialog from './DepartmentFormDialog.vue'
import DepartmentUsersDialog from './DepartmentUsersDialog.vue'

// Store
const authStore = useAuthStore()

// 响应式数据
const departments = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const showCreateDialog = ref(false)
const showUsersDialog = ref(false)
const editingDepartment = ref(null)
const selectedDepartment = ref(null)

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 方法
const loadDepartments = async () => {
  try {
    loading.value = true
    
    const params = new URLSearchParams({
      skip: ((pagination.page - 1) * pagination.size).toString(),
      limit: pagination.size.toString()
    })
    
    const response = await fetch(`/api/v1/department-management/reporting?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      departments.value = data.data || []
      pagination.total = data.total || 0
    } else {
      ElMessage.error('获取填报单位列表失败')
    }
  } catch (error) {
    console.error('获取填报单位列表失败:', error)
    ElMessage.error('获取填报单位列表失败')
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  loadDepartments()
}

const handleSearch = () => {
  // 重置到第一页并重新加载
  pagination.page = 1
  loadDepartments()
}

const editDepartment = (department) => {
  editingDepartment.value = { ...department }
  showCreateDialog.value = true
}

const deleteDepartment = async (department) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除填报单位"${department.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await fetch(`/api/v1/department-management/reporting/${department.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      ElMessage.success('删除成功')
      loadDepartments()
    } else {
      const error = await response.json()
      ElMessage.error(error.detail || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const viewUsers = (department) => {
  selectedDepartment.value = department
  showUsersDialog.value = true
}

const exportDepartments = () => {
  // 导出部门列表
  const csvContent = [
    ['部门名称', '描述', '用户数量', '排序', '状态', '创建时间'],
    ...departments.value.map(dept => [
      dept.name,
      dept.description || '',
      dept.user_count,
      dept.sort_order,
      dept.status === 'active' ? '启用' : '禁用',
      formatDateTime(dept.created_at)
    ])
  ].map(row => row.join(',')).join('\n')
  
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `填报单位列表_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  
  ElMessage.success('导出成功')
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  editingDepartment.value = null
  loadDepartments()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadDepartments()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadDepartments()
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadDepartments()
})
</script>

<style scoped>
.reporting-departments {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.search-box {
  width: 250px;
}

.dept-name {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
