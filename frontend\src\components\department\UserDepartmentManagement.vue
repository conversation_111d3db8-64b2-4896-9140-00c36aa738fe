<template>
  <div class="user-department-management">
    <el-card>
      <template #header>
        <span>用户归属管理</span>
      </template>
      
      <div class="management-content">
        <el-button type="primary" @click="showBatchTransferDialog = true" v-if="userStore.user?.role === 'admin'">
          <el-icon><Switch /></el-icon>
          批量调动
        </el-button>
        
        <el-button @click="viewTransferHistory">
          <el-icon><Clock /></el-icon>
          调动历史
        </el-button>
      </div>
      
      <el-empty description="用户归属管理功能" />
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Switch, Clock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const showBatchTransferDialog = ref(false)

const viewTransferHistory = () => {
  // 查看调动历史
}
</script>

<style scoped>
.management-content {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}
</style>
