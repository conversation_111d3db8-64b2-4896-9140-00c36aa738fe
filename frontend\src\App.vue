<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
</script>

<template>
  <div class="app">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <h1>台账管理系统</h1>
        </div>
        <el-menu
          mode="horizontal"
          :default-active="$route.path"
          class="nav-menu"
          router
        >
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/ledgers">台账管理</el-menu-item>
          <el-menu-item v-if="authStore.canManageTags" index="/years">年度管理</el-menu-item>
          <el-menu-item v-if="authStore.canManageTags" index="/department-management">部门管理</el-menu-item>
          <el-menu-item v-if="authStore.canManageUsers" index="/user-management">用户管理</el-menu-item>
          <el-menu-item v-if="authStore.canManageTags" index="/permission-management">权限管理</el-menu-item>
          <el-menu-item v-if="authStore.canManageReminders" index="/reminders">提醒管理</el-menu-item>
          <el-menu-item index="/about">关于</el-menu-item>
        </el-menu>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-main class="app-main">
      <RouterView />
    </el-main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.app-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px;
  line-height: 60px;
  width: 100%;
  flex-shrink: 0;
}

.header-content {
  width: 100%;
  max-width: none;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.logo-section h1 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
}

.app-main {
  flex: 1;
  background: #f5f7fa;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}
</style>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

#app {
  width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}
</style>
