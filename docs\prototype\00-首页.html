<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XXX单位台账管理系统 - 原型演示</title>
    <link rel="stylesheet" href="common.css">
    <style>
        .welcome-container {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #f0f8ff, #e3f2fd);
            border-radius: 12px;
            margin-bottom: 40px;
            border: 1px solid #bbdefb;
        }
        
        .welcome-title {
            font-size: 32px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .welcome-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .welcome-description {
            font-size: 16px;
            color: #555;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .prototype-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .prototype-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #1976d2;
        }
        
        .card-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .card-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .card-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .card-features li {
            color: #555;
            font-size: 13px;
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .card-features li::before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .system-info {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .info-title {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .info-section {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .info-section h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .info-label {
            color: #666;
            font-weight: 500;
        }
        
        .info-value {
            color: #333;
            font-weight: bold;
        }
        
        .highlight-box {
            background: #e8f5e8;
            border: 1px solid #81c784;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .highlight-title {
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .highlight-content {
            color: #2e7d32;
            font-size: 14px;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .prototype-grid {
                grid-template-columns: 1fr;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="header">
        <h1>XXX单位台账管理系统</h1>
        <div class="user-info">
            <span>用户：张三 | 综合办 | 主管权限</span>
            <button class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <!-- 导航菜单 -->
    <div class="nav-menu">
        <div class="nav-tabs">
            <a href="00-首页.html" class="nav-tab active">系统首页</a>
            <a href="01-台账查看.html" class="nav-tab">台账查看</a>
            <a href="02-台账录入.html" class="nav-tab">台账录入</a>
            <a href="03-筛选导出.html" class="nav-tab">筛选导出</a>
            <a href="04-权限管理.html" class="nav-tab">权限管理</a>
            <a href="05-年度管理.html" class="nav-tab">年度管理</a>
            <a href="06-部门管理.html" class="nav-tab">部门管理</a>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="page-container">
        <!-- 欢迎区域 -->
        <div class="welcome-container">
            <div class="welcome-title">台账管理系统原型演示</div>
            <div class="welcome-subtitle">解决企业台账管理痛点，实现"一次填报、多次复用"</div>
            <div class="welcome-description">
                本系统专为企业内部台账管理设计，支持三级权限体系、智能标签筛选、年度数据管理等功能。
                通过统一的台账录入和多维度筛选导出，有效减少重复劳动，提升工作效率。
            </div>
        </div>
        
        <!-- 功能模块卡片 -->
        <div class="prototype-grid">
            <div class="prototype-card" onclick="window.location.href='01-台账查看.html'">
                <div class="card-icon">📊</div>
                <div class="card-title">台账查看</div>
                <div class="card-description">
                    智能筛选和查看台账数据，支持多维度筛选条件，左侧独立筛选栏设计。
                </div>
                <ul class="card-features">
                    <li>左侧筛选栏 + 右侧表格展示</li>
                    <li>关键词标签分类筛选</li>
                    <li>责任单位和对口部门筛选</li>
                    <li>时间范围和状态筛选</li>
                    <li>实时筛选结果统计</li>
                </ul>
            </div>
            
            <div class="prototype-card" onclick="window.location.href='02-台账录入.html'">
                <div class="card-icon">✏️</div>
                <div class="card-title">台账录入</div>
                <div class="card-description">
                    基于现有台账格式的录入界面，支持富文本编辑和必填项验证。
                </div>
                <ul class="card-features">
                    <li>必填项星号标记和验证</li>
                    <li>富文本编辑器支持</li>
                    <li>序号字段主管专用权限</li>
                    <li>多选对口部门和标签</li>
                    <li>草稿保存功能</li>
                </ul>
            </div>
            
            <div class="prototype-card" onclick="window.location.href='03-筛选导出.html'">
                <div class="card-icon">📤</div>
                <div class="card-title">筛选导出</div>
                <div class="card-description">
                    三步式导出流程，支持多种格式导出，便于用户在Excel中进行数据分析。
                </div>
                <ul class="card-features">
                    <li>三步式导出流程设计</li>
                    <li>筛选条件预览和确认</li>
                    <li>Excel/Word/PDF多格式支持</li>
                    <li>导出模板和选项配置</li>
                    <li>导出结果预览功能</li>
                </ul>
            </div>
            
            <div class="prototype-card" onclick="window.location.href='04-权限管理.html'">
                <div class="card-icon">🔐</div>
                <div class="card-title">权限管理</div>
                <div class="card-description">
                    三级权限体系管理，支持用户管理、标签管理和权限矩阵查看。
                </div>
                <ul class="card-features">
                    <li>主管、管理部门、基层三级权限</li>
                    <li>用户信息和角色管理</li>
                    <li>标签分类管理（管理部门权限）</li>
                    <li>权限矩阵可视化展示</li>
                    <li>批量用户导入导出</li>
                </ul>
            </div>
            
            <div class="prototype-card" onclick="window.location.href='05-年度管理.html'">
                <div class="card-icon">📅</div>
                <div class="card-title">年度管理</div>
                <div class="card-description">
                    年度台账创建、历史数据管理和数据迁移功能，支持年度间数据对比。
                </div>
                <ul class="card-features">
                    <li>年度台账创建和配置继承</li>
                    <li>历史台账归档和查询</li>
                    <li>年度间数据对比分析</li>
                    <li>数据迁移和备份功能</li>
                    <li>统计数据可视化展示</li>
                </ul>
            </div>
            
            <div class="prototype-card" onclick="window.location.href='06-部门管理.html'">
                <div class="card-icon">🏢</div>
                <div class="card-title">部门管理</div>
                <div class="card-description">
                    主管专用的部门管理功能，支持填报单位和对口部门的灵活配置。
                </div>
                <ul class="card-features">
                    <li>填报单位管理（12个单位）</li>
                    <li>对口部门管理（本单位+省分公司）</li>
                    <li>部门名称修改和组织架构调整</li>
                    <li>部门变更历史记录</li>
                    <li>批量导入导出功能</li>
                </ul>
            </div>
        </div>
        
        <!-- 系统信息 -->
        <div class="system-info">
            <div class="info-title">系统设计要点</div>
            <div class="info-grid">
                <div class="info-section">
                    <h4>🎯 核心功能</h4>
                    <div class="info-item">
                        <span class="info-label">统一台账录入</span>
                        <span class="info-value">基于现有格式</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">智能标签筛选</span>
                        <span class="info-value">独立筛选区域</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">多格式导出</span>
                        <span class="info-value">Excel分析优先</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">三级权限体系</span>
                        <span class="info-value">精细化管理</span>
                    </div>
                </div>
                
                <div class="info-section">
                    <h4>📊 组织架构</h4>
                    <div class="info-item">
                        <span class="info-label">填报单位</span>
                        <span class="info-value">12个</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">本单位对口部门</span>
                        <span class="info-value">8个</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">省分公司对口部门</span>
                        <span class="info-value">9个</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">权限级别</span>
                        <span class="info-value">3级</span>
                    </div>
                </div>
            </div>
            
            <div class="highlight-box">
                <div class="highlight-title">💡 设计亮点</div>
                <div class="highlight-content">
                    • <strong>标签筛选独立设计</strong>：解决用户关心的筛选操作问题，左侧筛选栏与表格完全分离<br>
                    • <strong>部门管理灵活配置</strong>：主管可后台修改部门名称，适应企业改革需求<br>
                    • <strong>年度数据自动归档</strong>：每年新建空白台账，历史数据只读保护<br>
                    • <strong>权限分级精细化</strong>：主管、管理部门、基层用户三级权限，功能权限清晰划分<br>
                    • <strong>导出功能专业化</strong>：三步式导出流程，支持Excel数据分析需求
                </div>
            </div>
        </div>
    </div>

    <script src="common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showMessage('欢迎使用台账管理系统原型演示！点击功能卡片体验各个模块。', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
