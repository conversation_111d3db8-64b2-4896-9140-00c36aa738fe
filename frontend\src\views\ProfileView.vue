<template>
  <div class="profile-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1>个人信息</h1>
        <p>查看和编辑您的个人资料</p>
      </div>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 个人信息卡片 -->
    <div class="profile-container">
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="16">
          <el-card shadow="hover" class="profile-card">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
                <el-button 
                  type="primary" 
                  @click="editMode = !editMode"
                  :loading="loading"
                >
                  {{ editMode ? '取消编辑' : '编辑信息' }}
                </el-button>
              </div>
            </template>

            <el-form 
              :model="userForm" 
              label-width="120px" 
              :disabled="!editMode"
              ref="formRef"
              :rules="formRules"
            >
              <el-form-item label="用户名">
                <el-input v-model="userForm.username" disabled />
              </el-form-item>
              
              <el-form-item label="姓名" prop="full_name">
                <el-input v-model="userForm.full_name" />
              </el-form-item>
              
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="userForm.email" type="email" />
              </el-form-item>
              
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="userForm.phone" />
              </el-form-item>
              
              <el-form-item label="角色">
                <el-tag :type="getRoleType(userForm.role)">
                  {{ getRoleLabel(userForm.role) }}
                </el-tag>
              </el-form-item>
              
              <el-form-item label="所属部门">
                <el-input v-model="userForm.department" disabled />
              </el-form-item>
              
              <el-form-item label="账户状态">
                <el-tag :type="userForm.is_active ? 'success' : 'danger'">
                  {{ userForm.is_active ? '正常' : '禁用' }}
                </el-tag>
              </el-form-item>
              
              <el-form-item label="最后登录">
                <span>{{ formatDate(userForm.last_login) }}</span>
              </el-form-item>
              
              <el-form-item label="登录次数">
                <span>{{ userForm.login_count || 0 }} 次</span>
              </el-form-item>
              
              <el-form-item label="注册时间">
                <span>{{ formatDate(userForm.created_at) }}</span>
              </el-form-item>

              <el-form-item v-if="editMode">
                <el-button type="primary" @click="saveProfile" :loading="saving">
                  保存修改
                </el-button>
                <el-button @click="resetForm">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 侧边栏 -->
        <el-col :span="8">
          <!-- 头像卡片 -->
          <el-card shadow="hover" class="avatar-card">
            <template #header>
              <span>头像</span>
            </template>
            <div class="avatar-section">
              <el-avatar :size="120" class="user-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="avatar-info">
                <h3>{{ userForm.full_name }}</h3>
                <p>{{ getRoleLabel(userForm.role) }}</p>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card shadow="hover" class="quick-actions">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="action-buttons">
              <el-button type="warning" @click="$router.push('/change-password')" block>
                <el-icon><Lock /></el-icon>
                修改密码
              </el-button>
              <el-button type="info" @click="refreshProfile" :loading="loading" block>
                <el-icon><Refresh /></el-icon>
                刷新信息
              </el-button>
            </div>
          </el-card>

          <!-- 安全信息 -->
          <el-card shadow="hover" class="security-info">
            <template #header>
              <span>安全信息</span>
            </template>
            <el-descriptions :column="1" size="small">
              <el-descriptions-item label="密码强度">
                <el-tag type="success">强</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="两步验证">
                <el-tag type="info">未启用</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="登录保护">
                <el-tag type="success">已启用</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, User, Lock, Refresh } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const editMode = ref(false)
const loading = ref(false)
const saving = ref(false)
const formRef = ref()

// 用户表单数据
const userForm = reactive({
  username: '',
  full_name: '',
  email: '',
  phone: '',
  role: '',
  department: '',
  is_active: true,
  last_login: '',
  login_count: 0,
  created_at: ''
})

// 表单验证规则
const formRules = {
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 获取角色类型
const getRoleType = (role: string) => {
  switch (role) {
    case 'admin': return 'danger'
    case 'manager': return 'warning'
    case 'user': return 'success'
    default: return 'info'
  }
}

// 获取角色标签
const getRoleLabel = (role: string) => {
  switch (role) {
    case 'admin': return '系统管理员'
    case 'manager': return '管理部门'
    case 'user': return '基层用户'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载用户信息
const loadProfile = async () => {
  try {
    loading.value = true
    await authStore.getCurrentUser()
    
    // 填充表单数据
    const user = authStore.user
    if (user) {
      Object.assign(userForm, {
        username: user.username,
        full_name: user.full_name,
        email: user.email || '',
        phone: user.phone || '',
        role: user.role,
        department: user.department?.name || '未分配',
        is_active: user.is_active,
        last_login: user.last_login,
        login_count: user.login_count,
        created_at: user.created_at
      })
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败')
  } finally {
    loading.value = false
  }
}

// 保存个人信息
const saveProfile = async () => {
  try {
    await formRef.value.validate()
    saving.value = true
    
    // 调用更新API
    const updateData = {
      full_name: userForm.full_name,
      email: userForm.email,
      phone: userForm.phone
    }
    
    // 这里需要调用更新用户信息的API
    // await authStore.updateProfile(updateData)
    
    ElMessage.success('个人信息更新成功')
    editMode.value = false
    await loadProfile()
  } catch (error) {
    console.error('保存个人信息失败:', error)
    ElMessage.error('保存个人信息失败')
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  loadProfile()
  editMode.value = false
}

// 刷新个人信息
const refreshProfile = () => {
  loadProfile()
}

// 组件挂载时加载数据
onMounted(() => {
  loadProfile()
})
</script>

<style scoped>
.profile-view {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content h1 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
}

.profile-container {
  max-width: 1200px;
}

.profile-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-card {
  margin-bottom: 20px;
}

.avatar-section {
  text-align: center;
  padding: 20px 0;
}

.user-avatar {
  margin-bottom: 15px;
  background: linear-gradient(45deg, #409eff, #67c23a);
}

.avatar-info h3 {
  margin: 0 0 5px 0;
  color: #303133;
}

.avatar-info p {
  margin: 0;
  color: #909399;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.security-info {
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .profile-view {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
