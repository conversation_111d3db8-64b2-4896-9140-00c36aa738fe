<template>
  <div class="department-stats">
    <el-card>
      <template #header>
        <span>部门统计分析</span>
      </template>
      
      <div class="stats-grid">
        <div v-for="stat in departmentStats" :key="stat.department_id" class="stat-card">
          <div class="stat-header">
            <h4>{{ stat.department_name }}</h4>
          </div>
          <div class="stat-content">
            <div class="stat-item">
              <span class="stat-label">用户数量:</span>
              <span class="stat-value">{{ stat.user_count }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">台账数量:</span>
              <span class="stat-value">{{ stat.ledger_count }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">完成率:</span>
              <span class="stat-value">{{ stat.completion_rate }}%</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const departmentStats = ref([])
const loading = ref(false)

const loadStats = async () => {
  try {
    loading.value = true
    const response = await fetch('/api/v1/departments/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    })
    
    if (response.ok) {
      departmentStats.value = await response.json()
    }
  } catch (error) {
    console.error('获取部门统计失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.stat-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.stat-header h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: 500;
  color: #303133;
}
</style>
