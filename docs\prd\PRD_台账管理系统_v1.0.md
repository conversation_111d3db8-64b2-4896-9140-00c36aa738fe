# 台账管理系统 产品需求文档 (PRD)

## 1. 文档信息

| 项目 | 内容 |
|------|------|
| 文档版本 | v1.0 |
| 创建日期 | 2025-06-26 |
| 负责人 | Emma (产品经理) |
| 审核人 | Mike (团队领袖) |
| 项目代号 | 台账管理系统 (Ledger Management System) |

### 版本历史
- v1.0 (2025-06-26): 初始版本，完整需求分析

---

## 2. 背景与问题陈述

### 2.1 现状痛点
- **重复填报问题**：各管理部门要求内容相似的台账、报告，造成基层单位重复劳动
- **数据孤岛现象**：同一工作事项需要在多个台账中重复填写，数据无法复用
- **协作效率低**：共享文档开放编辑容易混乱，缺乏权限管控
- **提醒机制缺失**：缺乏自动化的报送提醒，容易遗漏重要时间节点
- **数据筛选困难**：无法根据部门、工作类型等维度快速筛选相关内容

### 2.2 核心问题
**如何构建一个统一的台账管理平台，实现"一次填报、多次复用"，同时保证数据安全和协作效率？**

---

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **提升填报效率**：减少重复填报工作量50%以上
2. **增强数据复用**：实现一次录入，多维度输出
3. **优化协作流程**：建立清晰的权限体系和审批流程
4. **自动化提醒**：100%覆盖重要时间节点提醒

### 3.2 关键结果 (Key Results)
- **效率指标**：台账填报时间减少60%
- **质量指标**：数据一致性提升90%
- **用户满意度**：系统使用满意度≥85%
- **覆盖率指标**：全公司台账统一管理覆盖率≥80%

### 3.3 反向指标 (Counter Metrics)
- 系统响应时间不超过3秒
- 数据丢失率控制在0.01%以内
- 用户学习成本不超过2小时

---

## 4. 用户画像与用户故事

### 4.1 核心用户画像

#### 用户类型1：基层填报人员
- **角色描述**：各部门单位的具体执行人员
- **核心需求**：简化填报流程，避免重复工作
- **使用场景**：定期更新工作进展，维护台账内容
- **技能水平**：基础办公软件操作能力

#### 用户类型2：管理部门人员
- **角色描述**：需要收集、汇总台账信息的管理人员
- **核心需求**：快速获取所需数据，进行批注和监督
- **使用场景**：定期收集台账，形成汇总报告
- **技能水平**：熟练办公软件操作，具备数据分析能力

#### 用户类型3：系统管理员
- **角色描述**：负责系统维护和权限管理的技术人员
- **核心需求**：用户权限管理，系统配置维护
- **使用场景**：用户管理，系统参数配置
- **技能水平**：具备基础技术管理能力

### 4.2 用户故事

#### 基层填报人员用户故事
```
作为一名基层填报人员，
我希望能够在一个统一的平台上填写工作内容，
这样我就不需要在多个不同的台账中重复填写相同的信息，
从而节省时间，提高工作效率。
```

#### 管理部门人员用户故事
```
作为一名管理部门人员，
我希望能够通过标签筛选快速获取我需要的台账内容，
这样我就能够高效地完成汇总工作，
并且可以对相关内容进行批注和监督。
```

---

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 统一台账录入模块
**功能描述**：基于现有台账格式的统一录入界面

**详细规格**：
- **字段设计**（基于实际台账结构）：
  - 序号（可编辑，仅主管权限）*必填
  - 工作事项名称（必填，文本）*
  - 具体措施（必填，富文本）*
  - 进展情况（必填，富文本）*
  - 成效描述（选填，富文本）
  - 责任单位（必填，下拉选择：综合办、人力、财务、党建部、纪委办、工会、管控部、生产中心A、生产中心B、运输中心、运维中心、团委）*
  - 对口部门（必填，多选下拉）*
  - 填报日期（自动记录）
  - 关键词标签（必填，多选标签）*
  - 备注（选填，文本）

- **对口部门选项**：
  - 本单位：综合办、人力、财务、党建部、纪委办、工会、管控部、团委
  - 省分公司：综合办（党委办）、人力、财务、党建、纪检、质服、运管、工会、团委

- **关键词标签系统**：
  - **管理部门可增删修订标签**：重点工作、大监督、专项工作、安全生产、质量管控等
  - **下属单位只能勾选**：不可增删修订标签
  - **标签分类**：工作类型、部门归属、时间周期、重要程度等

**业务逻辑规则**：
- 带星号(*)字段为必填项，必须完成后才能保存
- 序号字段仅主管账号可编辑修改
- 填报人只能编辑本部门/本人负责的事项
- 主管有权限修订其他部门/单位管理员的归属关系
- 支持草稿保存功能
- 每年新建空白台账，前一年度数据存入历史台账

#### 5.1.2 智能筛选与导出模块
**功能描述**：根据标签和条件筛选台账内容并导出

**详细规格**：
- **筛选条件**：
  - 按标签筛选（支持多标签组合）
  - 按时间范围筛选
  - 按责任单位筛选
  - 按填报状态筛选

- **导出功能**：
  - 支持Excel格式导出

  - 自定义导出模板

**业务逻辑规则**：
- 管理部门只能查看和导出有权限的内容
- 导出记录自动记录操作日志
- 支持批量导出多个筛选结果

#### 5.1.3 权限管理模块
**功能描述**：基于实际组织架构的细粒度权限控制

**详细规格**：
- **权限类型**：
  - **序号编辑权限**：仅主管账号可编辑修改第一列序号
  - **录入权限**：只能编辑本部门内容
  - **查看权限**：可查看相关标签内容
  - **批注权限**：管理部门可对他人内容添加批注，但不能修改原内容
  - **导出权限**：可导出相关内容
  - **标签管理权限**：管理部门可增删修订关键词标签
  - **归属修订权限**：主管可修订其他部门/单位管理员的归属关系
  - **历史台账权限**：查看和管理历史年度台账

- **权限矩阵**：
  - **主管用户**：全部权限（序号编辑、归属修订、系统配置等）
  - **管理部门用户**：查看权限（相关标签）+ 批注权限 + 导出权限 + 标签管理权限
  - **基层填报用户**：录入权限（本部门）+ 查看权限（本部门）+ 标签勾选权限
  - **系统管理员**：技术管理权限（用户管理、系统维护等）

- **特殊权限规则**：
  - 管理部门可以在批注栏给其他部门写批注，但不能修改其他栏目内容
  - 下属单位只能在已有标签中勾选，不可增删修订标签
  - 每年台账数据自动归档，普通用户无法修改历史数据

#### 5.1.4 自动提醒模块
**功能描述**：基于时间规则的自动提醒系统

**详细规格**：
- **提醒规则配置**：
  - 支持按工作类型设置提醒周期
  - 支持自定义提醒时间（提前天数）
  - 支持多级提醒（首次提醒、催办提醒）

- **提醒方式**：
  - 系统内消息提醒


**业务逻辑规则**：
- 提醒规则由管理部门设置
- 提醒对象为相关责任单位/人
- 提醒记录可追溯和统计

#### 5.1.5 批注与协作模块
**功能描述**：支持管理部门对台账内容进行批注

**详细规格**：
- **批注功能**：
  - 针对具体事项添加批注
  - 批注支持富文本格式
  - 批注历史记录可追溯
  - 批注状态管理（待处理/已处理）

- **协作功能**：
  - 批注消息通知
  - 批注回复功能
  - 批注状态跟踪

#### 5.1.6 年度台账管理模块
**功能描述**：管理年度台账的创建、归档和历史数据查询

**详细规格**：
- **年度台账创建**：
  - 每年自动或手动创建新的空白台账
  - 继承上一年度的基础配置（部门设置、标签体系等）
  - 序号重新开始编排

- **历史台账管理**：
  - 前一年度数据自动存入历史台账
  - 历史数据只读，不可修改
  - 支持历史数据查询和导出
  - 历史数据按年度分类存储

- **数据迁移功能**：
  - 年度切换时的数据平滑迁移
  - 配置信息的继承和更新
  - 用户权限的年度更新

**业务逻辑规则**：
- 历史台账数据完全只读，任何用户都不能修改
- 年度切换需要主管权限确认
- 历史数据保留期限可配置（建议至少保留5年）
- 支持跨年度数据对比和分析

#### 5.1.7 界面美化模块
**功能描述**：提供美观、专业的用户界面

**详细规格**：
- **表头设计**：
  - 专业的企业级表头样式
  - 清晰的字段标识和必填项标记
  - 响应式设计，适配不同屏幕尺寸

- **交互优化**：
  - 直观的操作按钮和图标
  - 友好的错误提示和操作引导
  - 快捷键支持，提升操作效率

- **视觉设计**：
  - 统一的色彩方案和字体规范
  - 清晰的数据层次和分组显示
  - 适合长时间使用的护眼配色

#### 5.1.8 部门管理模块
**功能描述**：主管可从后台灵活管理部门名称和组织架构

**详细规格**：
- **部门信息管理**：
  - 填报单位名称的增删改查
  - 对口部门名称的增删改查
  - 部门层级关系设置
  - 部门状态管理（启用/停用）

- **组织架构调整**：
  - 支持部门合并、拆分、重命名
  - 历史数据中的部门名称自动更新或保留历史记录
  - 部门变更影响分析和提醒

- **数据一致性保障**：
  - 部门名称变更时，相关台账数据自动更新
  - 提供数据迁移确认机制
  - 变更日志记录，便于追溯

**业务逻辑规则**：
- 仅主管账号可进行部门管理操作
- 部门删除前需确认无关联数据或提供数据迁移方案
- 部门变更需要确认影响范围并记录操作日志
- 支持批量导入部门信息（Excel格式）

**特殊考虑**：
- 企业改革期间的部门频繁变更支持
- 历史台账中的部门名称处理策略（保留历史 vs 统一更新）
- 部门变更对用户权限的自动调整

---

## 6. 范围定义

### 6.1 包含功能 (In Scope)
✅ 统一台账录入和管理（基于现有台账格式）
✅ 基于关键词标签的智能筛选
✅ 多格式导出功能（Excel、Word、PDF）
✅ 细粒度权限控制（主管、管理部门、基层用户）
✅ 自动提醒系统
✅ 批注和协作功能（管理部门专用批注栏）
✅ 年度台账管理和历史数据归档
✅ 用户管理和系统配置
✅ 表头美化和界面优化
✅ 序号可编辑功能（主管专用）
✅ 标签管理系统（管理部门可增删修订，基层只能勾选）
✅ 部门管理系统（主管可后台修改部门名称和组织架构）

### 6.2 排除功能 (Out of Scope)
❌ 复杂的工作流审批
❌ 数据分析和图表功能（用户下载Excel后自行分析）
❌ 饼状图、柱状图等数据可视化
❌ 移动端APP开发（第一版）
❌ 与其他业务系统的深度集成
❌ 实时协作编辑功能
❌ 文件附件管理（第一版）
❌ 复杂的统计报表和BI功能

---

## 7. 依赖与风险

### 7.1 内部依赖项
- **技术依赖**：需要确定技术栈和开发框架
- **数据依赖**：需要现有台账数据的梳理和迁移方案
- **人员依赖**：需要各部门配合进行需求确认和测试

### 7.2 外部依赖项
- **基础设施**：服务器和网络环境
- **第三方服务**：邮件服务、消息推送服务（可选）
- **合规要求**：数据安全和隐私保护要求

### 7.3 潜在风险
| 风险类型 | 风险描述 | 影响程度 | 缓解措施 |
|----------|----------|----------|----------|
| 技术风险 | 数据迁移复杂度高 | 中 | 制定详细迁移方案，分步实施 |
| 业务风险 | 用户接受度不高 | 高 | 充分的用户培训和试点推广 |
| 时间风险 | 开发周期可能延长 | 中 | 采用敏捷开发，分阶段交付 |
| 数据风险 | 数据安全和备份 | 高 | 建立完善的数据备份和恢复机制 |

---

## 8. 发布初步计划

### 8.1 开发阶段规划
**第一阶段（MVP版本）**：
- 基础台账录入功能
- 简单标签筛选
- 基础权限管理
- Excel导出功能
- 预计开发周期：6-8周

**第二阶段（完整版本）**：
- 自动提醒系统
- 批注协作功能
- 高级筛选和导出优化
- 年度台账管理
- 界面美化完善
- 预计开发周期：4-5周

### 8.2 上线策略
- **灰度发布**：选择2-3个部门进行试点
- **全量发布**：试点成功后全公司推广
- **数据跟踪**：监控系统使用情况和用户反馈

### 8.3 成本估算建议
基于功能复杂度和开发周期，移除数据分析功能后的估算：
- **开发成本**：18-25万元（包含前后端开发、测试、部署）
- **基础设施成本**：2-5万元/年（服务器、域名、SSL证书等）
- **维护成本**：3-5万元/年（系统维护、功能优化）

**总体预算建议：23-35万元（首年）**
**节省成本**：移除数据分析功能节省2-5万元开发成本

---

## 9. 技术架构建议

### 9.1 推荐技术栈
- **前端**：React + Ant Design（企业级UI组件库）
- **后端**：Node.js + Express 或 Python + Django
- **数据库**：MySQL + Redis（缓存）
- **部署**：Docker + Nginx

### 9.2 系统架构特点
- **模块化设计**：便于功能扩展和维护
- **权限细粒度控制**：基于角色的访问控制(RBAC)
- **数据安全**：数据加密存储，操作日志记录
- **高可用性**：支持负载均衡和故障恢复

---

## 10. 界面设计要点

### 10.1 设计原则
- **简洁易用**：界面清晰，操作直观
- **响应式设计**：适配不同屏幕尺寸
- **一致性**：统一的视觉风格和交互模式
- **可访问性**：支持键盘操作，符合无障碍标准

### 10.2 关键页面
1. **台账录入页面**：
   - 表单式录入，支持富文本编辑
   - 必填项明确标记（星号标识）
   - 下拉选择框（责任单位、对口部门）
   - 多选标签系统（关键词）
   - 序号字段特殊权限控制

2. **台账列表页面**：
   - 表格展示，美观的表头设计
   - 支持多维度筛选和排序
   - 批注栏单独显示
   - 年度切换功能

3. **筛选导出页面**：
   - 基于关键词标签的智能筛选
   - 按对口部门筛选
   - 按责任单位筛选
   - 预览和多格式导出

4. **权限管理页面**：
   - 用户角色管理（主管、管理部门、基层用户）
   - 部门归属设置
   - 标签管理权限配置

5. **提醒设置页面**：
   - 提醒规则配置
   - 多渠道提醒设置

6. **年度管理页面**：
   - 年度台账创建
   - 历史台账查询
   - 数据归档管理

7. **部门管理页面**（主管专用）：
   - 填报单位管理
   - 对口部门管理
   - 组织架构调整
   - 部门变更历史

### 10.3 组织架构配置
**填报单位设置**：
- 综合办、人力、财务、党建部、纪委办、工会、管控部
- 生产中心A、生产中心B、运输中心、运维中心、团委

**对口部门设置**：
- 本单位：综合办、人力、财务、党建部、纪委办、工会、管控部、团委
- 省分公司：综合办（党委办）、人力、财务、党建、纪检、质服、运管、工会、团委

---

## 11. 下一步行动计划

### 11.1 立即行动项
1. **需求确认会议**：与各部门确认具体台账格式和字段需求
2. **技术选型确认**：确定最终的开发技术栈
3. **预算审批**：提交详细预算方案供决策

### 11.2 后续计划
1. **原型设计**：制作交互原型进行用户验证（1-2周）
2. **技术架构设计**：详细的系统架构和数据库设计（1周）
3. **开发计划制定**：详细的开发时间表和里程碑（1周）
4. **试点部门选择**：确定试点部门并制定试点方案（1周）

---

## 12. 附录

### 12.1 术语表
- **台账**：用于记录和跟踪特定工作事项的表格化文档
- **标签系统**：用于分类和筛选数据的标记体系
- **权限矩阵**：定义不同角色用户权限的对照表
- **MVP**：最小可行产品(Minimum Viable Product)

### 12.2 参考资料
- 现有台账模板样例
- 各部门台账使用情况调研
- 类似系统的最佳实践案例

---

**文档状态：已完成**
**文档路径：/docs/prd/PRD_台账管理系统_v1.0.md**
**下一步：等待老板确认需求，准备进入技术架构设计阶段**
