"""
API依赖注入
"""
from typing import Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from ..core.database import SessionLocal
from ..core.auth import get_user_from_token, check_user_permission
from ..core.security import get_client_ip, log_security_event, SecurityEventType
from ..models.user import User


# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


def get_db() -> Generator:
    """
    获取数据库会话的依赖注入函数
    """
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    获取当前认证用户
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user = get_user_from_token(db, credentials.credentials)
    if not user:
        # 记录无效令牌事件
        log_security_event(
            SecurityEventType.INVALID_TOKEN,
            None,
            {"token_prefix": credentials.credentials[:10] + "..."},
            get_client_ip(request)
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户账号已被禁用",
        )

    return user


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前活跃用户（已验证且启用）
    """
    return current_user


def require_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    要求管理员权限
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


def require_manager_or_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    要求管理部门或管理员权限
    """
    if not (current_user.is_admin or current_user.is_manager):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理部门或管理员权限"
        )
    return current_user


def require_permission(permission: str):
    """
    要求特定权限的依赖注入工厂
    """
    def permission_dependency(
        request: Request,
        current_user: User = Depends(get_current_user)
    ) -> User:
        if not check_user_permission(current_user, permission):
            # 记录权限拒绝事件
            log_security_event(
                SecurityEventType.PERMISSION_DENIED,
                current_user.id,
                {"required_permission": permission, "user_role": current_user.role},
                get_client_ip(request)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少必要权限: {permission}"
            )
        return current_user

    return permission_dependency


def require_tag_management():
    """
    要求标签管理权限的依赖注入
    """
    def tag_management_dependency(
        request: Request,
        current_user: User = Depends(get_current_user)
    ) -> User:
        if not current_user.can_manage_tags:
            # 记录权限拒绝事件
            log_security_event(
                SecurityEventType.PERMISSION_DENIED,
                current_user.id,
                {"required_permission": "manage_tags", "user_role": current_user.role},
                get_client_ip(request)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要标签管理权限"
            )
        return current_user

    return tag_management_dependency


def require_user_management():
    """
    要求用户管理权限的依赖注入
    """
    def user_management_dependency(
        request: Request,
        current_user: User = Depends(get_current_user)
    ) -> User:
        if not current_user.can_manage_users:
            # 记录权限拒绝事件
            log_security_event(
                SecurityEventType.PERMISSION_DENIED,
                current_user.id,
                {"required_permission": "manage_users", "user_role": current_user.role},
                get_client_ip(request)
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要用户管理权限"
            )
        return current_user

    return user_management_dependency


def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取可选的当前用户（用于不强制登录的接口）
    """
    if not credentials:
        return None

    user = get_user_from_token(db, credentials.credentials)
    if not user or not user.is_active:
        return None

    return user
